#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频同步播放器 - 专门解决音频同步问题
使用精确的时间基准同步和优化的音频处理策略
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk

# 🎵 音频同步优化的ffmpeg配置
print("🔧 设置音频同步优化环境...")

# 音频同步优化配置
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '100'

# 关键：启用适度的多线程以提升性能，但保持同步
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;2|thread_type;1|thread_count;2|buffer_size;65536'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '2'

# 启用硬件加速但保持音频同步
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '0'

# 音频同步关键配置
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '0'  # 启用异步，提升性能
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '0'     # 不强制同步，让程序控制

# 优化seek性能，减少音频延迟
os.environ['OPENCV_FFMPEG_SEEK_BUFFER_SIZE'] = '8192'
os.environ['OPENCV_FFMPEG_DISABLE_SEEK_BUFFER'] = '0'

# 线程安全配置
os.environ['OPENCV_FFMPEG_THREAD_SAFE'] = '1'
os.environ['FFMPEG_THREAD_SAFE'] = '1'

print("✅ 音频同步优化环境设置完成")

# 检查依赖
try:
    import pygame
    pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=1024)
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用")
except Exception as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")

class AudioSyncPlayer:
    def __init__(self, root, video_path=None):
        self.root = root
        self.root.title("音频同步播放器 - 精确音视频同步")
        self.root.geometry("1000x700")
        
        # 播放状态
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.duration = 0
        
        # 视频相关
        self.cap = None
        self.video_path = None
        self.video_thread = None
        
        # 精确音频同步
        self.audio_file = None
        self.audio_clip = None
        self.audio_ready = False  # 音频是否准备就绪
        self.master_clock = None  # 主时钟
        self.video_start_time = None  # 视频开始时间
        self.audio_start_time = None  # 音频开始时间
        self.sync_offset = 0  # 同步偏移
        self.audio_playing = False
        
        # 同步控制
        self.sync_lock = threading.Lock()
        self.frame_time_tolerance = 0.05  # 50ms容差
        self.sync_correction_threshold = 0.1  # 100ms同步纠正阈值
        
        # 性能优化
        self.frame_cache = {}
        self.cache_size = 60
        self.last_sync_check = 0
        self.sync_check_interval = 0.1  # 100ms检查一次同步
        
        # seek优化
        self.seek_lock = threading.Lock()
        self.last_seek_time = 0
        self.seek_debounce = 0.2
        self.seek_in_progress = False
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示区域
        video_container = ttk.Frame(main_frame)
        video_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = tk.Label(video_container, bg='black', fg='white',
                                   text="音频同步播放器\n\n特点:\n• 精确的音视频同步\n• 主时钟同步算法\n• 智能同步纠正\n• 优化的音频处理\n\n点击'打开视频'开始")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="播放控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮行
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="📁 打开视频", command=self.open_video).pack(side=tk.LEFT, padx=(0, 5))
        
        self.play_btn = ttk.Button(btn_frame, text="▶️ 播放", command=self.toggle_play)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(btn_frame, text="⏹️ 停止", command=self.stop).pack(side=tk.LEFT, padx=(0, 5))
        
        # 音频控制
        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="🔊 音频", variable=self.audio_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 同步信息显示
        self.sync_info_var = tk.StringVar(value="同步状态: 未开始")
        ttk.Label(btn_frame, textvariable=self.sync_info_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 进度控制
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_seek)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))
        
        self.time_label = ttk.Label(progress_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 同步调试面板
        debug_frame = ttk.LabelFrame(main_frame, text="同步调试")
        debug_frame.pack(fill=tk.X, pady=(0, 10))
        
        debug_btn_frame = ttk.Frame(debug_frame)
        debug_btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(debug_btn_frame, text="🔄 重新同步", command=self.force_resync).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(debug_btn_frame, text="📊 同步状态", command=self.show_sync_status).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(debug_btn_frame, text="🎵 测试音频", command=self.test_audio).pack(side=tk.LEFT, padx=(0, 5))
        
        # 同步偏移调整
        ttk.Label(debug_btn_frame, text="同步偏移(ms):").pack(side=tk.LEFT, padx=(20, 5))
        self.offset_var = tk.DoubleVar(value=0)
        offset_scale = ttk.Scale(debug_btn_frame, from_=-1000, to=1000,
                               orient=tk.HORIZONTAL, variable=self.offset_var,
                               command=self.adjust_sync_offset)
        offset_scale.pack(side=tk.LEFT, padx=(0, 10))
        
        self.offset_label = ttk.Label(debug_btn_frame, text="0ms")
        self.offset_label.pack(side=tk.LEFT)
        
        # 信息面板
        info_frame = ttk.LabelFrame(main_frame, text="播放信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_label = ttk.Label(info_frame, text="未加载视频")
        self.info_label.pack(padx=5, pady=5)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪 - 音频同步播放器", relief=tk.SUNKEN)
        self.status_label.pack(fill=tk.X)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def open_video(self):
        """打开视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("常见视频", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv"),
                ("MP4文件", "*.mp4"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频文件 - 音频同步优化版本"""
        try:
            print(f"📁 加载视频: {video_path}")
            self.status_label.configure(text="正在加载视频...")
            
            # 停止当前播放
            self.stop()
            
            # 清理缓存
            self.frame_cache.clear()
            
            # 使用音频同步优化的ffmpeg后端
            print("🔧 使用音频同步优化ffmpeg后端...")
            self.cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
            
            if not self.cap.isOpened():
                print("⚠️ ffmpeg后端失败，尝试默认后端...")
                self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 验证参数
            if self.total_frames <= 0:
                self.total_frames = 10000
            if self.fps <= 0 or self.fps > 120:
                self.fps = 30
            
            self.duration = self.total_frames / self.fps
            
            print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps, {width}x{height}")
            
            # 设置进度条
            self.progress_bar.configure(to=self.total_frames-1)
            self.current_frame = 0
            
            # 显示第一帧
            self.show_current_frame()
            
            # 加载音频用于同步
            self.load_audio_for_sync(video_path)
            
            # 更新界面
            self.video_path = video_path
            filename = os.path.basename(video_path)
            self.info_label.configure(text=f"文件: {filename}\n分辨率: {width}x{height}\n帧率: {self.fps}fps\n时长: {self.format_time(self.duration)}")
            self.status_label.configure(text=f"已加载: {filename}")
            
        except Exception as e:
            error_msg = f"加载视频失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            self.status_label.configure(text="加载失败")
    
    def load_audio_for_sync(self, video_path):
        """加载音频用于同步"""
        if not self.audio_var.get() or not MOVIEPY_AVAILABLE:
            print("🔇 音频已禁用")
            return
        
        def load_in_thread():
            try:
                print("🔊 加载音频用于同步...")
                self.root.after(0, lambda: self.status_label.configure(text="正在加载音频..."))
                
                # 使用moviepy加载音频
                video_clip = VideoFileClip(video_path)
                
                if video_clip.audio is not None:
                    self.audio_clip = video_clip.audio
                    
                    # 导出音频文件用于pygame播放
                    import tempfile
                    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                        self.audio_file = temp_file.name
                    
                    self.audio_clip.write_audiofile(
                        self.audio_file,
                        verbose=False,
                        logger=None,
                        codec='pcm_s16le',
                        ffmpeg_params=['-ar', '44100', '-ac', '2']
                    )
                    
                    print(f"✅ 音频加载完成: {self.audio_clip.duration:.2f}秒")
                    self.audio_ready = True
                    self.root.after(0, lambda: self.status_label.configure(text="音频加载完成"))
                else:
                    print("⚠️ 视频文件没有音频轨道")
                    self.audio_clip = None
                    self.audio_file = None
                    
            except Exception as e:
                print(f"❌ 音频加载失败: {e}")
                self.audio_clip = None
                self.audio_file = None
        
        threading.Thread(target=load_in_thread, daemon=True).start()

    def show_current_frame(self):
        """显示当前帧"""
        if not self.cap:
            return

        try:
            # 检查缓存
            if self.current_frame in self.frame_cache:
                frame = self.frame_cache[self.current_frame]
            else:
                # 设置帧位置并读取
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)
                ret, frame = self.cap.read()
                if not ret:
                    return

                # 缓存管理
                if len(self.frame_cache) >= self.cache_size:
                    oldest_frame = min(self.frame_cache.keys())
                    del self.frame_cache[oldest_frame]

                self.frame_cache[self.current_frame] = frame.copy()

            # 转换并显示
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 获取显示区域大小
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()

            if label_width > 1 and label_height > 1:
                h, w = frame_rgb.shape[:2]
                scale = min(label_width/w, label_height/h)
                new_w, new_h = int(w*scale), int(h*scale)

                if new_w > 0 and new_h > 0:
                    frame_resized = cv2.resize(frame_rgb, (new_w, new_h))
                    pil_image = Image.fromarray(frame_resized)
                    photo = ImageTk.PhotoImage(pil_image)

                    self.video_label.configure(image=photo, text="")
                    self.video_label.image = photo

            # 更新进度和时间
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(self.duration)}")

        except Exception as e:
            print(f"❌ 显示帧失败: {e}")

    def format_time(self, seconds):
        """格式化时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"

    def toggle_play(self):
        """播放/暂停切换"""
        if not self.cap:
            messagebox.showwarning("警告", "请先加载视频文件")
            return

        if self.is_playing:
            self.pause()
        else:
            self.play()

    def play(self):
        """开始播放 - 精确音频同步版本"""
        if not self.cap:
            return

        self.is_playing = True
        self.play_btn.configure(text="⏸️ 暂停")

        # 初始化主时钟
        with self.sync_lock:
            self.master_clock = time.time()
            self.video_start_time = self.master_clock
            current_time_offset = self.current_frame / self.fps

            # 调整主时钟以考虑当前播放位置
            self.master_clock -= current_time_offset

        # 启动音频播放
        self.start_audio_sync()

        # 启动视频播放线程
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.sync_playback_loop, daemon=True)
            self.video_thread.start()

        self.status_label.configure(text="正在播放...")
        print("▶️ 开始同步播放")

    def pause(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 暂停音频
        self.pause_audio()

        self.status_label.configure(text="已暂停")
        print("⏸️ 暂停播放")

    def stop(self):
        """停止播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 停止音频
        self.stop_audio()

        # 重置位置
        if self.cap:
            self.current_frame = 0
            self.show_current_frame()

        # 重置同步状态
        with self.sync_lock:
            self.master_clock = None
            self.video_start_time = None
            self.audio_start_time = None

        self.sync_info_var.set("同步状态: 已停止")
        self.status_label.configure(text="已停止")
        print("⏹️ 停止播放")

    def sync_playback_loop(self):
        """精确同步播放循环"""
        print("🎬 启动精确同步播放循环")

        while self.is_playing and self.cap:
            try:
                current_time = time.time()

                # 计算基于主时钟的目标帧
                with self.sync_lock:
                    if self.master_clock:
                        elapsed_time = current_time - self.master_clock
                        # 应用同步偏移
                        adjusted_time = elapsed_time + (self.sync_offset / 1000.0)
                        target_frame = int(adjusted_time * self.fps)

                        # 精确同步控制
                        frame_diff = target_frame - self.current_frame

                        # 检查同步状态
                        if abs(frame_diff) > self.sync_correction_threshold * self.fps:
                            # 需要同步纠正
                            print(f"🔄 同步纠正: 当前帧{self.current_frame}, 目标帧{target_frame}, 差距{frame_diff}")
                            self.current_frame = target_frame
                            self.frame_cache.clear()  # 清理缓存
                        elif frame_diff > 0:
                            # 渐进式追赶
                            self.current_frame = min(target_frame, self.current_frame + min(frame_diff, 2))

                        # 检查播放结束
                        if self.current_frame >= self.total_frames:
                            self.root.after(0, self.stop)
                            break

                        # 更新同步信息
                        if current_time - self.last_sync_check > self.sync_check_interval:
                            self.last_sync_check = current_time
                            sync_error = frame_diff / self.fps * 1000  # 转换为毫秒
                            self.root.after(0, lambda: self.sync_info_var.set(f"同步状态: {sync_error:.1f}ms"))

                # 更新显示
                self.root.after(0, self.show_current_frame)

                # 精确的时间控制
                frame_time = 1.0 / self.fps
                time.sleep(frame_time * 0.9)

            except Exception as e:
                print(f"❌ 同步播放循环错误: {e}")
                break

        print("🎬 同步播放循环结束")

    def start_audio_sync(self):
        """启动音频同步播放"""
        if not self.audio_var.get() or not PYGAME_AVAILABLE:
            return

        # 检查音频是否准备就绪
        if not self.audio_ready or not self.audio_file or not os.path.exists(self.audio_file):
            print("⚠️ 音频未准备就绪，跳过音频播放")
            return

        try:
            # 计算音频开始位置
            current_time_offset = self.current_frame / self.fps

            print(f"🔊 启动音频同步播放，偏移: {current_time_offset:.2f}秒")

            # 加载并播放音频
            pygame.mixer.music.load(self.audio_file)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()

            # 记录音频开始时间
            with self.sync_lock:
                self.audio_start_time = time.time() - current_time_offset

            self.audio_playing = True
            print("✅ 音频同步播放开始")

        except Exception as e:
            print(f"❌ 音频同步播放失败: {e}")
            self.audio_playing = False

    def pause_audio(self):
        """暂停音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.pause()
            self.audio_playing = False
            print("🔊 音频已暂停")
        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.stop()
            self.audio_playing = False
            self.audio_start_time = None
            print("🔊 音频已停止")
        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def on_seek(self, value):
        """进度条拖动处理 - 精确同步版本"""
        if not self.cap:
            return

        current_time = time.time()

        # 防抖处理
        if current_time - self.last_seek_time < self.seek_debounce:
            return

        self.last_seek_time = current_time

        with self.seek_lock:
            if self.seek_in_progress:
                return
            self.seek_in_progress = True

        try:
            frame_num = int(float(value))
            seek_time = frame_num / self.fps

            print(f"🎬 精确同步跳转: 帧{frame_num} ({seek_time:.2f}秒)")

            # 清理缓存
            self.frame_cache.clear()

            with self.sync_lock:
                self.current_frame = frame_num

                if self.is_playing:
                    # 重新同步主时钟
                    self.master_clock = time.time() - seek_time

                    # 重新启动音频
                    self.stop_audio()
                    self.start_audio_sync()

            # 显示当前帧
            if not self.is_playing:
                self.show_current_frame()

        except Exception as e:
            print(f"❌ 精确同步跳转失败: {e}")
        finally:
            self.seek_in_progress = False

    def force_resync(self):
        """强制重新同步"""
        if self.is_playing:
            print("🔄 强制重新同步")
            with self.sync_lock:
                current_time_offset = self.current_frame / self.fps
                self.master_clock = time.time() - current_time_offset

            # 重新启动音频
            self.stop_audio()
            self.start_audio_sync()

            self.sync_info_var.set("同步状态: 已重新同步")

    def show_sync_status(self):
        """显示同步状态"""
        if not self.is_playing:
            messagebox.showinfo("同步状态", "播放器未运行")
            return

        current_time = time.time()
        with self.sync_lock:
            if self.master_clock and self.audio_start_time:
                video_time = current_time - self.master_clock
                audio_time = current_time - self.audio_start_time
                sync_diff = (video_time - audio_time) * 1000  # 转换为毫秒

                status_msg = f"视频时间: {video_time:.3f}s\n"
                status_msg += f"音频时间: {audio_time:.3f}s\n"
                status_msg += f"同步差异: {sync_diff:.1f}ms\n"
                status_msg += f"当前帧: {self.current_frame}\n"
                status_msg += f"帧率: {self.fps}fps"

                messagebox.showinfo("同步状态", status_msg)
            else:
                messagebox.showinfo("同步状态", "同步信息不可用")

    def test_audio(self):
        """测试音频播放"""
        if not self.audio_file:
            messagebox.showwarning("警告", "没有可用的音频文件")
            return

        try:
            pygame.mixer.music.load(self.audio_file)
            pygame.mixer.music.play()
            messagebox.showinfo("测试音频", "音频测试播放开始")
        except Exception as e:
            messagebox.showerror("错误", f"音频测试失败: {e}")

    def adjust_sync_offset(self, value):
        """调整同步偏移"""
        self.sync_offset = float(value)
        self.offset_label.configure(text=f"{self.sync_offset:.0f}ms")

        if self.is_playing:
            # 实时调整同步
            with self.sync_lock:
                current_time_offset = self.current_frame / self.fps
                adjusted_offset = current_time_offset + (self.sync_offset / 1000.0)
                self.master_clock = time.time() - adjusted_offset

    def on_close(self):
        """程序关闭处理"""
        print("🧹 清理资源...")

        # 停止播放
        self.stop()

        # 释放视频资源
        if self.cap:
            self.cap.release()

        # 清理音频资源
        if self.audio_clip:
            self.audio_clip.close()

        if self.audio_file and os.path.exists(self.audio_file):
            try:
                os.unlink(self.audio_file)
                print("🧹 临时音频文件已清理")
            except Exception as e:
                print(f"⚠️ 清理音频文件失败: {e}")

        # 退出pygame
        if PYGAME_AVAILABLE:
            pygame.mixer.quit()

        print("✅ 资源清理完成")
        self.root.destroy()


def main():
    """主函数"""
    print("=" * 60)
    print("🎬 音频同步播放器 - 精确音视频同步")
    print("=" * 60)
    print("同步特点:")
    print("• 主时钟同步算法 - 精确的时间基准")
    print("• 智能同步纠正 - 自动检测和纠正同步偏差")
    print("• 实时同步监控 - 显示同步状态和偏差")
    print("• 手动同步调整 - 支持手动微调同步偏移")
    print("• 优化音频处理 - 减少音频延迟")
    print("=" * 60)

    root = tk.Tk()

    # 检查命令行参数
    video_path = None
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"⚠️ 文件不存在: {video_path}")
            video_path = None

    app = AudioSyncPlayer(root, video_path)
    root.mainloop()


if __name__ == "__main__":
    main()
