#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频同步测试工具
专门用于测试和诊断音频同步问题
"""

import os
import time
import tempfile
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading

# 检查依赖
try:
    import pygame
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用")
except:
    PYGAME_AVAILABLE = False
    print("❌ pygame 不可用")

try:
    from moviepy.editor import VideoFileClip, AudioFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except:
    MOVIEPY_AVAILABLE = False
    print("❌ moviepy 不可用")

class AudioSyncTester:
    def __init__(self, root):
        self.root = root
        self.root.title("音频同步测试工具")
        self.root.geometry("600x400")
        
        self.video_path = None
        self.audio_path = None
        self.video_duration = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        # 文件选择
        file_frame = ttk.Frame(self.root)
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(file_frame, text="选择视频文件", 
                  command=self.select_video).pack(side=tk.LEFT, padx=(0, 10))
        
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT)
        
        # 测试控制
        test_frame = ttk.LabelFrame(self.root, text="音频同步测试")
        test_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 跳转位置输入
        pos_frame = ttk.Frame(test_frame)
        pos_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(pos_frame, text="跳转到(秒):").pack(side=tk.LEFT)
        self.position_var = tk.DoubleVar(value=10.0)
        ttk.Entry(pos_frame, textvariable=self.position_var, width=10).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(pos_frame, text="测试音频跳转", 
                  command=self.test_audio_seek).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(pos_frame, text="播放原始音频", 
                  command=self.play_original_audio).pack(side=tk.LEFT)
        
        # 结果显示
        result_frame = ttk.LabelFrame(self.root, text="测试结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.result_text = tk.Text(result_frame, height=15)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_message)
        self.result_text.see(tk.END)
        self.root.update()
        print(message)
        
    def select_video(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[("视频文件", "*.mp4 *.avi *.mov"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.video_path = file_path
            self.file_label.configure(text=os.path.basename(file_path))
            self.log(f"✅ 选择视频: {file_path}")
            
            # 提取音频
            self.extract_audio()
            
    def extract_audio(self):
        """提取音频文件"""
        if not self.video_path or not MOVIEPY_AVAILABLE:
            return
            
        def extract():
            try:
                self.log("🔊 开始提取音频...")
                
                # 创建临时音频文件
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    self.audio_path = temp_file.name
                
                # 使用moviepy提取音频
                video_clip = VideoFileClip(self.video_path)
                self.video_duration = video_clip.duration
                
                if video_clip.audio is not None:
                    audio_clip = video_clip.audio
                    audio_clip.write_audiofile(
                        self.audio_path,
                        verbose=False,
                        logger=None,
                        codec='pcm_s16le',
                        ffmpeg_params=['-ar', '44100', '-ac', '2']
                    )
                    
                    file_size = os.path.getsize(self.audio_path)
                    self.log(f"✅ 音频提取完成: {file_size} 字节")
                    self.log(f"📹 视频时长: {self.video_duration:.2f} 秒")
                    
                    audio_clip.close()
                else:
                    self.log("❌ 视频没有音频轨道")
                    
                video_clip.close()
                
            except Exception as e:
                self.log(f"❌ 音频提取失败: {e}")
                
        threading.Thread(target=extract, daemon=True).start()
        
    def test_audio_seek(self):
        """测试音频跳转"""
        if not self.audio_path or not PYGAME_AVAILABLE:
            self.log("❌ 音频文件或pygame不可用")
            return
            
        position = self.position_var.get()
        if position >= self.video_duration:
            self.log(f"❌ 跳转位置超出视频长度: {position:.2f}s >= {self.video_duration:.2f}s")
            return
            
        self.log(f"🎯 测试音频跳转到: {position:.2f} 秒")
        
        def test_seek():
            try:
                start_time = time.time()
                
                # 创建音频片段
                self.log("🔧 创建音频片段...")
                audio_clip = AudioFileClip(self.audio_path)
                audio_segment = audio_clip.subclip(position)
                
                # 创建临时文件
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    temp_path = temp_file.name
                
                # 导出音频片段
                audio_segment.write_audiofile(
                    temp_path,
                    verbose=False,
                    logger=None,
                    codec='pcm_s16le',
                    ffmpeg_params=['-ar', '44100', '-ac', '2']
                )
                
                creation_time = time.time() - start_time
                self.log(f"✅ 音频片段创建完成，耗时: {creation_time:.2f} 秒")
                
                # 播放音频片段
                def play_segment():
                    try:
                        pygame.mixer.music.stop()
                        pygame.mixer.music.load(temp_path)
                        pygame.mixer.music.set_volume(1.0)
                        pygame.mixer.music.play()
                        
                        self.log(f"🔊 开始播放从 {position:.2f} 秒开始的音频片段")
                        
                        # 清理临时文件（延迟删除）
                        def cleanup():
                            time.sleep(5)  # 等待播放
                            try:
                                os.unlink(temp_path)
                            except:
                                pass
                        threading.Thread(target=cleanup, daemon=True).start()
                        
                    except Exception as e:
                        self.log(f"❌ 播放音频片段失败: {e}")
                        try:
                            os.unlink(temp_path)
                        except:
                            pass
                
                self.root.after(0, play_segment)
                
                audio_segment.close()
                audio_clip.close()
                
            except Exception as e:
                self.log(f"❌ 音频跳转测试失败: {e}")
                import traceback
                traceback.print_exc()
                
        threading.Thread(target=test_seek, daemon=True).start()
        
    def play_original_audio(self):
        """播放原始音频"""
        if not self.audio_path or not PYGAME_AVAILABLE:
            self.log("❌ 音频文件或pygame不可用")
            return
            
        try:
            pygame.mixer.music.stop()
            pygame.mixer.music.load(self.audio_path)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()
            
            self.log("🔊 开始播放原始音频（从头开始）")
            
        except Exception as e:
            self.log(f"❌ 播放原始音频失败: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = AudioSyncTester(root)
    root.mainloop()
