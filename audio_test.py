#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频播放测试脚本
用于快速诊断音频播放问题
"""

import os
import sys
import time

def test_winsound():
    """测试Windows内置音频"""
    try:
        import winsound
        print("🔔 测试Windows系统音频...")
        winsound.PlaySound("SystemAsterisk", winsound.SND_ALIAS)
        print("✅ Windows系统音频测试完成")
        return True
    except ImportError:
        print("❌ winsound不可用")
        return False
    except Exception as e:
        print(f"❌ Windows音频测试失败: {e}")
        return False

def test_pygame_basic():
    """测试pygame基础音频功能"""
    try:
        import pygame
        print("🎮 测试pygame基础功能...")
        
        # 初始化pygame
        pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=1024)
        pygame.mixer.init()
        
        print(f"✅ pygame初始化成功: {pygame.mixer.get_init()}")
        
        # 创建一个简单的测试音频（440Hz正弦波）
        import numpy as np
        
        # 生成1秒的440Hz正弦波
        sample_rate = 44100
        duration = 1.0
        frequency = 440
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        wave = np.sin(frequency * 2 * np.pi * t)
        
        # 转换为pygame可用的格式
        wave = (wave * 32767).astype(np.int16)
        stereo_wave = np.array([wave, wave]).T
        
        # 创建pygame Sound对象
        sound = pygame.sndarray.make_sound(stereo_wave)
        
        print("🔊 播放测试音频（440Hz正弦波）...")
        sound.play()
        time.sleep(1.5)  # 等待播放完成
        
        print("✅ pygame音频测试完成")
        return True
        
    except ImportError as e:
        print(f"❌ pygame不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ pygame音频测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pygame_music():
    """测试pygame音乐播放功能"""
    try:
        import pygame
        print("🎵 测试pygame音乐播放...")
        
        # 查找测试音频文件
        test_files = [
            "C:/Windows/Media/Windows Logon.wav",
            "C:/Windows/Media/chimes.wav",
            "C:/Windows/Media/ding.wav"
        ]
        
        test_file = None
        for file_path in test_files:
            if os.path.exists(file_path):
                test_file = file_path
                break
        
        if test_file:
            print(f"🔊 使用测试文件: {test_file}")
            pygame.mixer.music.load(test_file)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()
            
            # 等待播放
            time.sleep(2)
            
            print("✅ pygame音乐播放测试完成")
            return True
        else:
            print("⚠️ 没有找到系统音频文件进行测试")
            return False
            
    except Exception as e:
        print(f"❌ pygame音乐播放测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 开始音频系统诊断...")
    print("=" * 50)
    
    # 测试1: Windows系统音频
    print("\n📋 测试1: Windows系统音频")
    winsound_ok = test_winsound()
    
    # 测试2: pygame基础功能
    print("\n📋 测试2: pygame基础音频")
    pygame_basic_ok = test_pygame_basic()
    
    # 测试3: pygame音乐播放
    print("\n📋 测试3: pygame音乐播放")
    pygame_music_ok = test_pygame_music()
    
    # 总结
    print("\n" + "=" * 50)
    print("🔍 诊断结果总结:")
    print(f"   Windows系统音频: {'✅ 正常' if winsound_ok else '❌ 异常'}")
    print(f"   pygame基础音频: {'✅ 正常' if pygame_basic_ok else '❌ 异常'}")
    print(f"   pygame音乐播放: {'✅ 正常' if pygame_music_ok else '❌ 异常'}")
    
    if not any([winsound_ok, pygame_basic_ok, pygame_music_ok]):
        print("\n❌ 所有音频测试都失败了！")
        print("   可能的原因：")
        print("   1. 音频设备未连接或损坏")
        print("   2. 音频驱动程序问题")
        print("   3. 系统音频服务未启动")
        print("   4. 音量被完全静音")
    elif winsound_ok and not pygame_basic_ok:
        print("\n⚠️ 系统音频正常，但pygame有问题")
        print("   建议重新安装pygame: pip install --upgrade pygame")
    elif pygame_basic_ok and not pygame_music_ok:
        print("\n⚠️ pygame基础功能正常，但音乐播放有问题")
        print("   可能是音频文件格式兼容性问题")
    else:
        print("\n✅ 音频系统基本正常")
        print("   如果MP4播放器仍然无声，可能是：")
        print("   1. 音频文件提取有问题")
        print("   2. 音频同步逻辑有问题")
        print("   3. 音频文件格式不支持")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
