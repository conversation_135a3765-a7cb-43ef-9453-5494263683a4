#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最基础的测试 - 找出自动关闭的真正原因
"""

import sys
import time
import os

def test_basic_python():
    """测试基础Python功能"""
    print("=" * 50)
    print("基础Python测试")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")
    
    # 测试基本循环
    print("\n测试基本循环...")
    for i in range(5):
        print(f"循环 {i+1}/5")
        time.sleep(1)
    
    print("✅ 基础Python测试通过")
    return True

def test_tkinter_minimal():
    """测试最小Tkinter"""
    print("\n" + "=" * 50)
    print("最小Tkinter测试")
    print("=" * 50)
    
    try:
        import tkinter as tk
        print("✅ tkinter导入成功")
        
        # 创建最简单的窗口
        root = tk.Tk()
        root.title("基础测试窗口")
        root.geometry("300x200")
        
        # 添加标签
        label = tk.Label(root, text="这是一个测试窗口\n程序正在运行...", 
                        font=("Arial", 12))
        label.pack(expand=True)
        
        # 添加关闭按钮
        def close_window():
            print("用户点击关闭按钮")
            root.destroy()
        
        button = tk.Button(root, text="点击关闭", command=close_window)
        button.pack(pady=10)
        
        # 设置自动关闭（10秒后）
        def auto_close():
            print("10秒时间到，自动关闭窗口")
            root.destroy()
        
        root.after(10000, auto_close)  # 10秒后自动关闭
        
        print("启动Tkinter窗口（10秒后自动关闭）...")
        print("如果窗口立即消失，说明有环境问题")
        
        # 启动主循环
        root.mainloop()
        
        print("✅ Tkinter窗口正常关闭")
        return True
        
    except Exception as e:
        print(f"❌ Tkinter测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_opencv_basic():
    """测试基础OpenCV"""
    print("\n" + "=" * 50)
    print("基础OpenCV测试")
    print("=" * 50)
    
    try:
        import cv2
        print(f"✅ OpenCV导入成功，版本: {cv2.__version__}")
        
        # 测试创建VideoCapture（不打开文件）
        cap = cv2.VideoCapture()
        print("✅ VideoCapture对象创建成功")
        
        cap.release()
        print("✅ VideoCapture释放成功")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenCV测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combined():
    """测试组合功能"""
    print("\n" + "=" * 50)
    print("组合功能测试")
    print("=" * 50)
    
    try:
        import tkinter as tk
        import cv2
        
        print("创建组合测试窗口...")
        
        root = tk.Tk()
        root.title("组合测试 - 不会自动关闭")
        root.geometry("400x300")
        
        # 状态显示
        status_var = tk.StringVar(value="程序启动中...")
        status_label = tk.Label(root, textvariable=status_var, font=("Arial", 14))
        status_label.pack(pady=20)
        
        # 计数器
        counter = [0]
        
        def update_counter():
            counter[0] += 1
            status_var.set(f"程序运行中... {counter[0]}秒")
            
            # 每秒更新一次
            root.after(1000, update_counter)
        
        # 启动计数器
        update_counter()
        
        # 测试按钮
        def test_opencv():
            try:
                cap = cv2.VideoCapture()
                cap.release()
                status_var.set("OpenCV测试成功")
            except Exception as e:
                status_var.set(f"OpenCV测试失败: {e}")
        
        tk.Button(root, text="测试OpenCV", command=test_opencv).pack(pady=10)
        
        # 强制保持运行
        def keep_running():
            status_var.set("强制保持运行中...")
            root.after(2000, lambda: status_var.set(f"程序运行中... {counter[0]}秒"))
        
        tk.Button(root, text="保持运行", command=keep_running).pack(pady=5)
        
        # 手动关闭
        def manual_close():
            import tkinter.messagebox as msgbox
            if msgbox.askyesno("确认", "确定要关闭吗？"):
                root.destroy()
        
        tk.Button(root, text="手动关闭", command=manual_close).pack(pady=5)
        
        # 禁用默认关闭
        def on_closing():
            import tkinter.messagebox as msgbox
            if msgbox.askyesno("确认关闭", "真的要关闭程序吗？\n\n选择'否'继续运行"):
                print("用户确认关闭")
                root.destroy()
            else:
                print("用户取消关闭")
                status_var.set("用户取消关闭，继续运行")
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        print("启动组合测试窗口...")
        print("这个窗口应该会一直运行，除非你手动关闭")
        
        # 启动主循环
        root.mainloop()
        
        print("✅ 组合测试正常结束")
        return True
        
    except Exception as e:
        print(f"❌ 组合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def wait_for_user():
    """等待用户输入"""
    print("\n" + "=" * 50)
    print("等待用户确认")
    print("=" * 50)
    
    try:
        input("按回车键继续下一个测试...")
    except:
        print("无法获取用户输入，自动继续...")
        time.sleep(2)

def main():
    """主测试函数"""
    print("🔍 程序自动关闭问题 - 基础诊断")
    print("这个测试将帮助找出程序自动关闭的真正原因")
    
    tests = [
        ("基础Python功能", test_basic_python),
        ("最小Tkinter窗口", test_tkinter_minimal),
        ("基础OpenCV功能", test_opencv_basic),
        ("组合功能测试", test_combined),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
            results.append((test_name, False))
            import traceback
            traceback.print_exc()
        
        # 等待用户确认（除了最后一个测试）
        if test_func != test_combined:
            wait_for_user()
    
    # 显示总结
    print("\n" + "=" * 50)
    print("🏁 测试总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！环境正常")
        print("如果播放器仍然自动关闭，可能是特定的代码逻辑问题")
    else:
        print("⚠️  部分测试失败，可能存在环境问题")
        print("建议检查失败的测试项目")
    
    # 最终等待
    print("\n测试完成。")
    try:
        input("按回车键退出...")
    except:
        print("等待5秒后自动退出...")
        time.sleep(5)

if __name__ == "__main__":
    main()
