#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的音频同步实现
解决音频不同步的根本问题
"""

import time
import threading

class CorrectAudioSync:
    """正确的音频同步管理器"""
    
    def __init__(self):
        self.reset()
        
    def reset(self):
        """重置所有同步状态"""
        self.video_start_real_time = None  # 视频开始播放的真实时间
        self.video_start_position = 0      # 视频开始播放时的位置（秒）
        self.audio_start_real_time = None  # 音频开始播放的真实时间
        self.audio_start_position = 0      # 音频开始播放时的位置（秒）
        self.sync_offset_ms = 0            # 用户设置的同步偏移（毫秒）
        self.is_playing = False
        
    def start_playback(self, video_position_seconds=0):
        """开始播放（视频和音频都从指定位置开始）"""
        current_real_time = time.time()
        
        self.video_start_real_time = current_real_time
        self.video_start_position = video_position_seconds
        
        self.audio_start_real_time = current_real_time
        self.audio_start_position = video_position_seconds  # 音频也应该从相同位置开始
        
        self.is_playing = True
        
        print(f"🎬 开始播放:")
        print(f"   视频位置: {video_position_seconds:.2f}s")
        print(f"   音频位置: {video_position_seconds:.2f}s")
        print(f"   开始时间: {current_real_time:.3f}")
        
    def video_seek(self, new_video_position_seconds):
        """视频跳转（只更新视频时间基准）"""
        if not self.is_playing:
            return
            
        current_real_time = time.time()
        
        # 只更新视频的时间基准
        self.video_start_real_time = current_real_time
        self.video_start_position = new_video_position_seconds
        
        print(f"🎬 视频跳转到: {new_video_position_seconds:.2f}s")
        
    def audio_seek(self, new_audio_position_seconds):
        """音频跳转（只更新音频时间基准）"""
        if not self.is_playing:
            return
            
        current_real_time = time.time()
        
        # 只更新音频的时间基准
        self.audio_start_real_time = current_real_time
        self.audio_start_position = new_audio_position_seconds
        
        print(f"🔊 音频跳转到: {new_audio_position_seconds:.2f}s")
        
    def sync_audio_to_video(self):
        """将音频同步到当前视频位置"""
        if not self.is_playing:
            return
            
        current_video_position = self.get_current_video_position()
        self.audio_seek(current_video_position)
        
        print(f"🔄 音频同步到视频位置: {current_video_position:.2f}s")
        
    def get_current_video_position(self):
        """获取当前视频位置"""
        if not self.is_playing or not self.video_start_real_time:
            return 0
            
        current_real_time = time.time()
        elapsed_time = current_real_time - self.video_start_real_time
        return self.video_start_position + elapsed_time
        
    def get_current_audio_position(self):
        """获取当前音频位置"""
        if not self.is_playing or not self.audio_start_real_time:
            return 0
            
        current_real_time = time.time()
        elapsed_time = current_real_time - self.audio_start_real_time
        return self.audio_start_position + elapsed_time
        
    def get_sync_difference(self):
        """获取音视频同步差异（秒）"""
        video_pos = self.get_current_video_position()
        audio_pos = self.get_current_audio_position()
        
        # 应用用户设置的偏移
        offset_seconds = self.sync_offset_ms / 1000.0
        adjusted_audio_pos = audio_pos + offset_seconds
        
        return video_pos - adjusted_audio_pos
        
    def set_sync_offset(self, offset_ms):
        """设置同步偏移"""
        self.sync_offset_ms = offset_ms
        print(f"🔧 同步偏移设置为: {offset_ms}ms")
        
    def get_status(self):
        """获取详细状态"""
        if not self.is_playing:
            return "未播放"
            
        video_pos = self.get_current_video_position()
        audio_pos = self.get_current_audio_position()
        sync_diff = self.get_sync_difference()
        
        return {
            'video_position': video_pos,
            'audio_position': audio_pos,
            'sync_difference': sync_diff,
            'sync_offset_ms': self.sync_offset_ms,
            'is_synced': abs(sync_diff) < 0.1  # 100ms以内认为同步
        }

# 使用示例和测试
def test_correct_sync():
    """测试正确的同步逻辑"""
    sync = CorrectAudioSync()
    
    print("=" * 50)
    print("测试正确的音频同步逻辑")
    print("=" * 50)
    
    # 测试1: 从头开始播放
    print("\n📋 测试1: 从头开始播放")
    sync.start_playback(0)
    time.sleep(1)
    status = sync.get_status()
    print(f"1秒后 - 视频: {status['video_position']:.2f}s, 音频: {status['audio_position']:.2f}s, 差异: {status['sync_difference']:.3f}s")
    
    # 测试2: 视频跳转
    print("\n📋 测试2: 视频跳转到10秒")
    sync.video_seek(10.0)
    time.sleep(0.5)
    status = sync.get_status()
    print(f"跳转后 - 视频: {status['video_position']:.2f}s, 音频: {status['audio_position']:.2f}s, 差异: {status['sync_difference']:.3f}s")
    
    # 测试3: 音频同步
    print("\n📋 测试3: 音频同步到视频")
    sync.sync_audio_to_video()
    time.sleep(0.5)
    status = sync.get_status()
    print(f"同步后 - 视频: {status['video_position']:.2f}s, 音频: {status['audio_position']:.2f}s, 差异: {status['sync_difference']:.3f}s")
    
    # 测试4: 设置偏移
    print("\n📋 测试4: 设置500ms偏移")
    sync.set_sync_offset(500)
    status = sync.get_status()
    print(f"偏移后 - 视频: {status['video_position']:.2f}s, 音频: {status['audio_position']:.2f}s, 差异: {status['sync_difference']:.3f}s")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_correct_sync()
