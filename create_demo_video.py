#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示视频文件
用于测试MP4播放器的字幕功能
"""

import cv2
import numpy as np
import os

def create_demo_video():
    """创建一个简单的演示视频"""
    
    # 视频参数
    width, height = 640, 480
    fps = 30
    duration = 30  # 30秒
    total_frames = fps * duration
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    video_path = 'demo_video.mp4'
    out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
    
    if not out.isOpened():
        print("无法创建视频文件")
        return None
    
    print(f"正在创建演示视频: {video_path}")
    print(f"分辨率: {width}x{height}, 帧率: {fps}, 时长: {duration}秒")
    
    # 生成视频帧
    for frame_num in range(total_frames):
        # 创建彩色背景
        # 使用渐变色背景
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 计算当前时间
        current_time = frame_num / fps
        
        # 创建渐变背景
        for y in range(height):
            for x in range(width):
                # 基于时间和位置的颜色变化
                r = int(128 + 127 * np.sin(current_time * 0.5 + x * 0.01))
                g = int(128 + 127 * np.sin(current_time * 0.3 + y * 0.01))
                b = int(128 + 127 * np.sin(current_time * 0.7))
                frame[y, x] = [b, g, r]  # OpenCV使用BGR格式
        
        # 添加一些图形元素
        center_x, center_y = width // 2, height // 2
        
        # 绘制移动的圆圈
        circle_x = int(center_x + 100 * np.sin(current_time * 2))
        circle_y = int(center_y + 50 * np.cos(current_time * 2))
        cv2.circle(frame, (circle_x, circle_y), 30, (255, 255, 255), -1)
        cv2.circle(frame, (circle_x, circle_y), 30, (0, 0, 0), 2)
        
        # 绘制旋转的矩形
        angle = current_time * 45  # 每秒旋转45度
        rect_size = 60
        
        # 计算矩形的四个角点
        cos_a = np.cos(np.radians(angle))
        sin_a = np.sin(np.radians(angle))
        
        points = np.array([
            [-rect_size//2, -rect_size//2],
            [rect_size//2, -rect_size//2],
            [rect_size//2, rect_size//2],
            [-rect_size//2, rect_size//2]
        ], dtype=np.float32)
        
        # 旋转矩形
        rotation_matrix = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
        rotated_points = np.dot(points, rotation_matrix.T)
        rotated_points += [center_x, center_y]
        rotated_points = rotated_points.astype(np.int32)
        
        cv2.fillPoly(frame, [rotated_points], (0, 255, 255))
        cv2.polylines(frame, [rotated_points], True, (0, 0, 0), 2)
        
        # 添加时间文本
        time_text = f"时间: {current_time:.1f}s"
        cv2.putText(frame, time_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(frame, time_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 1)
        
        # 添加帧数文本
        frame_text = f"帧: {frame_num + 1}/{total_frames}"
        cv2.putText(frame, frame_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, frame_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
        
        # 添加说明文本
        if current_time < 5:
            info_text = "MP4播放器演示视频"
        elif current_time < 10:
            info_text = "支持实时字幕生成"
        elif current_time < 15:
            info_text = "使用faster-whisper引擎"
        elif current_time < 20:
            info_text = "可调整字幕样式"
        elif current_time < 25:
            info_text = "显示生成进度"
        else:
            info_text = "演示即将结束"
        
        text_size = cv2.getTextSize(info_text, cv2.FONT_HERSHEY_SIMPLEX, 1.2, 2)[0]
        text_x = (width - text_size[0]) // 2
        text_y = height - 50
        
        cv2.putText(frame, info_text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
        cv2.putText(frame, info_text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 2)
        
        # 写入帧
        out.write(frame)
        
        # 显示进度
        if frame_num % (fps * 2) == 0:  # 每2秒显示一次进度
            progress = (frame_num + 1) / total_frames * 100
            print(f"进度: {progress:.1f}%")
    
    # 释放资源
    out.release()
    
    print(f"演示视频创建完成: {video_path}")
    print(f"文件大小: {os.path.getsize(video_path) / 1024 / 1024:.2f} MB")
    
    return video_path

if __name__ == "__main__":
    video_path = create_demo_video()
    if video_path:
        print(f"\n可以使用以下命令播放视频:")
        print(f"python simple_mp4_player.py")
        print(f"然后选择文件: {video_path}")
