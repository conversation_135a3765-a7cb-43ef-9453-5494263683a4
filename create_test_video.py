#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试视频文件
用于验证播放器功能
"""

import cv2
import numpy as np
import os

def create_test_video(output_path="test_video.mp4", duration=10, fps=30):
    """创建测试视频"""
    print(f"创建测试视频: {output_path}")
    print(f"时长: {duration}秒, 帧率: {fps}fps")
    
    # 视频参数
    width, height = 1280, 720
    total_frames = duration * fps
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    if not out.isOpened():
        print("❌ 无法创建视频写入器")
        return False
    
    print(f"生成 {total_frames} 帧...")
    
    for i in range(total_frames):
        # 创建帧
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 时间信息
        current_time = i / fps
        
        # 背景渐变
        t = i / total_frames
        
        # 彩色背景
        frame[:, :, 0] = int(100 + 100 * np.sin(t * 2 * np.pi))  # 红色
        frame[:, :, 1] = int(100 + 100 * np.sin(t * 2 * np.pi + np.pi/3))  # 绿色
        frame[:, :, 2] = int(100 + 100 * np.sin(t * 2 * np.pi + 2*np.pi/3))  # 蓝色
        
        # 添加移动的圆形
        center_x = int(width * (0.2 + 0.6 * t))
        center_y = int(height * (0.3 + 0.4 * np.sin(t * 4 * np.pi)))
        radius = int(50 + 30 * np.sin(t * 6 * np.pi))
        
        cv2.circle(frame, (center_x, center_y), radius, (255, 255, 255), -1)
        cv2.circle(frame, (center_x, center_y), radius, (0, 0, 0), 3)
        
        # 添加文字信息
        font = cv2.FONT_HERSHEY_SIMPLEX
        
        # 标题
        title = "测试视频 - Test Video"
        cv2.putText(frame, title, (50, 80), font, 2, (255, 255, 255), 3)
        cv2.putText(frame, title, (50, 80), font, 2, (0, 0, 0), 1)
        
        # 时间信息
        time_text = f"时间: {current_time:.1f}s / {duration}s"
        cv2.putText(frame, time_text, (50, 150), font, 1.5, (255, 255, 255), 3)
        cv2.putText(frame, time_text, (50, 150), font, 1.5, (0, 0, 0), 1)
        
        # 帧信息
        frame_text = f"帧: {i+1} / {total_frames}"
        cv2.putText(frame, frame_text, (50, 200), font, 1, (255, 255, 255), 2)
        cv2.putText(frame, frame_text, (50, 200), font, 1, (0, 0, 0), 1)
        
        # 分辨率信息
        res_text = f"分辨率: {width}x{height}"
        cv2.putText(frame, res_text, (50, 250), font, 1, (255, 255, 255), 2)
        cv2.putText(frame, res_text, (50, 250), font, 1, (0, 0, 0), 1)
        
        # 进度条
        progress_width = width - 100
        progress_height = 20
        progress_x = 50
        progress_y = height - 100
        
        # 进度条背景
        cv2.rectangle(frame, (progress_x, progress_y), 
                     (progress_x + progress_width, progress_y + progress_height), 
                     (100, 100, 100), -1)
        
        # 进度条填充
        fill_width = int(progress_width * t)
        cv2.rectangle(frame, (progress_x, progress_y), 
                     (progress_x + fill_width, progress_y + progress_height), 
                     (0, 255, 0), -1)
        
        # 进度条边框
        cv2.rectangle(frame, (progress_x, progress_y), 
                     (progress_x + progress_width, progress_y + progress_height), 
                     (255, 255, 255), 2)
        
        # 进度百分比
        progress_text = f"{t*100:.1f}%"
        cv2.putText(frame, progress_text, (progress_x + progress_width + 20, progress_y + 15), 
                   font, 0.7, (255, 255, 255), 2)
        
        # 字幕区域提示
        subtitle_y = height - 50
        subtitle_text = f"字幕测试文本 - 第{i//30 + 1}段"
        cv2.putText(frame, subtitle_text, (50, subtitle_y), font, 1, (255, 255, 0), 2)
        
        # 写入帧
        out.write(frame)
        
        # 显示进度
        if (i + 1) % (fps * 2) == 0:  # 每2秒显示一次进度
            print(f"  进度: {i+1}/{total_frames} ({(i+1)/total_frames*100:.1f}%)")
    
    out.release()
    
    # 验证创建的视频
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path)
        print(f"✓ 视频创建成功")
        print(f"  文件: {output_path}")
        print(f"  大小: {file_size:,} 字节 ({file_size / (1024*1024):.2f} MB)")
        
        # 验证视频可读性
        cap = cv2.VideoCapture(output_path)
        if cap.isOpened():
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps_check = cap.get(cv2.CAP_PROP_FPS)
            width_check = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height_check = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            print(f"  验证信息:")
            print(f"    帧数: {frame_count}")
            print(f"    帧率: {fps_check}")
            print(f"    分辨率: {width_check}x{height_check}")
            
            # 测试读取几帧
            frames_read = 0
            for i in range(min(10, frame_count)):
                ret, frame = cap.read()
                if ret:
                    frames_read += 1
                else:
                    break
            
            print(f"    可读取帧数: {frames_read}/10")
            cap.release()
            
            if frames_read > 0:
                print("✓ 视频验证通过，可以正常播放")
                return True
            else:
                print("❌ 视频无法读取")
                return False
        else:
            print("❌ 无法打开创建的视频")
            return False
    else:
        print("❌ 视频文件未创建")
        return False

def create_multiple_test_videos():
    """创建多个测试视频"""
    videos = [
        ("test_video_short.mp4", 5, 30),    # 5秒，30fps
        ("test_video_medium.mp4", 15, 30),  # 15秒，30fps
        ("test_video_hd.mp4", 10, 25),      # 10秒，25fps
    ]
    
    success_count = 0
    for filename, duration, fps in videos:
        print(f"\n{'='*50}")
        if create_test_video(filename, duration, fps):
            success_count += 1
        print(f"{'='*50}")
    
    print(f"\n总结: 成功创建 {success_count}/{len(videos)} 个测试视频")
    return success_count

def main():
    """主函数"""
    print("测试视频创建工具")
    print("用于创建测试视频文件验证播放器功能")
    
    # 创建单个测试视频
    print("\n创建标准测试视频...")
    if create_test_video("test_video.mp4", 10, 30):
        print("\n🎉 测试视频创建成功！")
        print("可以使用 test_video.mp4 测试播放器功能")
    else:
        print("\n❌ 测试视频创建失败")
    
    # 询问是否创建更多测试视频
    try:
        choice = input("\n是否创建更多测试视频？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            create_multiple_test_videos()
    except:
        pass
    
    print("\n完成！")

if __name__ == "__main__":
    main()
