#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建带音频的测试视频
"""

import numpy as np
from moviepy.editor import VideoClip, AudioClip, CompositeVideoClip
import os

def create_test_video_with_audio():
    """创建一个带音频的测试视频"""
    print("🎬 创建带音频的测试视频...")
    
    try:
        # 视频参数
        duration = 10  # 10秒
        fps = 30
        size = (640, 480)
        
        # 创建视频帧生成函数
        def make_frame(t):
            """生成视频帧"""
            # 创建一个渐变色背景
            frame = np.zeros((size[1], size[0], 3), dtype=np.uint8)
            
            # 添加时间变化的颜色
            color_r = int(128 + 127 * np.sin(2 * np.pi * t / duration))
            color_g = int(128 + 127 * np.sin(2 * np.pi * t / duration + 2))
            color_b = int(128 + 127 * np.sin(2 * np.pi * t / duration + 4))
            
            frame[:, :] = [color_r, color_g, color_b]
            
            # 添加一个移动的圆圈
            center_x = int(size[0] * (0.5 + 0.3 * np.sin(2 * np.pi * t / duration)))
            center_y = int(size[1] * (0.5 + 0.3 * np.cos(2 * np.pi * t / duration)))
            radius = 50
            
            y, x = np.ogrid[:size[1], :size[0]]
            mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
            frame[mask] = [255, 255, 255]  # 白色圆圈
            
            return frame
        
        # 创建视频剪辑
        video_clip = VideoClip(make_frame, duration=duration)
        video_clip = video_clip.set_fps(fps)
        
        # 创建音频生成函数
        def make_audio(t):
            """生成音频"""
            # 创建一个简单的正弦波音频
            frequency = 440  # A4音符
            amplitude = 0.3
            
            # 添加一些变化
            freq_mod = frequency * (1 + 0.1 * np.sin(2 * np.pi * t / 2))
            
            # 生成立体声音频
            audio_left = amplitude * np.sin(2 * np.pi * freq_mod * t)
            audio_right = amplitude * np.sin(2 * np.pi * freq_mod * t + 0.5)
            
            return np.array([audio_left, audio_right]).T
        
        # 创建音频剪辑
        audio_clip = AudioClip(make_audio, duration=duration, fps=44100)
        
        # 合成视频和音频
        final_clip = video_clip.set_audio(audio_clip)
        
        # 输出文件
        output_file = "test_video_with_audio.mp4"
        
        print(f"🔊 正在生成视频文件: {output_file}")
        print("⏰ 这可能需要几分钟时间...")
        
        # 导出视频
        final_clip.write_videofile(
            output_file,
            fps=fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # 关闭剪辑
        final_clip.close()
        video_clip.close()
        audio_clip.close()
        
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ 测试视频创建成功: {output_file}")
            print(f"📁 文件大小: {file_size} 字节")
            print(f"⏱️ 视频时长: {duration} 秒")
            print(f"🔊 包含音频: 440Hz正弦波")
            return output_file
        else:
            print("❌ 视频文件创建失败")
            return None
            
    except Exception as e:
        print(f"❌ 创建测试视频失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🎬 测试视频生成工具")
    print("=" * 50)
    
    # 检查是否已存在测试视频
    test_file = "test_video_with_audio.mp4"
    if os.path.exists(test_file):
        print(f"📁 测试视频已存在: {test_file}")
        
        # 检查是否有音频
        try:
            from moviepy.editor import VideoFileClip
            clip = VideoFileClip(test_file)
            if clip.audio is not None:
                print("✅ 视频包含音频轨道")
            else:
                print("❌ 视频不包含音频轨道，需要重新创建")
                os.remove(test_file)
                create_test_video_with_audio()
            clip.close()
        except Exception as e:
            print(f"检查视频失败: {e}")
            create_test_video_with_audio()
    else:
        create_test_video_with_audio()
    
    print("=" * 50)
    print("🎬 完成")
    print()
    print("现在可以使用以下命令测试音频播放：")
    print("python simple_mp4_player.py test_video_with_audio.mp4")

if __name__ == "__main__":
    main()
