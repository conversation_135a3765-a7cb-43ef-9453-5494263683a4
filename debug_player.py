#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版播放器 - 找出自动关闭的真正原因
"""

import sys
import os
import time
import traceback
import atexit

# 设置详细的日志记录
def log_message(msg):
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {msg}")
    
    # 同时写入日志文件
    try:
        with open("debug_log.txt", "a", encoding="utf-8") as f:
            f.write(f"[{timestamp}] {msg}\n")
            f.flush()
    except:
        pass

def exit_handler():
    log_message("🔚 程序退出处理器被调用")

# 注册退出处理器
atexit.register(exit_handler)

log_message("🚀 开始启动调试版播放器")

try:
    log_message("📦 导入tkinter...")
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox
    log_message("✅ tkinter导入成功")
    
    log_message("📦 导入OpenCV...")
    import cv2
    log_message(f"✅ OpenCV导入成功，版本: {cv2.__version__}")
    
    log_message("📦 导入PIL...")
    from PIL import Image, ImageTk
    log_message("✅ PIL导入成功")
    
except Exception as e:
    log_message(f"❌ 导入失败: {e}")
    traceback.print_exc()
    input("按回车键退出...")
    sys.exit(1)

class DebugPlayer:
    def __init__(self):
        log_message("🔧 开始初始化播放器...")
        
        try:
            log_message("创建主窗口...")
            self.root = tk.Tk()
            self.root.title("调试版播放器 - 不会自动关闭")
            self.root.geometry("600x500")
            
            log_message("设置变量...")
            self.cap = None
            self.running = True
            
            log_message("创建界面...")
            self.create_ui()
            
            log_message("设置关闭处理...")
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # 设置定时器，定期报告状态
            self.status_timer()
            
            log_message("✅ 播放器初始化完成")
            
        except Exception as e:
            log_message(f"❌ 初始化失败: {e}")
            traceback.print_exc()
            raise
    
    def create_ui(self):
        """创建最简单的界面"""
        try:
            log_message("创建界面组件...")
            
            # 主框架
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # 标题
            title_label = ttk.Label(main_frame, text="调试版MP4播放器", 
                                   font=("Arial", 16, "bold"))
            title_label.pack(pady=(0, 20))
            
            # 状态显示
            self.status_var = tk.StringVar(value="程序正在运行...")
            status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                   font=("Arial", 12))
            status_label.pack(pady=10)
            
            # 运行时间显示
            self.runtime_var = tk.StringVar(value="运行时间: 0秒")
            runtime_label = ttk.Label(main_frame, textvariable=self.runtime_var)
            runtime_label.pack(pady=5)
            
            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=20)
            
            # 测试按钮
            ttk.Button(button_frame, text="测试按钮1", 
                      command=self.test_button1).pack(side=tk.LEFT, padx=5)
            
            ttk.Button(button_frame, text="测试按钮2", 
                      command=self.test_button2).pack(side=tk.LEFT, padx=5)
            
            ttk.Button(button_frame, text="选择视频", 
                      command=self.select_video).pack(side=tk.LEFT, padx=5)
            
            # 强制保持运行按钮
            keep_frame = ttk.Frame(main_frame)
            keep_frame.pack(pady=20)
            
            ttk.Button(keep_frame, text="🔒 强制保持运行", 
                      command=self.force_keep_alive).pack(pady=5)
            
            self.keep_status = tk.StringVar(value="正常运行模式")
            ttk.Label(keep_frame, textvariable=self.keep_status, 
                     foreground="green").pack(pady=5)
            
            # 日志显示区域
            log_frame = ttk.LabelFrame(main_frame, text="运行日志")
            log_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
            
            # 创建文本框和滚动条
            text_frame = ttk.Frame(log_frame)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            self.log_text = tk.Text(text_frame, height=8, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
            self.log_text.configure(yscrollcommand=scrollbar.set)
            
            self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 添加初始日志
            self.add_log("界面创建完成")
            self.add_log("程序正在正常运行...")
            
            log_message("✅ 界面创建完成")
            
        except Exception as e:
            log_message(f"❌ 界面创建失败: {e}")
            traceback.print_exc()
            raise
    
    def add_log(self, message):
        """添加日志到界面"""
        try:
            timestamp = time.strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)  # 滚动到最新
            
            # 限制日志行数
            lines = self.log_text.get("1.0", tk.END).split('\n')
            if len(lines) > 50:
                self.log_text.delete("1.0", "10.0")
                
        except Exception as e:
            log_message(f"添加日志失败: {e}")
    
    def test_button1(self):
        """测试按钮1"""
        log_message("🔘 测试按钮1被点击")
        self.add_log("测试按钮1被点击")
        self.status_var.set("测试按钮1已点击")
    
    def test_button2(self):
        """测试按钮2"""
        log_message("🔘 测试按钮2被点击")
        self.add_log("测试按钮2被点击")
        self.status_var.set("测试按钮2已点击")
    
    def select_video(self):
        """选择视频文件"""
        try:
            log_message("📁 打开文件选择对话框")
            self.add_log("打开文件选择对话框...")
            
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )
            
            if file_path:
                log_message(f"📁 选择了文件: {file_path}")
                self.add_log(f"选择了文件: {os.path.basename(file_path)}")
                self.test_video_load(file_path)
            else:
                log_message("📁 用户取消了文件选择")
                self.add_log("用户取消了文件选择")
                
        except Exception as e:
            log_message(f"❌ 文件选择失败: {e}")
            self.add_log(f"文件选择失败: {e}")
            traceback.print_exc()
    
    def test_video_load(self, file_path):
        """测试视频加载"""
        try:
            log_message(f"🎬 测试加载视频: {file_path}")
            self.add_log("正在测试视频加载...")
            
            # 检查文件
            if not os.path.exists(file_path):
                raise Exception("文件不存在")
            
            file_size = os.path.getsize(file_path)
            log_message(f"文件大小: {file_size} 字节")
            self.add_log(f"文件大小: {file_size:,} 字节")
            
            # 尝试打开视频
            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                log_message(f"视频信息: {width}x{height}, {fps}fps, {frame_count}帧")
                self.add_log(f"✅ 视频加载成功")
                self.add_log(f"分辨率: {width}x{height}")
                self.add_log(f"帧率: {fps}, 总帧数: {frame_count}")
                
                cap.release()
                self.status_var.set("视频测试成功")
            else:
                raise Exception("无法打开视频文件")
                
        except Exception as e:
            log_message(f"❌ 视频加载测试失败: {e}")
            self.add_log(f"❌ 视频加载失败: {e}")
            self.status_var.set("视频测试失败")
    
    def force_keep_alive(self):
        """强制保持运行"""
        log_message("🔒 强制保持运行模式激活")
        self.add_log("🔒 强制保持运行模式激活")
        self.keep_status.set("🔒 强制保持运行中...")
        
        # 设置定时器，定期报告状态
        def keep_alive_timer():
            if self.running:
                self.add_log("💓 程序保持运行中...")
                self.root.after(5000, keep_alive_timer)  # 每5秒报告一次
        
        keep_alive_timer()
    
    def status_timer(self):
        """状态定时器"""
        if self.running:
            try:
                # 计算运行时间
                if not hasattr(self, 'start_time'):
                    self.start_time = time.time()
                
                runtime = int(time.time() - self.start_time)
                self.runtime_var.set(f"运行时间: {runtime}秒")
                
                # 每10秒报告一次状态
                if runtime % 10 == 0:
                    log_message(f"💓 程序运行正常，已运行{runtime}秒")
                
                # 继续定时器
                self.root.after(1000, self.status_timer)
                
            except Exception as e:
                log_message(f"状态定时器错误: {e}")
    
    def on_closing(self):
        """关闭处理"""
        try:
            log_message("🚪 用户请求关闭程序")
            self.add_log("用户请求关闭程序")
            
            # 询问确认
            result = messagebox.askyesno("确认关闭", "确定要关闭程序吗？")
            if result:
                log_message("✅ 用户确认关闭")
                self.running = False
                
                if self.cap:
                    self.cap.release()
                
                self.root.destroy()
            else:
                log_message("❌ 用户取消关闭")
                self.add_log("用户取消关闭，继续运行")
                
        except Exception as e:
            log_message(f"关闭处理错误: {e}")
            traceback.print_exc()
    
    def run(self):
        """运行播放器"""
        try:
            log_message("🎬 启动主循环...")
            self.add_log("启动主循环...")
            
            # 显示启动信息
            self.status_var.set("程序启动成功，正在运行...")
            
            log_message("💡 程序已启动，等待用户操作...")
            log_message("💡 如果程序自动关闭，请检查 debug_log.txt 文件")
            
            # 启动主循环
            self.root.mainloop()
            
            log_message("🔚 主循环结束")
            
        except Exception as e:
            log_message(f"❌ 主循环异常: {e}")
            traceback.print_exc()
            
            # 保持控制台打开
            try:
                input("\n程序异常，按回车键退出...")
            except:
                time.sleep(10)

def main():
    """主函数"""
    try:
        log_message("=" * 50)
        log_message("🚀 调试版MP4播放器启动")
        log_message("=" * 50)
        log_message(f"Python版本: {sys.version}")
        log_message(f"工作目录: {os.getcwd()}")
        
        # 创建播放器
        player = DebugPlayer()
        
        # 运行播放器
        player.run()
        
        log_message("🔚 程序正常结束")
        
    except Exception as e:
        log_message(f"❌ 程序启动失败: {e}")
        traceback.print_exc()
        
        # 保持控制台打开
        try:
            print("\n程序启动失败！")
            print("请检查 debug_log.txt 文件获取详细信息")
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
