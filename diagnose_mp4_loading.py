#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MP4文件加载诊断工具
检查MP4文件的各种问题并提供解决方案
"""

import os
import sys
import cv2
import tempfile
import numpy as np

def check_file_basic_info(video_path):
    """检查文件基本信息"""
    print("=" * 60)
    print("1. 文件基本信息检查")
    print("=" * 60)
    
    if not video_path:
        print("❌ 未提供视频文件路径")
        return False
    
    print(f"文件路径: {video_path}")
    
    # 检查文件是否存在
    if not os.path.exists(video_path):
        print("❌ 文件不存在")
        return False
    
    print("✓ 文件存在")
    
    # 检查文件大小
    file_size = os.path.getsize(video_path)
    print(f"文件大小: {file_size:,} 字节 ({file_size / (1024*1024):.2f} MB)")
    
    if file_size == 0:
        print("❌ 文件大小为0，文件可能损坏")
        return False
    
    if file_size < 1024:
        print("⚠️  文件过小，可能不是有效的视频文件")
        return False
    
    # 检查文件扩展名
    _, ext = os.path.splitext(video_path)
    print(f"文件扩展名: {ext}")
    
    if ext.lower() not in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
        print("⚠️  不是常见的视频文件扩展名")
    
    # 检查文件权限
    if not os.access(video_path, os.R_OK):
        print("❌ 文件无读取权限")
        return False
    
    print("✓ 文件权限正常")
    
    return True

def test_opencv_backends(video_path):
    """测试不同的OpenCV后端"""
    print("\n" + "=" * 60)
    print("2. OpenCV后端测试")
    print("=" * 60)
    
    backends = [
        ("默认后端", None),
        ("FFMPEG后端", cv2.CAP_FFMPEG),
        ("MSMF后端", cv2.CAP_MSMF),
        ("DSHOW后端", cv2.CAP_DSHOW),
    ]
    
    working_backends = []
    
    for backend_name, backend_flag in backends:
        print(f"\n测试 {backend_name}...")
        
        try:
            if backend_flag is not None:
                cap = cv2.VideoCapture(video_path, backend_flag)
            else:
                cap = cv2.VideoCapture(video_path)
            
            if cap.isOpened():
                # 获取基本信息
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                print(f"  分辨率: {width}x{height}")
                print(f"  帧率: {fps:.2f}")
                print(f"  总帧数: {frame_count}")
                
                # 尝试读取第一帧
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"  ✓ 成功读取帧，形状: {frame.shape}")
                    working_backends.append(backend_name)
                else:
                    print("  ❌ 无法读取帧")
                
                cap.release()
            else:
                print("  ❌ 无法打开文件")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    print(f"\n可用后端: {len(working_backends)}/{len(backends)}")
    for backend in working_backends:
        print(f"  ✓ {backend}")
    
    return len(working_backends) > 0

def check_video_codec(video_path):
    """检查视频编解码器"""
    print("\n" + "=" * 60)
    print("3. 视频编解码器检查")
    print("=" * 60)
    
    try:
        # 尝试使用ffprobe获取详细信息
        import subprocess
        
        try:
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-print_format', 'json', 
                '-show_format', '-show_streams', video_path
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                import json
                info = json.loads(result.stdout)
                
                print("✓ ffprobe 可用，获取详细信息:")
                
                # 格式信息
                if 'format' in info:
                    format_info = info['format']
                    print(f"  格式: {format_info.get('format_name', 'Unknown')}")
                    print(f"  时长: {float(format_info.get('duration', 0)):.2f} 秒")
                    print(f"  比特率: {format_info.get('bit_rate', 'Unknown')}")
                
                # 流信息
                if 'streams' in info:
                    for i, stream in enumerate(info['streams']):
                        codec_type = stream.get('codec_type', 'unknown')
                        codec_name = stream.get('codec_name', 'unknown')
                        print(f"  流 {i}: {codec_type} - {codec_name}")
                        
                        if codec_type == 'video':
                            width = stream.get('width', 'Unknown')
                            height = stream.get('height', 'Unknown')
                            print(f"    分辨率: {width}x{height}")
                
                return True
            else:
                print("❌ ffprobe 执行失败")
                
        except subprocess.TimeoutExpired:
            print("❌ ffprobe 超时")
        except FileNotFoundError:
            print("⚠️  ffprobe 不可用")
        except Exception as e:
            print(f"❌ ffprobe 错误: {e}")
    
    except ImportError:
        print("⚠️  subprocess 模块不可用")
    
    # 尝试使用OpenCV获取编解码器信息
    try:
        cap = cv2.VideoCapture(video_path)
        if cap.isOpened():
            fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
            codec = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
            print(f"OpenCV检测到的编解码器: {codec}")
            cap.release()
            return True
        else:
            print("❌ OpenCV无法打开文件获取编解码器信息")
            
    except Exception as e:
        print(f"❌ OpenCV编解码器检查失败: {e}")
    
    return False

def test_file_corruption(video_path):
    """测试文件是否损坏"""
    print("\n" + "=" * 60)
    print("4. 文件完整性检查")
    print("=" * 60)
    
    try:
        # 尝试读取文件的前几个字节
        with open(video_path, 'rb') as f:
            header = f.read(32)
            print(f"文件头 (前32字节): {header.hex()}")
            
            # 检查MP4文件签名
            if header[4:8] == b'ftyp':
                print("✓ 检测到MP4文件签名")
            else:
                print("⚠️  未检测到标准MP4文件签名")
            
            # 尝试读取更多内容
            f.seek(0, 2)  # 移动到文件末尾
            file_size = f.tell()
            
            # 读取文件的多个位置
            test_positions = [0, file_size // 4, file_size // 2, file_size * 3 // 4]
            
            for pos in test_positions:
                try:
                    f.seek(pos)
                    data = f.read(1024)
                    if len(data) > 0:
                        print(f"✓ 位置 {pos} 可读取")
                    else:
                        print(f"❌ 位置 {pos} 无数据")
                except Exception as e:
                    print(f"❌ 位置 {pos} 读取失败: {e}")
                    return False
        
        print("✓ 文件完整性检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件完整性检查失败: {e}")
        return False

def create_test_video():
    """创建测试视频文件"""
    print("\n" + "=" * 60)
    print("5. 创建测试视频")
    print("=" * 60)
    
    try:
        # 创建临时视频文件
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video_path = temp_video.name
        temp_video.close()
        
        print(f"创建测试视频: {temp_video_path}")
        
        # 视频参数
        width, height = 640, 480
        fps = 30
        duration = 5  # 5秒
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频写入器")
            return None
        
        # 生成测试帧
        total_frames = fps * duration
        for i in range(total_frames):
            # 创建彩色渐变帧
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # 渐变背景
            t = i / total_frames
            frame[:, :, 0] = int(255 * t)  # 红色渐变
            frame[:, :, 1] = int(255 * (1 - t))  # 绿色反向渐变
            frame[:, :, 2] = 128  # 固定蓝色
            
            # 添加文字
            cv2.putText(frame, f'Test Frame {i+1}', (50, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(frame, f'Time: {i/fps:.1f}s', (50, 150), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        
        # 验证创建的视频
        print("验证创建的测试视频...")
        if test_opencv_backends(temp_video_path):
            print("✓ 测试视频创建成功并可正常播放")
            return temp_video_path
        else:
            print("❌ 测试视频创建失败")
            os.unlink(temp_video_path)
            return None
            
    except Exception as e:
        print(f"❌ 创建测试视频失败: {e}")
        return None

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("6. 解决方案建议")
    print("=" * 60)
    
    solutions = [
        {
            "问题": "文件不存在或路径错误",
            "解决方案": [
                "检查文件路径是否正确",
                "确保文件名没有特殊字符",
                "尝试使用绝对路径",
                "检查文件是否被移动或删除"
            ]
        },
        {
            "问题": "文件损坏或格式不支持",
            "解决方案": [
                "使用其他播放器验证文件是否正常",
                "尝试重新下载或复制文件",
                "使用ffmpeg转换为标准MP4格式",
                "检查文件是否完整下载"
            ]
        },
        {
            "问题": "编解码器不支持",
            "解决方案": [
                "安装K-Lite Codec Pack",
                "更新OpenCV到最新版本",
                "使用ffmpeg转换编解码器",
                "尝试不同的视频后端"
            ]
        },
        {
            "问题": "权限问题",
            "解决方案": [
                "检查文件读取权限",
                "以管理员身份运行程序",
                "将文件复制到用户目录",
                "检查防病毒软件是否阻止"
            ]
        }
    ]
    
    for solution in solutions:
        print(f"\n{solution['问题']}:")
        for i, step in enumerate(solution['解决方案'], 1):
            print(f"  {i}. {step}")

def main():
    """主函数"""
    print("MP4文件加载诊断工具")
    print("用于诊断和解决MP4文件加载问题")
    
    # 获取视频文件路径
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
    else:
        # 尝试查找当前目录下的MP4文件
        mp4_files = [f for f in os.listdir('.') if f.lower().endswith('.mp4')]
        if mp4_files:
            video_path = mp4_files[0]
            print(f"自动选择文件: {video_path}")
        else:
            print("请提供MP4文件路径作为参数")
            print("用法: python diagnose_mp4_loading.py <video_file.mp4>")
            return
    
    # 运行诊断
    tests = [
        ("文件基本信息", lambda: check_file_basic_info(video_path)),
        ("OpenCV后端测试", lambda: test_opencv_backends(video_path)),
        ("视频编解码器检查", lambda: check_video_codec(video_path)),
        ("文件完整性检查", lambda: test_file_corruption(video_path)),
    ]
    
    passed_tests = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 创建测试视频
    print("\n" + "=" * 60)
    print("创建测试视频进行对比")
    print("=" * 60)
    
    test_video = create_test_video()
    if test_video:
        print(f"✓ 测试视频创建成功: {test_video}")
        print("可以使用此测试视频验证播放器功能")
    
    # 提供解决方案
    provide_solutions()
    
    # 总结
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    print(f"通过测试: {passed_tests}/{len(tests)}")
    
    if passed_tests == len(tests):
        print("🎉 文件检查全部通过，问题可能在播放器配置")
    elif passed_tests == 0:
        print("❌ 文件存在严重问题，建议更换文件或修复")
    else:
        print("⚠️  部分检查失败，请参考解决方案")

if __name__ == "__main__":
    main()
