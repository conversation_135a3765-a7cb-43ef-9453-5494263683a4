#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕错误诊断工具
检查和修复字幕系统的各种问题
"""

import os
import sys
import tempfile
import time
import threading
import queue

def check_whisper_status():
    """检查Whisper状态"""
    print("=" * 60)
    print("1. 检查Whisper状态")
    print("=" * 60)
    
    try:
        from faster_whisper import WhisperModel
        print("✓ faster-whisper 导入成功")
        
        # 尝试加载模型
        print("尝试加载Whisper模型...")
        model = WhisperModel("tiny", device="cpu", compute_type="int8")
        print("✓ Whisper模型加载成功")
        
        # 清理模型
        del model
        return True
        
    except ImportError as e:
        print(f"❌ faster-whisper 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Whisper模型加载失败: {e}")
        return False

def check_audio_extraction():
    """检查音频提取功能"""
    print("\n" + "=" * 60)
    print("2. 检查音频提取功能")
    print("=" * 60)
    
    # 检查moviepy
    try:
        from moviepy import VideoFileClip
        print("✓ moviepy 可用")
        moviepy_available = True
    except ImportError:
        print("❌ moviepy 不可用")
        moviepy_available = False
    
    # 检查ffmpeg
    try:
        import subprocess
        result = subprocess.run(["ffmpeg", "-version"], 
                              capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✓ ffmpeg 可用")
            ffmpeg_available = True
        else:
            print("❌ ffmpeg 不可用")
            ffmpeg_available = False
    except:
        print("❌ ffmpeg 不可用")
        ffmpeg_available = False
    
    if not moviepy_available and not ffmpeg_available:
        print("\n⚠️  警告: 没有可用的音频提取工具")
        print("建议安装: pip install moviepy")
        return False
    
    return True

def test_subtitle_text_processing():
    """测试字幕文本处理"""
    print("\n" + "=" * 60)
    print("3. 测试字幕文本处理")
    print("=" * 60)
    
    # 模拟字幕处理函数
    def process_subtitle_text(text):
        if not text:
            return ""
        
        try:
            # 基本文本清理
            processed_text = text.strip()
            processed_text = ' '.join(processed_text.split())
            
            # 中文标点转换
            import re
            if re.search(r'[\u4e00-\u9fff]', processed_text):
                processed_text = processed_text.replace(',', '，')
                processed_text = processed_text.replace('.', '。')
                processed_text = processed_text.replace('?', '？')
                processed_text = processed_text.replace('!', '！')
            
            # 长度控制
            if len(processed_text) > 50:
                processed_text = processed_text[:47] + "..."
            
            return processed_text
            
        except Exception as e:
            print(f"文本处理错误: {e}")
            return text
    
    # 测试用例
    test_cases = [
        ("你好,世界!", "你好，世界！"),
        ("这是一个测试.很好!", "这是一个测试。很好！"),
        ("  多余空格  ", "多余空格"),
        ("", ""),
        ("超长文本" * 20, "超长文本" * 15 + "..."),
    ]
    
    passed = 0
    for input_text, expected in test_cases:
        try:
            result = process_subtitle_text(input_text)
            if len(result) <= 50:  # 基本长度检查
                print(f"✓ '{input_text[:20]}...' → '{result[:20]}...'")
                passed += 1
            else:
                print(f"❌ '{input_text[:20]}...' → 长度超限")
        except Exception as e:
            print(f"❌ '{input_text[:20]}...' → 处理失败: {e}")
    
    print(f"\n文本处理测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)

def test_realtime_subtitle_system():
    """测试实时字幕系统"""
    print("\n" + "=" * 60)
    print("4. 测试实时字幕系统")
    print("=" * 60)
    
    # 模拟字幕缓冲区
    subtitle_buffer = queue.Queue()
    transcription_running = True
    
    # 模拟字幕数据
    test_subtitles = [
        {'text': '测试字幕1', 'start': 0.0, 'end': 2.0, 'index': 0},
        {'text': '测试字幕2', 'start': 2.5, 'end': 4.5, 'index': 1},
        {'text': '测试字幕3', 'start': 5.0, 'end': 7.0, 'index': 2},
    ]
    
    # 生成线程
    def generation_worker():
        try:
            for subtitle in test_subtitles:
                subtitle_buffer.put(subtitle)
                time.sleep(0.1)
            subtitle_buffer.put(None)  # 结束标记
            print("✓ 字幕生成线程完成")
            return True
        except Exception as e:
            print(f"❌ 字幕生成失败: {e}")
            return False
    
    # 显示线程
    def display_worker():
        try:
            displayed = 0
            while transcription_running:
                try:
                    subtitle_data = subtitle_buffer.get(timeout=1.0)
                    if subtitle_data is None:
                        break
                    
                    print(f"📝 显示字幕: {subtitle_data['text']}")
                    displayed += 1
                    
                except queue.Empty:
                    break
                except Exception as e:
                    print(f"❌ 字幕显示错误: {e}")
                    continue
            
            print(f"✓ 字幕显示线程完成 (显示了 {displayed} 条字幕)")
            return displayed == len(test_subtitles)
            
        except Exception as e:
            print(f"❌ 字幕显示失败: {e}")
            return False
    
    # 启动测试
    try:
        gen_thread = threading.Thread(target=generation_worker, daemon=True)
        display_thread = threading.Thread(target=display_worker, daemon=True)
        
        gen_thread.start()
        display_thread.start()
        
        # 等待完成
        gen_thread.join(timeout=5)
        display_thread.join(timeout=5)
        
        print("✓ 实时字幕系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 实时字幕系统测试失败: {e}")
        return False

def check_common_subtitle_errors():
    """检查常见字幕错误"""
    print("\n" + "=" * 60)
    print("5. 检查常见字幕错误")
    print("=" * 60)
    
    common_issues = [
        {
            "问题": "字幕不显示",
            "可能原因": [
                "Whisper模型未加载",
                "音频提取失败", 
                "字幕开关未启用",
                "视频没有音频轨道"
            ],
            "解决方案": [
                "检查Whisper模型状态",
                "安装moviepy或ffmpeg",
                "确保字幕开关开启",
                "检查视频文件是否包含音频"
            ]
        },
        {
            "问题": "字幕延迟或不同步",
            "可能原因": [
                "时间计算错误",
                "播放速度问题",
                "缓冲区阻塞",
                "线程同步问题"
            ],
            "解决方案": [
                "检查时间同步逻辑",
                "调整播放速度",
                "清理缓冲区",
                "重启字幕系统"
            ]
        },
        {
            "问题": "字幕文本错误",
            "可能原因": [
                "音频质量差",
                "语言设置错误",
                "模型精度不够",
                "文本处理错误"
            ],
            "解决方案": [
                "提高音频质量",
                "确认语言设置为中文",
                "使用更大的Whisper模型",
                "检查文本处理逻辑"
            ]
        },
        {
            "问题": "字幕格式问题",
            "可能原因": [
                "标点符号错误",
                "字符编码问题",
                "长度超限",
                "特殊字符处理"
            ],
            "解决方案": [
                "启用中文标点转换",
                "确保UTF-8编码",
                "启用自动断句",
                "过滤特殊字符"
            ]
        }
    ]
    
    print("常见字幕问题及解决方案:")
    for i, issue in enumerate(common_issues, 1):
        print(f"\n{i}. {issue['问题']}:")
        print("   可能原因:")
        for reason in issue['可能原因']:
            print(f"     • {reason}")
        print("   解决方案:")
        for solution in issue['解决方案']:
            print(f"     • {solution}")
    
    return True

def provide_troubleshooting_steps():
    """提供故障排除步骤"""
    print("\n" + "=" * 60)
    print("6. 故障排除步骤")
    print("=" * 60)
    
    steps = [
        {
            "步骤": "基础检查",
            "操作": [
                "确认视频文件包含音频",
                "检查字幕开关是否开启",
                "验证网络连接（首次下载模型需要）",
                "确保有足够的磁盘空间"
            ]
        },
        {
            "步骤": "依赖检查",
            "操作": [
                "运行: pip install faster-whisper",
                "运行: pip install moviepy",
                "检查Python版本 (推荐3.8+)",
                "验证OpenCV安装"
            ]
        },
        {
            "步骤": "重新初始化",
            "操作": [
                "重启播放器应用",
                "重新加载视频文件",
                "清理临时文件",
                "重置字幕设置"
            ]
        },
        {
            "步骤": "高级诊断",
            "操作": [
                "查看控制台错误信息",
                "测试不同的视频文件",
                "尝试不同的Whisper模型",
                "检查系统资源使用"
            ]
        }
    ]
    
    print("推荐的故障排除步骤:")
    for i, step in enumerate(steps, 1):
        print(f"\n{i}. {step['步骤']}:")
        for j, operation in enumerate(step['操作'], 1):
            print(f"   {j}) {operation}")
    
    return True

def main():
    """主函数"""
    print("字幕错误诊断工具")
    print("用于检查和修复字幕系统的各种问题")
    
    # 运行所有检查
    checks = [
        ("Whisper状态", check_whisper_status),
        ("音频提取功能", check_audio_extraction),
        ("字幕文本处理", test_subtitle_text_processing),
        ("实时字幕系统", test_realtime_subtitle_system),
        ("常见错误检查", check_common_subtitle_errors),
        ("故障排除步骤", provide_troubleshooting_steps)
    ]
    
    passed_checks = 0
    total_checks = len(checks) - 2  # 最后两个是信息性的，不计入通过率
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            if check_name in ["常见错误检查", "故障排除步骤"]:
                # 信息性检查，总是通过
                continue
            elif result:
                passed_checks += 1
                print(f"✓ {check_name} 通过")
            else:
                print(f"❌ {check_name} 失败")
        except Exception as e:
            print(f"❌ {check_name} 异常: {e}")
    
    # 显示总结
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    print(f"通过检查: {passed_checks}/{total_checks}")
    
    if passed_checks == total_checks:
        print("🎉 所有检查通过！字幕系统应该正常工作。")
        print("\n如果仍有问题，请:")
        print("• 检查具体的错误信息")
        print("• 尝试重启播放器")
        print("• 测试不同的视频文件")
    else:
        print("⚠️  发现问题，请根据上述信息进行修复。")
        print("\n常见解决方案:")
        print("• pip install faster-whisper moviepy")
        print("• 确保视频文件包含音频")
        print("• 检查网络连接")

if __name__ == "__main__":
    main()
