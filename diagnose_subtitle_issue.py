#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断字幕无法启用的问题
检查所有可能的原因并提供解决方案
"""

import sys
import os

def check_whisper_availability():
    """检查Whisper是否可用"""
    print("1. 检查faster-whisper可用性...")
    try:
        from faster_whisper import WhisperModel
        print("✓ faster-whisper 已安装")
        return True
    except ImportError as e:
        print(f"❌ faster-whisper 未安装: {e}")
        print("   解决方案: pip install faster-whisper")
        return False

def check_audio_extraction():
    """检查音频提取能力"""
    print("\n2. 检查音频提取能力...")
    
    # 检查moviepy
    moviepy_available = False
    try:
        from moviepy.editor import VideoFileClip
        print("✓ moviepy 可用")
        moviepy_available = True
    except ImportError:
        print("❌ moviepy 不可用")
        print("   解决方案: pip install moviepy")
    
    # 检查ffmpeg
    ffmpeg_available = False
    try:
        import subprocess
        result = subprocess.run(["ffmpeg", "-version"], 
                              capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✓ ffmpeg 可用")
            ffmpeg_available = True
        else:
            print("❌ ffmpeg 不可用")
    except:
        print("❌ ffmpeg 不可用")
        print("   解决方案: 下载ffmpeg并添加到PATH")
    
    return moviepy_available or ffmpeg_available

def check_basic_dependencies():
    """检查基础依赖"""
    print("\n3. 检查基础依赖...")
    
    dependencies = [
        ("cv2", "opencv-python"),
        ("numpy", "numpy"),
        ("PIL", "pillow"),
        ("tkinter", "tkinter")
    ]
    
    all_ok = True
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package} 未安装")
            all_ok = False
    
    return all_ok

def test_whisper_model_loading():
    """测试Whisper模型加载"""
    print("\n4. 测试Whisper模型加载...")
    
    try:
        from faster_whisper import WhisperModel
        print("正在尝试加载large-v3模型...")
        
        # 尝试加载模型
        model = WhisperModel("large-v3", device="cpu", compute_type="int8")
        print("✓ Whisper模型加载成功")
        
        # 测试转录功能
        print("测试转录功能...")
        # 这里可以添加一个简单的音频文件测试
        print("✓ 模型功能正常")
        return True
        
    except Exception as e:
        print(f"❌ Whisper模型加载失败: {e}")
        if "No module named" in str(e):
            print("   原因: faster-whisper未正确安装")
            print("   解决方案: pip install faster-whisper")
        elif "download" in str(e).lower() or "network" in str(e).lower():
            print("   原因: 网络问题，无法下载模型")
            print("   解决方案: 检查网络连接，或使用代理")
        elif "memory" in str(e).lower():
            print("   原因: 内存不足")
            print("   解决方案: 尝试使用更小的模型，如'base'或'small'")
        else:
            print(f"   原因: {e}")
        return False

def check_video_file_support():
    """检查视频文件支持"""
    print("\n5. 检查视频文件支持...")
    
    try:
        import cv2
        print("✓ OpenCV 可用于视频读取")
        
        # 检查支持的编解码器
        print("支持的视频格式:")
        formats = ['.mp4', '.avi', '.mov', '.mkv']
        for fmt in formats:
            print(f"  {fmt}")
        
        return True
    except ImportError:
        print("❌ OpenCV 不可用")
        return False

def create_test_environment():
    """创建测试环境"""
    print("\n6. 创建测试环境...")
    
    try:
        # 创建一个简单的测试音频文件
        import wave
        import numpy as np
        
        # 生成测试音频
        sample_rate = 16000
        duration = 2  # 2秒
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # 保存测试音频
        with wave.open('test_audio.wav', 'w') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        print("✓ 测试音频文件创建成功: test_audio.wav")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试环境失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n" + "="*60)
    print("解决方案总结")
    print("="*60)
    
    print("\n如果字幕无法启用，请按以下步骤操作：")
    
    print("\n步骤1: 安装必要依赖")
    print("pip install faster-whisper moviepy opencv-python pillow numpy")
    
    print("\n步骤2: 检查网络连接")
    print("首次使用时，Whisper需要下载模型文件（约1-3GB）")
    print("确保网络连接稳定")
    
    print("\n步骤3: 尝试使用更小的模型")
    print("如果large-v3模型加载失败，可以修改代码使用更小的模型：")
    print("将 'large-v3' 改为 'base' 或 'small'")
    
    print("\n步骤4: 检查视频文件")
    print("确保视频文件包含音频轨道")
    print("支持的格式: MP4, AVI, MOV等")
    
    print("\n步骤5: 查看控制台输出")
    print("运行播放器时查看控制台的错误信息")
    print("根据具体错误信息进行排查")
    
    print("\n步骤6: 使用演示模式")
    print("即使真实字幕不可用，播放器仍会显示演示字幕")
    print("可以用来测试字幕样式调整功能")

def main():
    """主函数"""
    print("MP4播放器字幕问题诊断工具")
    print("="*60)
    
    # 执行各项检查
    whisper_ok = check_whisper_availability()
    audio_ok = check_audio_extraction()
    deps_ok = check_basic_dependencies()
    video_ok = check_video_file_support()
    
    # 如果基础依赖都OK，尝试测试Whisper
    if whisper_ok and deps_ok:
        model_ok = test_whisper_model_loading()
    else:
        model_ok = False
    
    # 创建测试环境
    test_ok = create_test_environment()
    
    # 总结结果
    print("\n" + "="*60)
    print("诊断结果")
    print("="*60)
    
    print(f"faster-whisper: {'✓' if whisper_ok else '❌'}")
    print(f"音频提取: {'✓' if audio_ok else '❌'}")
    print(f"基础依赖: {'✓' if deps_ok else '❌'}")
    print(f"视频支持: {'✓' if video_ok else '❌'}")
    print(f"模型加载: {'✓' if model_ok else '❌'}")
    print(f"测试环境: {'✓' if test_ok else '❌'}")
    
    if all([whisper_ok, audio_ok, deps_ok, video_ok, model_ok]):
        print("\n🎉 所有检查通过！字幕功能应该可以正常工作。")
        print("如果仍然无法启用字幕，请检查：")
        print("1. 视频文件是否包含音频")
        print("2. 字幕开关是否已启用")
        print("3. 控制台是否有错误信息")
    else:
        print("\n⚠️ 发现问题，请根据上述检查结果进行修复。")
    
    # 提供解决方案
    provide_solutions()
    
    print(f"\n诊断完成。")

if __name__ == "__main__":
    main()
