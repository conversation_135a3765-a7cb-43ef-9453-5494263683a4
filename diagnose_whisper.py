#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Whisper 功能诊断工具
检查 faster-whisper 的安装和配置状态
"""

import sys
import os
import subprocess

def check_python_environment():
    """检查 Python 环境"""
    print("=" * 60)
    print("1. Python 环境检查")
    print("=" * 60)
    
    print(f"Python 版本: {sys.version}")
    print(f"Python 可执行文件: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ 运行在虚拟环境中")
        print(f"虚拟环境路径: {sys.prefix}")
    else:
        print("⚠️  可能不在虚拟环境中")

def check_faster_whisper_installation():
    """检查 faster-whisper 安装状态"""
    print("\n" + "=" * 60)
    print("2. faster-whisper 安装检查")
    print("=" * 60)
    
    try:
        import faster_whisper
        print(f"✓ faster-whisper 已安装")
        print(f"版本: {faster_whisper.__version__}")
        print(f"安装路径: {faster_whisper.__file__}")
        return True
    except ImportError as e:
        print(f"❌ faster-whisper 未安装: {e}")
        return False
    except Exception as e:
        print(f"❌ faster-whisper 导入错误: {e}")
        return False

def check_whisper_model_import():
    """检查 WhisperModel 导入"""
    print("\n" + "=" * 60)
    print("3. WhisperModel 导入检查")
    print("=" * 60)
    
    try:
        from faster_whisper import WhisperModel
        print("✓ WhisperModel 导入成功")
        
        # 检查可用的模型
        available_models = ['tiny', 'base', 'small', 'medium', 'large-v1', 'large-v2', 'large-v3']
        print(f"可用模型: {', '.join(available_models)}")
        return True
    except ImportError as e:
        print(f"❌ WhisperModel 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ WhisperModel 导入错误: {e}")
        return False

def check_dependencies():
    """检查相关依赖"""
    print("\n" + "=" * 60)
    print("4. 依赖检查")
    print("=" * 60)
    
    dependencies = [
        'ctranslate2',
        'huggingface_hub',
        'tokenizers',
        'onnxruntime'
    ]
    
    for dep in dependencies:
        try:
            module = __import__(dep)
            version = getattr(module, '__version__', '未知版本')
            print(f"✓ {dep}: {version}")
        except ImportError:
            print(f"❌ {dep}: 未安装")
        except Exception as e:
            print(f"⚠️  {dep}: {e}")

def test_model_loading():
    """测试模型加载"""
    print("\n" + "=" * 60)
    print("5. 模型加载测试")
    print("=" * 60)
    
    try:
        from faster_whisper import WhisperModel
        
        print("尝试加载 tiny 模型（最小模型，用于测试）...")
        
        # 使用最小的模型进行测试
        model = WhisperModel("tiny", device="cpu", compute_type="int8")
        print("✓ tiny 模型加载成功")
        
        # 清理模型
        del model
        print("✓ 模型清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        
        # 提供详细的错误信息
        if "download" in str(e).lower():
            print("   可能原因: 网络问题，无法下载模型")
            print("   解决方案: 检查网络连接，或使用代理")
        elif "memory" in str(e).lower():
            print("   可能原因: 内存不足")
            print("   解决方案: 关闭其他程序释放内存")
        elif "cuda" in str(e).lower():
            print("   可能原因: CUDA 相关问题")
            print("   解决方案: 使用 CPU 模式")
        else:
            print(f"   详细错误: {e}")
        
        return False

def check_audio_processing():
    """检查音频处理能力"""
    print("\n" + "=" * 60)
    print("6. 音频处理能力检查")
    print("=" * 60)
    
    # 检查音频相关库
    audio_libs = {
        'numpy': '数值计算',
        'scipy': '科学计算（可选）',
        'librosa': '音频处理（可选）',
        'soundfile': '音频文件读写（可选）'
    }
    
    for lib, desc in audio_libs.items():
        try:
            module = __import__(lib)
            version = getattr(module, '__version__', '未知版本')
            print(f"✓ {lib} ({desc}): {version}")
        except ImportError:
            if lib == 'numpy':
                print(f"❌ {lib} ({desc}): 未安装 - 必需")
            else:
                print(f"⚠️  {lib} ({desc}): 未安装 - 可选")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("7. 解决方案建议")
    print("=" * 60)
    
    solutions = [
        {
            "问题": "faster-whisper 未安装",
            "解决方案": [
                "pip install faster-whisper",
                "或者: pip install faster-whisper --upgrade"
            ]
        },
        {
            "问题": "模型下载失败",
            "解决方案": [
                "检查网络连接",
                "使用代理: pip install faster-whisper --proxy http://proxy:port",
                "手动下载模型文件"
            ]
        },
        {
            "问题": "内存不足",
            "解决方案": [
                "使用更小的模型: 'tiny' 或 'base'",
                "关闭其他程序释放内存",
                "使用 compute_type='int8' 减少内存使用"
            ]
        },
        {
            "问题": "虚拟环境问题",
            "解决方案": [
                "确保在正确的虚拟环境中运行",
                "激活虚拟环境: source .venv/Scripts/activate",
                "重新安装: pip install faster-whisper"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n{i}. {solution['问题']}:")
        for j, step in enumerate(solution['解决方案'], 1):
            print(f"   {j}) {step}")

def main():
    """主函数"""
    print("Whisper 功能诊断工具")
    print("用于诊断 faster-whisper 相关问题")
    
    # 运行所有检查
    checks = [
        ("Python 环境", check_python_environment),
        ("faster-whisper 安装", check_faster_whisper_installation),
        ("WhisperModel 导入", check_whisper_model_import),
        ("依赖检查", check_dependencies),
        ("模型加载测试", test_model_loading),
        ("音频处理能力", check_audio_processing)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            results.append((check_name, False))
    
    # 显示总结
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len([r for r in results if r[1] is not None])
    
    print(f"通过检查: {passed}/{total}")
    
    for check_name, result in results:
        if result is True:
            print(f"✓ {check_name}")
        elif result is False:
            print(f"❌ {check_name}")
        else:
            print(f"- {check_name}")
    
    if passed < total:
        print(f"\n⚠️  发现 {total - passed} 个问题")
        provide_solutions()
    else:
        print("\n🎉 所有检查通过！Whisper 功能应该可以正常使用。")

if __name__ == "__main__":
    main()
