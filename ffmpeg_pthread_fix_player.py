#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FFmpeg pthread修复播放器 - 专门解决pthread_frame.c:173错误
解决: Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173
"""

import os
import sys

# 🛡️ 在导入OpenCV之前设置所有必要的环境变量
print("🔧 设置FFmpeg安全环境变量...")

# 完全禁用MSMF
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'

# 🔧 专门解决pthread_frame.c:173错误的环境变量
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1'
os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'

# FFmpeg线程安全设置
os.environ['FFMPEG_THREAD_SAFE'] = '1'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'

# 禁用硬件加速，避免线程冲突
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'

print("✅ FFmpeg安全环境变量设置完成")

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import queue
import gc

class FFmpegPthreadFixPlayer:
    def __init__(self):
        print("🚀 启动FFmpeg pthread修复播放器...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("FFmpeg pthread修复播放器 - 解决pthread_frame.c:173错误")
        self.root.geometry("900x700")
        
        # 设置变量
        self.running = True
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.play_thread = None
        
        # 监控数据
        self.start_time = time.time()
        self.frame_count = 0
        self.error_count = 0
        self.pthread_error_count = 0
        self.recovery_count = 0
        
        # 创建界面
        self.create_ui()
        
        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 启动监控
        self.start_monitoring()
        
        print("✅ FFmpeg pthread修复播放器初始化完成")
    
    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightsteelblue")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题
        title_label = tk.Label(main_frame, text="FFmpeg pthread修复播放器", 
                              font=("Arial", 18, "bold"), bg="lightsteelblue")
        title_label.pack(pady=10)
        
        # 问题解决说明
        solution_frame = tk.LabelFrame(main_frame, text="pthread_frame.c:173 错误修复", font=("Arial", 12))
        solution_frame.pack(fill=tk.X, pady=5)
        
        solution_text = """🔧 专门修复: Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173
❌ 问题: FFmpeg多线程异步锁定冲突
✅ 解决: 强制单线程模式 + 线程安全设置
🛡️ 保护: 环境变量 + 专门的pthread错误处理"""
        
        tk.Label(solution_frame, text=solution_text, font=("Arial", 10), 
                justify=tk.LEFT, bg="lightyellow").pack(padx=10, pady=10, fill=tk.X)
        
        # 状态显示
        status_frame = tk.LabelFrame(main_frame, text="运行状态", font=("Arial", 12))
        status_frame.pack(fill=tk.X, pady=5)
        
        info_frame = tk.Frame(status_frame)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.runtime_var = tk.StringVar(value="运行时间: 0秒")
        tk.Label(info_frame, textvariable=self.runtime_var, 
                font=("Arial", 14, "bold"), fg="blue").pack(anchor=tk.W)
        
        self.stats_var = tk.StringVar(value="统计: 帧数=0, 错误=0, pthread错误=0")
        tk.Label(info_frame, textvariable=self.stats_var, 
                font=("Arial", 12)).pack(anchor=tk.W)
        
        self.pthread_var = tk.StringVar(value="🔧 pthread保护: 已激活")
        tk.Label(info_frame, textvariable=self.pthread_var, 
                font=("Arial", 12), fg="green").pack(anchor=tk.W)
        
        # 视频控制
        video_frame = tk.LabelFrame(main_frame, text="视频播放", font=("Arial", 12))
        video_frame.pack(fill=tk.X, pady=5)
        
        control_frame = tk.Frame(video_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Button(control_frame, text="📁 选择视频", font=("Arial", 12),
                 command=self.select_video, bg="lightblue").pack(side=tk.LEFT, padx=5)
        
        self.play_btn = tk.Button(control_frame, text="▶️ 开始播放", font=("Arial", 12),
                                 command=self.toggle_play, state="disabled", bg="orange")
        self.play_btn.pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="⏹️ 停止", font=("Arial", 12),
                 command=self.stop_play, bg="red", fg="white").pack(side=tk.LEFT, padx=5)
        
        self.video_info_var = tk.StringVar(value="未选择视频文件")
        tk.Label(control_frame, textvariable=self.video_info_var, 
                font=("Arial", 10)).pack(side=tk.LEFT, padx=10)
        
        # 日志显示
        log_frame = tk.LabelFrame(main_frame, text="详细日志", font=("Arial", 12))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(text_frame, font=("Consolas", 10), wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 控制按钮
        button_frame = tk.Frame(main_frame, bg="lightsteelblue")
        button_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(button_frame, text="🧹 清理内存", command=self.cleanup_memory).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="🔄 pthread恢复", command=self.pthread_recovery).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="💾 保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="❌ 关闭", command=self.manual_close, 
                 bg="red", fg="white").pack(side=tk.RIGHT, padx=5)
        
        # 添加初始日志
        self.add_log("✅ FFmpeg pthread修复播放器启动成功")
        self.add_log("🔧 已修复: pthread_frame.c:173 异步锁定错误")
        self.add_log("🛡️ 保护: 强制单线程 + 线程安全设置")
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        runtime = int(time.time() - self.start_time)
        log_entry = f"[{timestamp}] [{runtime:04d}s] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 150:
            self.log_text.delete("1.0", "30.0")
        
        print(log_entry.strip())
    
    def select_video(self):
        """选择视频"""
        try:
            self.add_log("📁 打开文件选择对话框")
            
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_info_var.set(f"已选择: {filename}")
                self.add_log(f"✅ 选择视频: {filename}")
                
                # 测试视频
                self.test_video_pthread_safe()
            else:
                self.add_log("❌ 用户取消文件选择")
                
        except Exception as e:
            self.add_log(f"❌ 选择视频失败: {e}")
            self.error_count += 1
    
    def test_video_pthread_safe(self):
        """pthread安全的视频测试"""
        if not self.video_path:
            return
        
        try:
            self.add_log("🔧 开始pthread安全视频测试")
            
            # 创建pthread安全的capture
            cap = self.create_pthread_safe_capture()
            
            if cap and cap.isOpened():
                # 获取视频信息
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                # 测试读取帧（这里最容易出现pthread错误）
                test_frames = min(50, frame_count)
                success_reads = 0
                
                self.add_log(f"🧪 测试读取 {test_frames} 帧...")
                
                for i in range(test_frames):
                    try:
                        ret, frame = cap.read()
                        if ret and frame is not None:
                            success_reads += 1
                        else:
                            break
                    except Exception as read_error:
                        error_msg = str(read_error)
                        if "pthread_frame.c" in error_msg:
                            self.add_log(f"⚠️ 检测到pthread错误: {error_msg}")
                            self.pthread_error_count += 1
                        break
                
                cap.release()
                
                if success_reads >= test_frames * 0.8:  # 80%成功率
                    duration = frame_count / fps if fps > 0 else 0
                    self.add_log(f"✅ pthread安全测试通过")
                    self.add_log(f"   视频信息: {width}x{height}, {fps:.1f}fps")
                    self.add_log(f"   总帧数: {frame_count}, 时长: {duration:.1f}秒")
                    self.add_log(f"   读取测试: {success_reads}/{test_frames} 成功")
                    
                    self.play_btn.configure(state="normal")
                else:
                    self.add_log(f"❌ pthread安全测试失败: {success_reads}/{test_frames}")
                    self.error_count += 1
            else:
                self.add_log("❌ 无法创建pthread安全的capture")
                self.error_count += 1
                
        except Exception as e:
            error_msg = str(e)
            if "pthread_frame.c" in error_msg:
                self.add_log(f"❌ pthread测试中出现pthread错误: {error_msg}")
                self.pthread_error_count += 1
            else:
                self.add_log(f"❌ pthread安全测试失败: {e}")
            self.error_count += 1
    
    def create_pthread_safe_capture(self):
        """创建pthread安全的VideoCapture"""
        try:
            self.add_log("🔧 创建pthread安全的VideoCapture...")
            
            # 确保环境变量设置正确
            os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1'
            
            # 创建单线程FFMPEG capture
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
            
            if cap.isOpened():
                # 设置单线程参数
                try:
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
                    # 注意：不要设置线程相关的属性，因为这可能触发pthread错误
                except Exception as e:
                    self.add_log(f"⚠️ 设置capture参数时出错: {e}")
                
                self.add_log("✅ pthread安全capture创建成功")
                return cap
            else:
                self.add_log("❌ pthread安全capture创建失败")
                return None
                
        except Exception as e:
            error_msg = str(e)
            if "pthread_frame.c" in error_msg:
                self.add_log(f"❌ 创建capture时出现pthread错误: {error_msg}")
                self.pthread_error_count += 1
            else:
                self.add_log(f"❌ 创建pthread安全capture失败: {e}")
            return None

    def toggle_play(self):
        """切换播放"""
        if not self.video_path:
            return

        if not self.is_playing:
            self.start_play()
        else:
            self.stop_play()

    def start_play(self):
        """开始播放"""
        try:
            self.add_log("🎬 开始pthread安全播放")
            self.is_playing = True
            self.play_btn.configure(text="⏸️ 暂停播放")

            # 启动播放线程
            self.play_thread = threading.Thread(target=self.pthread_safe_play_loop, daemon=True)
            self.play_thread.start()

        except Exception as e:
            self.add_log(f"❌ 启动播放失败: {e}")
            self.error_count += 1

    def pthread_safe_play_loop(self):
        """pthread安全的播放循环"""
        try:
            self.add_log("🛡️ 播放线程启动（pthread安全模式）")

            # 创建pthread安全的capture
            cap = self.create_pthread_safe_capture()
            if not cap or not cap.isOpened():
                self.root.after(0, lambda: self.add_log("❌ 无法创建pthread安全的capture"))
                return

            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                fps = 30

            frame_delay = 1.0 / fps
            local_frame_count = 0
            consecutive_errors = 0
            pthread_errors = 0

            self.root.after(0, lambda: self.add_log(f"📊 播放参数: FPS={fps:.1f}, pthread安全模式"))

            while self.is_playing and self.running:
                try:
                    start_time = time.time()

                    # pthread安全的帧读取
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        local_frame_count += 1
                        self.frame_count += 1
                        consecutive_errors = 0  # 重置错误计数

                        # 每1000帧报告一次
                        if local_frame_count % 1000 == 0:
                            self.root.after(0, lambda lfc=local_frame_count:
                                          self.add_log(f"📊 播放进度: {lfc}帧"))

                        # 控制播放速度
                        elapsed = time.time() - start_time
                        sleep_time = frame_delay - elapsed
                        if sleep_time > 0:
                            time.sleep(sleep_time)
                    else:
                        # 视频结束，安全重新开始
                        self.root.after(0, lambda: self.add_log("🔄 视频播放完毕，pthread安全重启"))

                        # 安全地重新创建capture
                        cap.release()
                        time.sleep(0.3)  # 等待资源释放

                        cap = self.create_pthread_safe_capture()
                        if not cap or not cap.isOpened():
                            self.root.after(0, lambda: self.add_log("❌ pthread安全重启失败"))
                            break

                        local_frame_count = 0

                except Exception as e:
                    consecutive_errors += 1
                    error_msg = str(e)

                    # 专门检查pthread_frame.c错误
                    if "pthread_frame.c" in error_msg or "async_lock" in error_msg:
                        pthread_errors += 1
                        self.pthread_error_count += 1
                        self.root.after(0, lambda: self.add_log(f"🚨 检测到pthread_frame.c错误 (第{pthread_errors}次)"))

                        # pthread专门恢复
                        recovery_success = self.handle_pthread_error(cap)
                        if recovery_success:
                            cap = self.create_pthread_safe_capture()
                            if cap and cap.isOpened():
                                consecutive_errors = 0
                                self.root.after(0, lambda: self.add_log("✅ pthread错误恢复成功"))
                                continue

                        if pthread_errors >= 3:
                            self.root.after(0, lambda: self.add_log("❌ pthread错误过多，停止播放"))
                            break
                    else:
                        self.root.after(0, lambda err=error_msg:
                                      self.add_log(f"❌ 播放错误: {err}"))
                        self.error_count += 1

                        if consecutive_errors >= 10:
                            self.root.after(0, lambda: self.add_log("❌ 错误过多，停止播放"))
                            break

                    time.sleep(0.2)  # 错误后等待

            cap.release()
            self.root.after(0, lambda: self.add_log("✅ pthread安全播放线程结束"))

        except Exception as e:
            error_msg = str(e)
            if "pthread_frame.c" in error_msg:
                self.root.after(0, lambda: self.add_log(f"🚨 播放线程pthread错误: {error_msg}"))
                self.pthread_error_count += 1
            else:
                self.root.after(0, lambda err=error_msg:
                              self.add_log(f"❌ 播放线程异常: {err}"))
            self.error_count += 1
        finally:
            self.root.after(0, lambda: self.play_btn.configure(text="▶️ 开始播放"))

    def handle_pthread_error(self, current_cap):
        """处理pthread_frame.c错误"""
        try:
            self.add_log("🔧 开始pthread错误恢复...")

            # 1. 立即释放当前capture
            if current_cap:
                try:
                    current_cap.release()
                except:
                    pass

            # 2. 强制垃圾回收
            gc.collect()

            # 3. 重新设置环境变量
            os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1'
            os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'

            # 4. 等待更长时间
            time.sleep(0.5)

            self.add_log("✅ pthread错误恢复完成")
            return True

        except Exception as e:
            self.add_log(f"❌ pthread错误恢复失败: {e}")
            return False

    def stop_play(self):
        """停止播放"""
        self.add_log("⏹️ 停止播放")
        self.is_playing = False
        self.play_btn.configure(text="▶️ 开始播放")

        if self.play_thread and self.play_thread.is_alive():
            self.add_log("⏳ 等待播放线程结束...")
            self.play_thread.join(timeout=3.0)

    def pthread_recovery(self):
        """pthread专门恢复"""
        try:
            self.recovery_count += 1
            self.add_log(f"🔄 启动pthread专门恢复 (第{self.recovery_count}次)")

            # 停止播放
            self.is_playing = False

            # 强制垃圾回收
            gc.collect()

            # 重新设置所有pthread相关环境变量
            os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1'
            os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
            os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
            os.environ['FFMPEG_THREAD_SAFE'] = '1'

            time.sleep(1)

            # 重新测试视频
            if self.video_path:
                self.test_video_pthread_safe()

            self.add_log(f"✅ pthread专门恢复完成")

        except Exception as e:
            self.add_log(f"❌ pthread专门恢复失败: {e}")

    def start_monitoring(self):
        """启动监控"""
        def monitor():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)

                    # 更新显示
                    self.runtime_var.set(f"运行时间: {runtime}秒 ({runtime//60}分{runtime%60}秒)")
                    self.stats_var.set(f"统计: 帧数={self.frame_count}, 错误={self.error_count}, pthread错误={self.pthread_error_count}")

                    # pthread状态
                    if self.pthread_error_count == 0:
                        self.pthread_var.set("🔧 pthread保护: 正常运行")
                    else:
                        self.pthread_var.set(f"⚠️ pthread保护: 检测到{self.pthread_error_count}次错误")

                    # 每2分钟报告状态
                    if runtime > 0 and runtime % 120 == 0:
                        self.add_log(f"💓 pthread安全运行 - {runtime//60}分钟, 处理{self.frame_count}帧, pthread错误{self.pthread_error_count}次")

                    # 继续监控
                    self.root.after(1000, monitor)

                except Exception as e:
                    print(f"监控错误: {e}")
                    self.root.after(5000, monitor)

        monitor()
        self.add_log("💓 监控系统启动")

    def cleanup_memory(self):
        """清理内存"""
        try:
            before = len(gc.get_objects())
            collected = gc.collect()
            after = len(gc.get_objects())
            self.add_log(f"🧹 内存清理: 回收{collected}个对象, 对象数量 {before} -> {after}")
        except Exception as e:
            self.add_log(f"❌ 内存清理失败: {e}")

    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get("1.0", tk.END)
            timestamp = int(time.time())
            filename = f"pthread_fix_log_{timestamp}.txt"

            with open(filename, "w", encoding="utf-8") as f:
                f.write(f"FFmpeg pthread修复播放器日志\n")
                f.write(f"问题: pthread_frame.c:173 async_lock错误\n")
                f.write(f"解决: 强制单线程 + 线程安全设置\n")
                f.write(f"开始时间: {time.ctime(self.start_time)}\n")
                f.write(f"运行时长: {int(time.time() - self.start_time)}秒\n")
                f.write(f"处理帧数: {self.frame_count}\n")
                f.write(f"错误次数: {self.error_count}\n")
                f.write(f"pthread错误: {self.pthread_error_count}\n")
                f.write(f"恢复次数: {self.recovery_count}\n")
                f.write("=" * 50 + "\n")
                f.write(log_content)

            self.add_log(f"💾 日志已保存: {filename}")
        except Exception as e:
            self.add_log(f"❌ 保存日志失败: {e}")

    def manual_close(self):
        """手动关闭"""
        self.add_log("🚪 用户请求关闭")

        runtime = int(time.time() - self.start_time)
        result = messagebox.askyesno("确认关闭",
                                   f"确定要关闭pthread修复播放器吗？\n\n"
                                   f"运行时间: {runtime//60}分{runtime%60}秒\n"
                                   f"处理帧数: {self.frame_count}\n"
                                   f"pthread错误: {self.pthread_error_count}次")
        if result:
            self.add_log("✅ 用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")

    def on_closing(self):
        """窗口关闭处理"""
        self.add_log("⚠️ 检测到窗口关闭事件")

        result = messagebox.askyesno("确认关闭",
                                   "真的要关闭pthread修复播放器吗？\n\n"
                                   "这个版本专门修复了pthread_frame.c错误")
        if result:
            self.add_log("✅ 用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")

    def cleanup(self):
        """清理资源"""
        self.add_log("🧹 开始清理资源...")
        self.running = False
        self.is_playing = False

        if self.cap:
            try:
                self.cap.release()
                self.add_log("✅ 视频资源已释放")
            except:
                self.add_log("⚠️ 视频资源释放失败")

        if self.play_thread and self.play_thread.is_alive():
            self.add_log("⏳ 等待播放线程结束...")
            self.play_thread.join(timeout=3.0)

        self.add_log("✅ 资源清理完成")

    def run(self):
        """运行播放器"""
        try:
            print("🎬 启动pthread修复播放器界面...")
            print("🔧 专门修复pthread_frame.c:173错误")
            print("🛡️ 强制单线程模式 + 线程安全设置")

            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    filename = os.path.basename(video_file)
                    self.video_info_var.set(f"命令行: {filename}")
                    self.add_log(f"📁 命令行参数: {filename}")
                    self.root.after(1000, self.test_video_pthread_safe)
                else:
                    self.add_log(f"❌ 命令行文件不存在: {video_file}")

            # 启动主循环
            self.root.mainloop()

            print("🔚 pthread修复播放器正常结束")

        except Exception as e:
            print(f"❌ 播放器异常: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 FFmpeg pthread修复播放器")
    print("=" * 60)
    print("问题: Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173")
    print("原因: FFmpeg多线程异步锁定冲突")
    print("解决: 强制单线程模式 + 专门的pthread错误处理")
    print("保护: 环境变量 + 错误检测 + 自动恢复")
    print("=" * 60)

    try:
        player = FFmpegPthreadFixPlayer()
        player.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
