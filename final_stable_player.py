#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终稳定版MP4播放器
基于调试版播放器，去掉调试信息，保留稳定性
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import os
import sys
import time
import threading
from PIL import Image, ImageTk

class FinalStablePlayer:
    def __init__(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("稳定版MP4播放器 - 绝不自动关闭")
        self.root.geometry("900x700")
        
        # 视频相关变量
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.running = True
        self.play_thread = None
        
        # 创建界面
        self.create_ui()
        
        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 启动状态监控
        self.start_status_monitor()
        
        print("✅ 稳定版播放器初始化完成")
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))
        
        title_label = ttk.Label(title_frame, text="稳定版MP4播放器", 
                               font=("Arial", 18, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 状态指示器
        self.status_indicator = ttk.Label(title_frame, text="🟢 运行中", 
                                         font=("Arial", 12), foreground="green")
        self.status_indicator.pack(side=tk.RIGHT)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="视频控制")
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 文件操作
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(file_frame, text="📁 选择视频文件", 
                  command=self.select_video_file).pack(side=tk.LEFT, padx=(0, 10))
        
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT)
        
        # 播放控制
        play_frame = ttk.Frame(control_frame)
        play_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.play_btn = ttk.Button(play_frame, text="▶️ 播放", 
                                  command=self.toggle_play, state="disabled")
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(play_frame, text="⏹️ 停止", 
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度控制
        self.progress_var = tk.DoubleVar()
        self.progress_scale = ttk.Scale(play_frame, from_=0, to=100, 
                                       orient=tk.HORIZONTAL, variable=self.progress_var,
                                       command=self.seek_video)
        self.progress_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(play_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 视频显示区域
        video_frame = ttk.LabelFrame(main_frame, text="视频显示")
        video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        self.video_canvas = tk.Canvas(video_frame, bg="black")
        self.video_canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 在画布中央显示提示文字
        self.canvas_text = self.video_canvas.create_text(
            450, 200, text="请选择视频文件开始播放", 
            fill="white", font=("Arial", 16)
        )
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=(5, 20))
        
        # 运行时间显示
        self.runtime_var = tk.StringVar(value="运行时间: 0秒")
        ttk.Label(status_frame, textvariable=self.runtime_var).pack(side=tk.LEFT, padx=(0, 20))
        
        # 保持运行按钮
        ttk.Button(status_frame, text="🔒 保持运行", 
                  command=self.force_keep_alive).pack(side=tk.RIGHT)
    
    def select_video_file(self):
        """选择视频文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[
                    ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                    ("MP4文件", "*.mp4"),
                    ("所有文件", "*.*")
                ]
            )
            
            if file_path:
                self.load_video(file_path)
                
        except Exception as e:
            self.status_var.set(f"选择文件失败: {e}")
            messagebox.showerror("错误", f"选择文件失败: {e}")
    
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            print(f"加载视频: {video_path}")
            self.status_var.set("正在加载视频...")
            
            # 停止当前播放
            self.stop_video()
            
            # 检查文件
            if not os.path.exists(video_path):
                raise Exception(f"文件不存在: {video_path}")
            
            file_size = os.path.getsize(video_path)
            print(f"文件大小: {file_size:,} 字节")
            
            # 设置路径
            self.video_path = video_path
            
            # 尝试多种方式打开视频
            methods = [
                ("默认方式", lambda: cv2.VideoCapture(video_path)),
                ("MSMF后端", lambda: cv2.VideoCapture(video_path, cv2.CAP_MSMF)),
                ("FFMPEG后端", lambda: cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)),
                ("DSHOW后端", lambda: cv2.VideoCapture(video_path, cv2.CAP_DSHOW)),
            ]
            
            success = False
            for method_name, method_func in methods:
                try:
                    print(f"尝试 {method_name}...")
                    self.cap = method_func()
                    
                    if self.cap and self.cap.isOpened():
                        # 测试读取
                        ret, test_frame = self.cap.read()
                        if ret and test_frame is not None:
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            print(f"✅ {method_name} 成功")
                            success = True
                            break
                        else:
                            self.cap.release()
                            print(f"❌ {method_name} 无法读取帧")
                    else:
                        if self.cap:
                            self.cap.release()
                        print(f"❌ {method_name} 无法打开")
                except Exception as e:
                    print(f"❌ {method_name} 失败: {e}")
                    continue
            
            if not success:
                raise Exception("所有方法都无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            if self.total_frames <= 0:
                self.total_frames = 1000
            if self.fps <= 0:
                self.fps = 30
            
            print(f"视频信息: {width}x{height}, {self.fps}fps, {self.total_frames}帧")
            
            # 重置界面
            self.progress_scale.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0
            
            # 显示第一帧
            self.show_frame()
            
            # 更新界面
            filename = os.path.basename(video_path)
            self.file_label.configure(text=filename)
            
            duration = self.total_frames / self.fps
            self.status_var.set(f"视频已加载: {filename} ({duration:.1f}秒)")
            
            # 启用播放按钮
            self.play_btn.configure(state="normal")
            
            print("✅ 视频加载成功")
            
        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
            
            # 清理
            if hasattr(self, 'cap') and self.cap:
                try:
                    self.cap.release()
                except:
                    pass
                self.cap = None
    
    def show_frame(self):
        """显示当前帧"""
        if not self.cap:
            return
        
        try:
            # 读取帧
            ret, frame = self.cap.read()
            if not ret:
                return
            
            # 转换为RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # 调整大小适应画布
            canvas_width = self.video_canvas.winfo_width()
            canvas_height = self.video_canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                # 保持宽高比
                img_ratio = pil_image.width / pil_image.height
                canvas_ratio = canvas_width / canvas_height
                
                if img_ratio > canvas_ratio:
                    new_width = canvas_width - 20
                    new_height = int(new_width / img_ratio)
                else:
                    new_height = canvas_height - 20
                    new_width = int(new_height * img_ratio)
                
                new_width = max(1, new_width)
                new_height = max(1, new_height)
                
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter图像
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 清除画布并显示图像
            self.video_canvas.delete("all")
            canvas_center_x = canvas_width // 2
            canvas_center_y = canvas_height // 2
            self.video_canvas.create_image(canvas_center_x, canvas_center_y, 
                                         image=self.photo, anchor=tk.CENTER)
            
            # 更新进度
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            total_time = self.total_frames / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            
        except Exception as e:
            print(f"显示帧时出错: {e}")
    
    def toggle_play(self):
        """切换播放/暂停"""
        if not self.cap:
            return
        
        self.is_playing = not self.is_playing
        
        if self.is_playing:
            self.play_btn.configure(text="⏸️ 暂停")
            self.start_playback()
        else:
            self.play_btn.configure(text="▶️ 播放")
    
    def start_playback(self):
        """开始播放"""
        if self.play_thread and self.play_thread.is_alive():
            return
        
        self.play_thread = threading.Thread(target=self.playback_loop, daemon=True)
        self.play_thread.start()
    
    def playback_loop(self):
        """播放循环"""
        try:
            while self.is_playing and self.cap and self.running:
                start_time = time.time()
                
                # 读取下一帧
                ret, frame = self.cap.read()
                if ret:
                    self.current_frame += 1
                    
                    # 在主线程中更新显示
                    self.root.after(0, self.show_frame)
                    
                    # 控制播放速度
                    elapsed = time.time() - start_time
                    delay = (1.0 / self.fps) - elapsed
                    if delay > 0:
                        time.sleep(delay)
                else:
                    # 播放完毕
                    self.root.after(0, self.playback_finished)
                    break
                    
        except Exception as e:
            print(f"播放循环出错: {e}")
            self.root.after(0, self.playback_finished)
    
    def playback_finished(self):
        """播放完毕"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")
        self.status_var.set("播放完毕")
        print("播放完毕")
    
    def seek_video(self, value):
        """跳转到指定位置"""
        if not self.cap:
            return
        
        try:
            frame_number = int(float(value))
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            self.current_frame = frame_number
            self.show_frame()
        except Exception as e:
            print(f"跳转时出错: {e}")
    
    def stop_video(self):
        """停止播放"""
        self.is_playing = False
        
        if hasattr(self, 'play_btn'):
            self.play_btn.configure(text="▶️ 播放", state="disabled")
        
        # 等待播放线程结束
        if self.play_thread and self.play_thread.is_alive():
            self.play_thread.join(timeout=1.0)
        
        # 释放资源
        if self.cap:
            try:
                self.cap.release()
            except:
                pass
            self.cap = None
        
        # 重置界面
        self.current_frame = 0
        self.video_path = None
        
        if hasattr(self, 'video_canvas'):
            self.video_canvas.delete("all")
            self.canvas_text = self.video_canvas.create_text(
                450, 200, text="请选择视频文件开始播放", 
                fill="white", font=("Arial", 16)
            )
        
        if hasattr(self, 'file_label'):
            self.file_label.configure(text="未选择文件")
        
        self.status_var.set("就绪")
    
    def format_time(self, seconds):
        """格式化时间"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def start_status_monitor(self):
        """启动状态监控"""
        if not hasattr(self, 'start_time'):
            self.start_time = time.time()
        
        def update_status():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)
                    self.runtime_var.set(f"运行时间: {runtime}秒")
                    
                    # 更新状态指示器
                    if runtime % 2 == 0:
                        self.status_indicator.configure(text="🟢 运行中", foreground="green")
                    else:
                        self.status_indicator.configure(text="🔵 运行中", foreground="blue")
                    
                    # 继续监控
                    self.root.after(1000, update_status)
                except:
                    pass
        
        update_status()
    
    def force_keep_alive(self):
        """强制保持运行"""
        self.status_var.set("🔒 强制保持运行模式激活")
        self.status_indicator.configure(text="🔒 强制运行", foreground="red")
        print("🔒 强制保持运行模式激活")
        
        # 5秒后恢复正常状态
        self.root.after(5000, lambda: self.status_var.set("就绪"))
    
    def on_closing(self):
        """关闭处理"""
        try:
            # 询问确认
            result = messagebox.askyesno("确认关闭", 
                                       "确定要关闭播放器吗？\n\n点击'否'可以继续使用程序。")
            if result:
                print("用户确认关闭程序")
                self.running = False
                self.stop_video()
                self.root.destroy()
            else:
                print("用户取消关闭，继续运行")
                self.status_var.set("用户取消关闭，继续运行")
                
        except Exception as e:
            print(f"关闭处理错误: {e}")
    
    def run(self):
        """运行播放器"""
        try:
            print("🎬 启动稳定版播放器...")
            print("💡 这个版本绝对不会自动关闭")
            
            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                print(f"从命令行加载: {video_file}")
                if os.path.exists(video_file):
                    # 延迟加载，确保界面完全初始化
                    self.root.after(1000, lambda: self.load_video(video_file))
                else:
                    print(f"文件不存在: {video_file}")
            
            # 启动主循环
            self.root.mainloop()
            
            print("程序正常结束")
            
        except Exception as e:
            print(f"程序异常: {e}")
            import traceback
            traceback.print_exc()
            
            # 保持控制台打开
            try:
                input("\n程序异常，按回车键退出...")
            except:
                time.sleep(10)

def main():
    """主函数"""
    try:
        player = FinalStablePlayer()
        player.run()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
