#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复程序自动关闭问题的工具
分析并提供解决方案
"""

import sys
import os
import traceback
import time

def analyze_auto_close_issue():
    """分析自动关闭问题"""
    print("🔍 程序自动关闭问题分析")
    print("=" * 50)
    
    issues_found = []
    solutions = []
    
    # 1. 检查Python环境
    print("1. Python环境检查:")
    print(f"   Python版本: {sys.version}")
    print(f"   Python路径: {sys.executable}")
    
    # 2. 检查关键依赖
    print("\n2. 关键依赖检查:")
    dependencies = [
        ("tkinter", "GUI框架"),
        ("cv2", "OpenCV视频处理"),
        ("PIL", "图像处理"),
        ("numpy", "数值计算"),
    ]
    
    missing_deps = []
    for dep_name, desc in dependencies:
        try:
            __import__(dep_name)
            print(f"   ✅ {dep_name} ({desc}) - 可用")
        except ImportError as e:
            print(f"   ❌ {dep_name} ({desc}) - 缺失: {e}")
            missing_deps.append(dep_name)
    
    if missing_deps:
        issues_found.append(f"缺失依赖: {', '.join(missing_deps)}")
        solutions.append(f"安装缺失依赖: pip install {' '.join(missing_deps)}")
    
    # 3. 检查线程问题
    print("\n3. 线程安全检查:")
    try:
        import threading
        import tkinter as tk
        
        # 测试基本线程安全
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        test_var = tk.StringVar()
        
        def thread_test():
            try:
                # 这应该会失败，因为不在主线程
                test_var.set("test")
                return False
            except Exception:
                return True  # 预期的异常
        
        thread = threading.Thread(target=thread_test)
        thread.start()
        thread.join()
        
        root.destroy()
        print("   ✅ 线程安全检查通过")
        
    except Exception as e:
        print(f"   ❌ 线程安全检查失败: {e}")
        issues_found.append("线程安全问题")
        solutions.append("使用root.after()进行线程安全的UI更新")
    
    # 4. 检查异常处理
    print("\n4. 异常处理检查:")
    common_issues = [
        "faster-whisper导入失败",
        "Tkinter变量在线程中更新",
        "OpenCV VideoCapture错误",
        "资源清理不完整"
    ]
    
    for issue in common_issues:
        print(f"   ⚠️  常见问题: {issue}")
    
    # 5. 提供解决方案
    print("\n" + "=" * 50)
    print("🔧 解决方案")
    print("=" * 50)
    
    if issues_found:
        print("发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
        
        print("\n建议的解决方案:")
        for i, solution in enumerate(solutions, 1):
            print(f"   {i}. {solution}")
    else:
        print("✅ 未发现明显的环境问题")
    
    print("\n通用解决方案:")
    print("1. 使用稳定版播放器: python stable_mp4_player.py")
    print("2. 禁用Whisper功能，只使用基本播放")
    print("3. 添加更多异常处理和日志")
    print("4. 使用简化版播放器作为备用")
    
    return len(issues_found) == 0

def create_minimal_player():
    """创建最小化播放器"""
    print("\n🔧 创建最小化播放器...")
    
    minimal_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化MP4播放器 - 绝对不会自动关闭
"""

import tkinter as tk
from tkinter import ttk, filedialog
import cv2
import os
from PIL import Image, ImageTk

class MinimalPlayer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("最小化MP4播放器")
        self.root.geometry("800x600")
        
        self.cap = None
        self.setup_ui()
        
        # 防止自动关闭
        self.root.protocol("WM_DELETE_WINDOW", self.safe_close)
        
    def setup_ui(self):
        # 简单的界面
        ttk.Button(self.root, text="选择视频", command=self.open_file).pack(pady=10)
        ttk.Button(self.root, text="播放", command=self.play).pack(pady=5)
        ttk.Button(self.root, text="停止", command=self.stop).pack(pady=5)
        
        self.video_label = ttk.Label(self.root, text="请选择视频文件")
        self.video_label.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
        
        # 状态显示
        self.status = tk.StringVar(value="就绪")
        ttk.Label(self.root, textvariable=self.status).pack(pady=5)
        
        # 保持运行按钮
        ttk.Button(self.root, text="保持运行", 
                  command=lambda: self.status.set("强制保持运行")).pack(pady=5)
    
    def open_file(self):
        try:
            file_path = filedialog.askopenfilename(
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )
            if file_path:
                self.load_video(file_path)
        except Exception as e:
            self.status.set(f"打开文件失败: {e}")
    
    def load_video(self, path):
        try:
            if self.cap:
                self.cap.release()
            
            self.cap = cv2.VideoCapture(path)
            if self.cap.isOpened():
                self.status.set(f"视频已加载: {os.path.basename(path)}")
                self.show_first_frame()
            else:
                self.status.set("无法打开视频文件")
        except Exception as e:
            self.status.set(f"加载失败: {e}")
    
    def show_first_frame(self):
        try:
            ret, frame = self.cap.read()
            if ret:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                image = Image.fromarray(frame_rgb)
                image = image.resize((400, 300), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)
                self.video_label.configure(image=photo, text="")
                self.video_label.image = photo
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        except Exception as e:
            self.status.set(f"显示帧失败: {e}")
    
    def play(self):
        self.status.set("播放功能（简化版）")
    
    def stop(self):
        if self.cap:
            self.cap.release()
            self.cap = None
        self.video_label.configure(image="", text="已停止")
        self.video_label.image = None
        self.status.set("已停止")
    
    def safe_close(self):
        try:
            if self.cap:
                self.cap.release()
        except:
            pass
        self.root.destroy()
    
    def run(self):
        print("启动最小化播放器...")
        self.root.mainloop()
        print("程序正常结束")

if __name__ == "__main__":
    try:
        player = MinimalPlayer()
        player.run()
    except Exception as e:
        print(f"错误: {e}")
        input("按回车退出...")
'''
    
    try:
        with open("minimal_player.py", "w", encoding="utf-8") as f:
            f.write(minimal_code)
        print("✅ 最小化播放器已创建: minimal_player.py")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def provide_recommendations():
    """提供建议"""
    print("\n" + "=" * 50)
    print("📋 使用建议")
    print("=" * 50)
    
    recommendations = [
        {
            "问题": "程序立即关闭",
            "解决方案": [
                "使用: python stable_mp4_player.py",
                "检查控制台错误信息",
                "运行: python test_player_startup.py"
            ]
        },
        {
            "问题": "加载视频后关闭",
            "解决方案": [
                "使用简化版播放器",
                "禁用Whisper字幕功能",
                "检查视频文件格式"
            ]
        },
        {
            "问题": "线程相关错误",
            "解决方案": [
                "使用稳定版播放器（无后台线程）",
                "避免在线程中更新UI",
                "使用root.after()进行UI更新"
            ]
        },
        {
            "问题": "依赖包问题",
            "解决方案": [
                "重新安装: pip install opencv-python pillow",
                "检查Python环境",
                "使用虚拟环境"
            ]
        }
    ]
    
    for rec in recommendations:
        print(f"\n{rec['问题']}:")
        for i, solution in enumerate(rec['解决方案'], 1):
            print(f"   {i}. {solution}")

def main():
    """主函数"""
    print("程序自动关闭问题修复工具")
    print("=" * 50)
    
    # 分析问题
    env_ok = analyze_auto_close_issue()
    
    # 创建备用播放器
    minimal_created = create_minimal_player()
    
    # 提供建议
    provide_recommendations()
    
    print("\n" + "=" * 50)
    print("🎯 推荐的解决步骤")
    print("=" * 50)
    
    steps = [
        "1. 首先尝试稳定版播放器: python stable_mp4_player.py",
        "2. 如果仍有问题，使用最小化播放器: python minimal_player.py",
        "3. 检查控制台输出，查看具体错误信息",
        "4. 运行启动测试: python test_player_startup.py",
        "5. 如果需要字幕功能，逐步启用功能模块"
    ]
    
    for step in steps:
        print(step)
    
    print(f"\n环境检查: {'✅ 通过' if env_ok else '❌ 有问题'}")
    print(f"备用播放器: {'✅ 已创建' if minimal_created else '❌ 创建失败'}")
    
    print("\n💡 如果所有方法都失败，请提供详细的错误信息以便进一步诊断")

if __name__ == "__main__":
    main()
