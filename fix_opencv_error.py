#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV C++ 异常错误诊断和修复工具
解决 cv2.error: Unknown C++ exception from OpenCV code 错误
"""

import os
import sys
import subprocess
import tempfile

def check_opencv_installation():
    """检查OpenCV安装状态"""
    print("=" * 60)
    print("1. 检查OpenCV安装状态")
    print("=" * 60)
    
    try:
        import cv2
        print(f"✓ OpenCV 版本: {cv2.__version__}")
        
        # 检查构建信息
        build_info = cv2.getBuildInformation()
        print("\n构建信息摘要:")
        
        # 提取关键信息
        lines = build_info.split('\n')
        key_info = []
        for line in lines:
            if any(keyword in line.lower() for keyword in ['ffmpeg', 'gstreamer', 'media foundation', 'video', 'codec']):
                key_info.append(line.strip())
        
        if key_info:
            for info in key_info[:10]:  # 只显示前10行
                print(f"  {info}")
        else:
            print("  未找到视频相关的构建信息")
            
        return True
    except ImportError:
        print("❌ OpenCV 未安装")
        return False

def test_video_capture():
    """测试视频捕获功能"""
    print("\n" + "=" * 60)
    print("2. 测试视频捕获功能")
    print("=" * 60)
    
    try:
        import cv2
        
        # 测试创建VideoCapture对象
        print("测试创建VideoCapture对象...")
        cap = cv2.VideoCapture()
        print("✓ VideoCapture对象创建成功")
        cap.release()
        
        # 测试打开不存在的文件（应该失败但不崩溃）
        print("测试打开不存在的文件...")
        cap = cv2.VideoCapture("nonexistent_file.mp4")
        is_opened = cap.isOpened()
        print(f"文件打开状态: {is_opened} (应该是False)")
        cap.release()
        
        return True
        
    except Exception as e:
        print(f"❌ 视频捕获测试失败: {e}")
        return False

def create_test_video():
    """创建测试视频文件"""
    print("\n" + "=" * 60)
    print("3. 创建测试视频文件")
    print("=" * 60)
    
    try:
        import cv2
        import numpy as np
        
        # 创建临时视频文件
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video_path = temp_video.name
        temp_video.close()
        
        print(f"创建测试视频: {temp_video_path}")
        
        # 视频参数
        width, height = 640, 480
        fps = 30
        duration = 3  # 3秒
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频写入器")
            return None
        
        # 生成测试帧
        total_frames = fps * duration
        for i in range(total_frames):
            # 创建彩色渐变帧
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            color_value = int((i / total_frames) * 255)
            frame[:, :] = [color_value, 255 - color_value, 128]
            
            # 添加文字
            cv2.putText(frame, f'Frame {i+1}', (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✓ 测试视频创建成功: {temp_video_path}")
        return temp_video_path
        
    except Exception as e:
        print(f"❌ 创建测试视频失败: {e}")
        return None

def test_video_reading(video_path):
    """测试视频读取功能"""
    print("\n" + "=" * 60)
    print("4. 测试视频读取功能")
    print("=" * 60)
    
    if not video_path or not os.path.exists(video_path):
        print("❌ 测试视频文件不存在")
        return False
    
    try:
        import cv2
        
        print(f"测试读取视频: {video_path}")
        
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return False
        
        print("✓ 视频文件打开成功")
        
        # 获取视频属性
        try:
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            print(f"视频属性:")
            print(f"  分辨率: {width}x{height}")
            print(f"  帧率: {fps}")
            print(f"  总帧数: {total_frames}")
            
        except Exception as e:
            print(f"⚠️  获取视频属性时出错: {e}")
        
        # 测试读取帧
        frame_count = 0
        max_test_frames = 10
        
        print(f"测试读取前{max_test_frames}帧...")
        
        while frame_count < max_test_frames:
            ret, frame = cap.read()
            if not ret:
                print(f"在第{frame_count+1}帧时读取失败")
                break
            
            frame_count += 1
            if frame_count % 5 == 0:
                print(f"  已读取 {frame_count} 帧")
        
        cap.release()
        
        if frame_count > 0:
            print(f"✓ 成功读取 {frame_count} 帧")
            return True
        else:
            print("❌ 无法读取任何帧")
            return False
            
    except Exception as e:
        print(f"❌ 视频读取测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

def suggest_solutions():
    """提供解决方案建议"""
    print("\n" + "=" * 60)
    print("5. 解决方案建议")
    print("=" * 60)
    
    solutions = [
        {
            "问题": "OpenCV版本不兼容",
            "解决方案": [
                "重新安装OpenCV: pip uninstall opencv-python && pip install opencv-python",
                "尝试不同版本: pip install opencv-python==********",
                "使用opencv-contrib-python: pip install opencv-contrib-python"
            ]
        },
        {
            "问题": "视频编解码器问题",
            "解决方案": [
                "安装额外的编解码器支持",
                "转换视频格式为更兼容的格式",
                "使用ffmpeg预处理视频文件"
            ]
        },
        {
            "问题": "系统依赖缺失",
            "解决方案": [
                "Windows: 安装Visual C++ Redistributable",
                "Linux: 安装libgtk2.0-dev, pkg-config等",
                "macOS: 使用brew安装相关依赖"
            ]
        },
        {
            "问题": "内存或资源问题",
            "解决方案": [
                "检查可用内存",
                "关闭其他占用资源的程序",
                "尝试处理较小的视频文件"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n{i}. {solution['问题']}:")
        for j, step in enumerate(solution['解决方案'], 1):
            print(f"   {j}) {step}")

def main():
    """主函数"""
    print("OpenCV C++ 异常错误诊断工具")
    print("用于解决: cv2.error: Unknown C++ exception from OpenCV code")
    
    # 检查OpenCV安装
    if not check_opencv_installation():
        print("\n请先安装OpenCV: pip install opencv-python")
        return
    
    # 测试视频捕获
    if not test_video_capture():
        print("\n基础视频捕获功能有问题")
    
    # 创建和测试视频文件
    test_video_path = create_test_video()
    if test_video_path:
        success = test_video_reading(test_video_path)
        
        # 清理测试文件
        try:
            os.unlink(test_video_path)
            print(f"\n清理测试文件: {test_video_path}")
        except:
            pass
        
        if not success:
            print("\n视频读取功能有问题")
    
    # 提供解决方案
    suggest_solutions()
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
