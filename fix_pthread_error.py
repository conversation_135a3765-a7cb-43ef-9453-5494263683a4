#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复pthread错误的专用脚本
"""

import os
import sys

def setup_ultra_safe_environment():
    """设置超级安全的环境变量来修复pthread错误"""
    print("🔧 设置超级安全环境变量修复pthread错误...")
    
    # 完全禁用多线程
    os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1'
    os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
    os.environ['OPENCV_VIDEOIO_FFMPEG_THREAD_COUNT'] = '1'
    os.environ['FFMPEG_THREAD_SAFE'] = '0'  # 禁用线程安全
    
    # 禁用异步处理
    os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
    os.environ['OPENCV_VIDEOIO_DISABLE_ASYNC'] = '1'
    os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'
    
    # 禁用硬件加速
    os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'
    os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
    
    # 强制使用单线程解码
    os.environ['OPENCV_FFMPEG_THREAD_TYPE'] = '1'  # 只使用帧级线程
    os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
    
    # 禁用MSMF
    os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
    os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
    
    # 设置更安全的缓冲区
    os.environ['OPENCV_FFMPEG_READ_ATTEMPTS'] = '1'
    os.environ['OPENCV_FFMPEG_CAPTURE_BUFFER_SIZE'] = '1'
    
    print("✅ 超级安全环境变量设置完成")

def test_video_playback():
    """测试视频播放是否还有pthread错误"""
    setup_ultra_safe_environment()
    
    import cv2
    import time
    
    video_path = "test_video_with_audio.mp4"
    if not os.path.exists(video_path):
        print(f"❌ 测试视频不存在: {video_path}")
        return
    
    print(f"🎬 测试视频播放: {video_path}")
    
    try:
        # 创建VideoCapture
        cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
        
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return
        
        print("✅ 视频文件打开成功")
        
        # 获取视频信息
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"📊 视频信息: {total_frames}帧, {fps}fps")
        
        # 测试读取帧
        frame_count = 0
        max_test_frames = 100
        
        print("🔄 开始测试帧读取...")
        
        while frame_count < max_test_frames:
            ret, frame = cap.read()
            
            if not ret:
                print(f"⚠️ 读取失败在第{frame_count}帧")
                break
            
            frame_count += 1
            
            # 每10帧显示一次进度
            if frame_count % 10 == 0:
                print(f"✅ 已读取{frame_count}帧")
            
            # 测试跳转
            if frame_count == 50:
                print("🔄 测试跳转到第100帧...")
                cap.set(cv2.CAP_PROP_POS_FRAMES, 100)
        
        print(f"✅ 测试完成，成功读取{frame_count}帧")
        
        # 释放资源
        cap.release()
        print("✅ 资源释放完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def create_safe_player():
    """创建安全的播放器启动脚本"""
    safe_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys

# 🛡️ 超级安全环境设置 - 修复pthread错误
print("🔧 设置超级安全环境变量...")

# 完全禁用多线程
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1'
os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
os.environ['OPENCV_VIDEOIO_FFMPEG_THREAD_COUNT'] = '1'
os.environ['FFMPEG_THREAD_SAFE'] = '0'

# 禁用异步处理
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_VIDEOIO_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'

# 禁用硬件加速
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'

# 强制使用单线程解码
os.environ['OPENCV_FFMPEG_THREAD_TYPE'] = '1'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'

# 禁用MSMF
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'

print("✅ 超级安全环境变量设置完成")

# 启动播放器
if __name__ == "__main__":
    import subprocess
    
    # 传递所有命令行参数
    args = [sys.executable, "simple_mp4_player.py"] + sys.argv[1:]
    
    print(f"🚀 启动安全播放器: {' '.join(args)}")
    subprocess.run(args)
"""
    
    with open("safe_player.py", "w", encoding="utf-8") as f:
        f.write(safe_script)
    
    print("✅ 创建安全播放器启动脚本: safe_player.py")

def main():
    """主函数"""
    print("🔧 pthread错误修复工具")
    print("=" * 50)
    
    print("1. 设置超级安全环境变量")
    setup_ultra_safe_environment()
    
    print("\n2. 测试视频播放")
    test_video_playback()
    
    print("\n3. 创建安全播放器启动脚本")
    create_safe_player()
    
    print("\n" + "=" * 50)
    print("🎯 修复建议:")
    print("1. 使用 'python safe_player.py test_video_with_audio.mp4' 启动播放器")
    print("2. 如果还有错误，尝试重启系统")
    print("3. 确保使用最新版本的OpenCV")
    
    print("\n🔧 环境变量已设置:")
    env_vars = [
        'OPENCV_FFMPEG_CAPTURE_OPTIONS',
        'OPENCV_VIDEOIO_FFMPEG_THREAD_COUNT',
        'OPENCV_FFMPEG_DISABLE_ASYNC',
        'OPENCV_VIDEOIO_DISABLE_ASYNC'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"   {var} = {value}")

if __name__ == "__main__":
    main()
