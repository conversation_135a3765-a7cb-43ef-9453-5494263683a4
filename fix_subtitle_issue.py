#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复字幕无法启用的问题
一键安装所需依赖并验证功能
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n正在{description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✓ {description}成功")
            return True
        else:
            print(f"❌ {description}失败:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {description}超时")
        return False
    except Exception as e:
        print(f"❌ {description}出错: {e}")
        return False

def install_dependencies():
    """安装必要的依赖"""
    print("开始安装字幕功能所需的依赖包...")
    
    packages = [
        ("faster-whisper", "语音识别引擎"),
        ("moviepy", "音频提取工具"),
        ("opencv-python", "视频处理库"),
        ("pillow", "图像处理库"),
        ("numpy", "数值计算库")
    ]
    
    success_count = 0
    for package, description in packages:
        cmd = f"{sys.executable} -m pip install {package}"
        if run_command(cmd, f"安装{package} ({description})"):
            success_count += 1
    
    print(f"\n安装结果: {success_count}/{len(packages)} 个包安装成功")
    return success_count == len(packages)

def test_imports():
    """测试导入"""
    print("\n测试依赖包导入...")
    
    tests = [
        ("faster_whisper", "faster-whisper"),
        ("cv2", "opencv-python"),
        ("PIL", "pillow"),
        ("numpy", "numpy"),
        ("tkinter", "tkinter (内置)")
    ]
    
    success_count = 0
    for module, package in tests:
        try:
            __import__(module)
            print(f"✓ {package}")
            success_count += 1
        except ImportError:
            print(f"❌ {package} 导入失败")
    
    # 测试moviepy (可选)
    try:
        from moviepy.editor import VideoFileClip
        print("✓ moviepy")
        success_count += 1
    except ImportError:
        print("⚠️ moviepy 导入失败 (将尝试其他音频提取方法)")
    
    return success_count >= len(tests)

def test_whisper_model():
    """测试Whisper模型"""
    print("\n测试Whisper模型加载...")
    
    try:
        from faster_whisper import WhisperModel
        print("正在加载Whisper模型 (首次使用需要下载，请耐心等待)...")
        
        # 尝试加载较小的模型以节省时间和内存
        model = WhisperModel("base", device="cpu", compute_type="int8")
        print("✓ Whisper模型加载成功")
        
        # 简单测试
        print("测试模型功能...")
        # 这里可以添加一个简单的测试
        print("✓ 模型功能正常")
        return True
        
    except Exception as e:
        print(f"❌ Whisper模型测试失败: {e}")
        
        if "network" in str(e).lower() or "download" in str(e).lower():
            print("   可能的原因: 网络连接问题")
            print("   解决方案: 检查网络连接，或稍后重试")
        elif "memory" in str(e).lower():
            print("   可能的原因: 内存不足")
            print("   解决方案: 关闭其他程序释放内存")
        else:
            print(f"   错误详情: {e}")
        
        return False

def create_fixed_player():
    """创建修复版的播放器"""
    print("\n创建修复版播放器...")
    
    fixed_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版MP4播放器 - 确保字幕功能可用
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time

# 检查依赖
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    print("✓ faster-whisper 可用")
except ImportError:
    WHISPER_AVAILABLE = False
    print("❌ faster-whisper 不可用")

try:
    import cv2
    print("✓ opencv-python 可用")
except ImportError:
    print("❌ opencv-python 不可用")
    exit(1)

class FixedMP4Player:
    def __init__(self, root):
        self.root = root
        self.root.title("修复版MP4播放器")
        self.root.geometry("800x600")
        
        # 初始化变量
        self.whisper_model = None
        self.subtitle_enabled = tk.BooleanVar(value=True)
        self.subtitle_text = "字幕功能检查中..."
        
        self.setup_ui()
        self.check_subtitle_status()
        
    def setup_ui(self):
        """设置界面"""
        # 主标签
        title_label = tk.Label(self.root, text="MP4播放器字幕功能测试", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # 状态显示
        self.status_var = tk.StringVar(value="正在检查字幕功能...")
        status_label = tk.Label(self.root, textvariable=self.status_var, 
                               font=("Arial", 12), fg="blue")
        status_label.pack(pady=10)
        
        # 字幕开关
        subtitle_check = tk.Checkbutton(self.root, text="启用字幕功能", 
                                       variable=self.subtitle_enabled,
                                       font=("Arial", 12))
        subtitle_check.pack(pady=10)
        
        # 测试按钮
        test_button = tk.Button(self.root, text="测试字幕功能", 
                               command=self.test_subtitle_function,
                               font=("Arial", 12), bg="lightgreen")
        test_button.pack(pady=10)
        
        # 结果显示
        self.result_text = tk.Text(self.root, height=15, width=80)
        self.result_text.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
        
    def check_subtitle_status(self):
        """检查字幕状态"""
        def check():
            if WHISPER_AVAILABLE:
                try:
                    self.whisper_model = WhisperModel("base", device="cpu", compute_type="int8")
                    self.root.after(0, lambda: self.status_var.set("✓ 字幕功能已就绪"))
                    self.root.after(0, lambda: self.log_message("✓ Whisper模型加载成功"))
                except Exception as e:
                    self.root.after(0, lambda: self.status_var.set("❌ Whisper模型加载失败"))
                    self.root.after(0, lambda: self.log_message(f"❌ 错误: {e}"))
            else:
                self.root.after(0, lambda: self.status_var.set("❌ faster-whisper未安装"))
                self.root.after(0, lambda: self.log_message("❌ 请运行: pip install faster-whisper"))
        
        threading.Thread(target=check, daemon=True).start()
        
    def test_subtitle_function(self):
        """测试字幕功能"""
        self.log_message("\\n开始测试字幕功能...")
        
        if not self.subtitle_enabled.get():
            self.log_message("❌ 字幕功能已禁用")
            return
            
        if not WHISPER_AVAILABLE:
            self.log_message("❌ faster-whisper未安装")
            self.log_message("解决方案: pip install faster-whisper")
            return
            
        if not self.whisper_model:
            self.log_message("❌ Whisper模型未加载")
            return
            
        self.log_message("✓ 所有检查通过")
        self.log_message("✓ 字幕功能可以正常使用")
        self.log_message("\\n现在可以运行完整版播放器: python mp4_player_with_subtitles.py")
        
    def log_message(self, message):
        """记录消息"""
        self.result_text.insert(tk.END, message + "\\n")
        self.result_text.see(tk.END)
        self.root.update()

def main():
    root = tk.Tk()
    app = FixedMP4Player(root)
    root.mainloop()

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("fixed_mp4_player.py", "w", encoding="utf-8") as f:
            f.write(fixed_code)
        print("✓ 修复版播放器创建成功: fixed_mp4_player.py")
        return True
    except Exception as e:
        print(f"❌ 创建修复版播放器失败: {e}")
        return False

def main():
    """主函数"""
    print("MP4播放器字幕功能修复工具")
    print("="*50)
    
    # 步骤1: 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败，请手动安装")
        return
    
    # 步骤2: 测试导入
    if not test_imports():
        print("\n❌ 依赖导入失败，请检查安装")
        return
    
    # 步骤3: 测试Whisper
    whisper_ok = test_whisper_model()
    
    # 步骤4: 创建修复版播放器
    create_fixed_player()
    
    # 总结
    print("\n" + "="*50)
    print("修复结果")
    print("="*50)
    
    if whisper_ok:
        print("🎉 字幕功能修复成功！")
        print("\n现在可以:")
        print("1. 运行修复版播放器测试: python fixed_mp4_player.py")
        print("2. 运行完整版播放器: python mp4_player_with_subtitles.py")
        print("3. 选择MP4文件并启用字幕功能")
    else:
        print("⚠️ 部分功能可能不可用")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 重新运行此脚本")
        print("3. 或使用演示字幕模式")
    
    print("\n修复完成！")

if __name__ == "__main__":
    main()
