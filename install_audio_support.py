#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装音频支持依赖
为MP4播放器安装音频提取功能所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"\n正在安装 {package_name}...")
        if description:
            print(f"说明: {description}")
        
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            print(f"✓ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装出错: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def test_audio_extraction():
    """测试音频提取功能"""
    print("\n正在测试音频提取功能...")
    
    try:
        # 测试moviepy
        from moviepy.editor import VideoFileClip
        print("✓ moviepy 可用")
        return True
    except ImportError:
        print("❌ moviepy 不可用")
    
    try:
        # 测试ffmpeg
        import subprocess
        result = subprocess.run(["ffmpeg", "-version"], 
                              capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✓ ffmpeg 可用")
            return True
        else:
            print("❌ ffmpeg 不可用")
    except:
        print("❌ ffmpeg 不可用")
    
    return False

def main():
    """主函数"""
    print("=" * 60)
    print("MP4播放器 - 音频支持安装程序")
    print("=" * 60)
    
    print("\n检查当前环境...")
    
    # 检查已安装的包
    packages_status = {
        "moviepy": check_package("moviepy"),
        "opencv-python": check_package("cv2"),
        "pillow": check_package("PIL"),
        "numpy": check_package("numpy")
    }
    
    print("\n当前包状态:")
    for package, installed in packages_status.items():
        status = "✓ 已安装" if installed else "❌ 未安装"
        print(f"  {package}: {status}")
    
    # 安装缺失的包
    packages_to_install = [
        ("moviepy", "视频音频处理库，用于从MP4提取音频"),
        ("opencv-python", "计算机视觉库，用于视频处理"),
        ("pillow", "图像处理库，用于字幕渲染"),
        ("numpy", "数值计算库，基础依赖")
    ]
    
    print("\n开始安装缺失的包...")
    success_count = 0
    total_count = 0
    
    for package, description in packages_to_install:
        package_key = package.replace("-", "_") if package == "opencv-python" else package
        if package_key == "opencv_python":
            package_key = "cv2"
        
        if not packages_status.get(package_key, False):
            total_count += 1
            if install_package(package, description):
                success_count += 1
        else:
            print(f"\n{package} 已安装，跳过")
    
    # 测试功能
    print("\n" + "=" * 60)
    print("测试音频提取功能")
    print("=" * 60)
    
    if test_audio_extraction():
        print("\n✅ 音频提取功能可用！")
        print("现在可以使用完整的字幕功能了。")
    else:
        print("\n⚠️  音频提取功能不可用")
        print("请手动安装以下工具之一：")
        print("1. moviepy: pip install moviepy")
        print("2. ffmpeg: 从 https://ffmpeg.org 下载并添加到PATH")
    
    # 显示使用说明
    print("\n" + "=" * 60)
    print("使用说明")
    print("=" * 60)
    print("1. 运行播放器: python mp4_player_with_subtitles.py")
    print("2. 选择MP4文件")
    print("3. 点击播放按钮")
    print("4. 观察字幕生成进度")
    print("5. 调整字幕样式和位置")
    
    if total_count > 0:
        print(f"\n安装结果: {success_count}/{total_count} 个包安装成功")
    else:
        print("\n所有必要的包都已安装")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
