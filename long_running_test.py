#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长时间运行测试 - 专门找出播放一段时间后自动关闭的原因
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import os
import time
import threading
import traceback
import gc
import psutil

class LongRunningTest:
    def __init__(self):
        print("🚀 启动长时间运行测试...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("长时间运行测试 - 监控自动关闭")
        self.root.geometry("900x700")
        
        # 设置变量
        self.running = True
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.play_thread = None
        self.monitor_thread = None
        
        # 监控数据
        self.start_time = time.time()
        self.frame_count = 0
        self.error_count = 0
        self.last_activity = time.time()
        
        # 创建界面
        self.create_ui()
        
        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 启动监控
        self.start_monitoring()
        
        print("✅ 长时间运行测试初始化完成")
    
    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightgray")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = tk.Label(main_frame, text="长时间运行测试", 
                              font=("Arial", 16, "bold"), bg="lightgray")
        title_label.pack(pady=10)
        
        # 状态显示区域
        status_frame = tk.LabelFrame(main_frame, text="运行状态", font=("Arial", 12))
        status_frame.pack(fill=tk.X, pady=5)
        
        # 运行时间
        self.runtime_var = tk.StringVar(value="运行时间: 0秒")
        tk.Label(status_frame, textvariable=self.runtime_var, font=("Arial", 12, "bold")).pack(pady=5)
        
        # 系统信息
        info_frame = tk.Frame(status_frame)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.memory_var = tk.StringVar(value="内存使用: 0 MB")
        tk.Label(info_frame, textvariable=self.memory_var).pack(side=tk.LEFT, padx=10)
        
        self.cpu_var = tk.StringVar(value="CPU使用: 0%")
        tk.Label(info_frame, textvariable=self.cpu_var).pack(side=tk.LEFT, padx=10)
        
        self.frame_var = tk.StringVar(value="处理帧数: 0")
        tk.Label(info_frame, textvariable=self.frame_var).pack(side=tk.LEFT, padx=10)
        
        # 视频控制区域
        video_frame = tk.LabelFrame(main_frame, text="视频测试", font=("Arial", 12))
        video_frame.pack(fill=tk.X, pady=5)
        
        control_frame = tk.Frame(video_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Button(control_frame, text="选择视频", command=self.select_video).pack(side=tk.LEFT, padx=5)
        
        self.play_btn = tk.Button(control_frame, text="开始播放测试", 
                                 command=self.toggle_play, state="disabled")
        self.play_btn.pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="停止", command=self.stop_play).pack(side=tk.LEFT, padx=5)
        
        self.video_status_var = tk.StringVar(value="未选择视频")
        tk.Label(control_frame, textvariable=self.video_status_var).pack(side=tk.LEFT, padx=10)
        
        # 日志显示区域
        log_frame = tk.LabelFrame(main_frame, text="详细日志", font=("Arial", 12))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(text_frame, font=("Consolas", 9), wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 控制按钮
        button_frame = tk.Frame(main_frame, bg="lightgray")
        button_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(button_frame, text="强制垃圾回收", command=self.force_gc).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="内存检查", command=self.check_memory).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="强制保持运行", command=self.force_keep_alive).pack(side=tk.RIGHT, padx=5)
        
        # 添加初始日志
        self.add_log("程序启动成功")
        self.add_log("开始长时间运行测试")
        self.add_log("监控程序是否会自动关闭")
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        runtime = int(time.time() - self.start_time)
        log_entry = f"[{timestamp}] [{runtime:04d}s] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 200:
            self.log_text.delete("1.0", "50.0")
        
        print(log_entry.strip())
        
        # 更新最后活动时间
        self.last_activity = time.time()
    
    def select_video(self):
        """选择视频"""
        try:
            self.add_log("打开文件选择对话框")
            
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_status_var.set(f"已选择: {filename}")
                self.add_log(f"选择视频: {filename}")
                
                # 测试视频
                self.test_video()
            else:
                self.add_log("用户取消文件选择")
                
        except Exception as e:
            self.add_log(f"选择视频失败: {e}")
            self.error_count += 1
    
    def test_video(self):
        """测试视频"""
        if not self.video_path:
            return
        
        try:
            self.add_log("开始测试视频文件")
            
            import cv2
            
            # 尝试打开视频
            cap = cv2.VideoCapture(self.video_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                self.add_log(f"视频信息: {width}x{height}, {fps}fps, {frame_count}帧")
                
                # 测试读取几帧
                for i in range(min(5, frame_count)):
                    ret, frame = cap.read()
                    if ret:
                        self.add_log(f"成功读取第{i+1}帧")
                    else:
                        self.add_log(f"读取第{i+1}帧失败")
                        break
                
                cap.release()
                self.add_log("视频测试完成，可以开始播放测试")
                self.play_btn.configure(state="normal")
            else:
                self.add_log("无法打开视频文件")
                self.error_count += 1
                
        except Exception as e:
            self.add_log(f"视频测试失败: {e}")
            self.error_count += 1
    
    def toggle_play(self):
        """切换播放"""
        if not self.video_path:
            return
        
        if not self.is_playing:
            self.start_play()
        else:
            self.stop_play()
    
    def start_play(self):
        """开始播放"""
        try:
            self.add_log("开始播放测试")
            self.is_playing = True
            self.play_btn.configure(text="停止播放测试")
            
            # 启动播放线程
            self.play_thread = threading.Thread(target=self.play_loop, daemon=True)
            self.play_thread.start()
            
        except Exception as e:
            self.add_log(f"启动播放失败: {e}")
            self.error_count += 1
    
    def play_loop(self):
        """播放循环"""
        try:
            import cv2
            
            self.add_log("播放线程启动")
            
            cap = cv2.VideoCapture(self.video_path)
            if not cap.isOpened():
                self.add_log("播放线程: 无法打开视频")
                return
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                fps = 30
            
            frame_delay = 1.0 / fps
            local_frame_count = 0
            
            while self.is_playing and self.running:
                try:
                    ret, frame = cap.read()
                    if ret:
                        local_frame_count += 1
                        self.frame_count += 1
                        
                        # 每100帧报告一次
                        if local_frame_count % 100 == 0:
                            self.root.after(0, lambda: self.add_log(f"播放进度: {local_frame_count}帧"))
                        
                        # 控制播放速度
                        time.sleep(frame_delay)
                    else:
                        # 视频结束，重新开始
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        self.root.after(0, lambda: self.add_log("视频播放完毕，重新开始"))
                        
                except Exception as e:
                    self.root.after(0, lambda: self.add_log(f"播放循环错误: {e}"))
                    self.error_count += 1
                    time.sleep(1)
            
            cap.release()
            self.root.after(0, lambda: self.add_log("播放线程结束"))
            
        except Exception as e:
            self.root.after(0, lambda: self.add_log(f"播放线程异常: {e}"))
            self.error_count += 1
    
    def stop_play(self):
        """停止播放"""
        self.add_log("停止播放测试")
        self.is_playing = False
        self.play_btn.configure(text="开始播放测试")
        
        if self.play_thread and self.play_thread.is_alive():
            self.play_thread.join(timeout=2.0)
    
    def start_monitoring(self):
        """启动监控"""
        def monitor_loop():
            while self.running:
                try:
                    # 更新运行时间
                    runtime = int(time.time() - self.start_time)
                    self.root.after(0, lambda: self.runtime_var.set(f"运行时间: {runtime}秒"))
                    
                    # 更新系统信息
                    try:
                        process = psutil.Process()
                        memory_mb = process.memory_info().rss / 1024 / 1024
                        cpu_percent = process.cpu_percent()
                        
                        self.root.after(0, lambda: self.memory_var.set(f"内存使用: {memory_mb:.1f} MB"))
                        self.root.after(0, lambda: self.cpu_var.set(f"CPU使用: {cpu_percent:.1f}%"))
                        self.root.after(0, lambda: self.frame_var.set(f"处理帧数: {self.frame_count}"))
                        
                        # 检查内存泄漏
                        if memory_mb > 500:  # 超过500MB
                            self.root.after(0, lambda: self.add_log(f"⚠️ 内存使用过高: {memory_mb:.1f}MB"))
                        
                    except:
                        pass
                    
                    # 每30秒报告状态
                    if runtime > 0 and runtime % 30 == 0:
                        self.root.after(0, lambda: self.add_log(f"💓 程序运行正常 - {runtime}秒, 错误:{self.error_count}次"))
                    
                    # 检查是否长时间无活动
                    inactive_time = time.time() - self.last_activity
                    if inactive_time > 60:  # 超过1分钟无活动
                        self.root.after(0, lambda: self.add_log(f"⚠️ 程序可能无响应，无活动时间: {inactive_time:.0f}秒"))
                        self.last_activity = time.time()
                    
                    time.sleep(1)
                    
                except Exception as e:
                    self.root.after(0, lambda: self.add_log(f"监控线程错误: {e}"))
                    time.sleep(5)
        
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.add_log("监控线程启动")
    
    def force_gc(self):
        """强制垃圾回收"""
        before = len(gc.get_objects())
        collected = gc.collect()
        after = len(gc.get_objects())
        self.add_log(f"垃圾回收: 回收{collected}个对象, 对象数量 {before} -> {after}")
    
    def check_memory(self):
        """检查内存"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            self.add_log(f"内存详情: RSS={memory_info.rss/1024/1024:.1f}MB, VMS={memory_info.vms/1024/1024:.1f}MB")
        except Exception as e:
            self.add_log(f"内存检查失败: {e}")
    
    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get("1.0", tk.END)
            filename = f"long_running_log_{int(time.time())}.txt"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(log_content)
            self.add_log(f"日志已保存: {filename}")
        except Exception as e:
            self.add_log(f"保存日志失败: {e}")
    
    def force_keep_alive(self):
        """强制保持运行"""
        self.add_log("🔒 强制保持运行模式激活")
        messagebox.showinfo("保持运行", "程序已设置为强制保持运行模式！")
    
    def on_closing(self):
        """关闭处理"""
        self.add_log("用户尝试关闭程序")
        
        result = messagebox.askyesno("确认关闭", 
                                   "确定要关闭长时间运行测试吗？\n\n这将停止监控自动关闭问题。")
        if result:
            self.add_log("用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("用户取消关闭，继续运行")
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        self.is_playing = False
        
        if self.cap:
            try:
                self.cap.release()
            except:
                pass
        
        # 等待线程结束
        if self.play_thread and self.play_thread.is_alive():
            self.play_thread.join(timeout=2.0)
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)
    
    def run(self):
        """运行测试"""
        try:
            print("🎬 启动长时间运行测试界面...")
            print("💡 这个测试将监控程序是否会自动关闭")
            print("💡 请让程序运行较长时间，观察是否会自动关闭")
            
            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    self.video_status_var.set(f"命令行: {os.path.basename(video_file)}")
                    self.root.after(1000, self.test_video)
            
            # 启动主循环
            self.root.mainloop()
            
            print("🔚 长时间运行测试结束")
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 长时间运行测试")
    print("=" * 60)
    print("目的: 监控程序是否会在播放一段时间后自动关闭")
    print("功能: 详细监控内存、CPU、错误等信息")
    print("建议: 让程序运行至少30分钟以上")
    print("=" * 60)
    
    try:
        test = LongRunningTest()
        test.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        traceback.print_exc()
        
        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
