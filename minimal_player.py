#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化MP4播放器 - 绝对不会自动关闭
"""

import tkinter as tk
from tkinter import ttk, filedialog
import cv2
import os
from PIL import Image, ImageTk

class MinimalPlayer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("最小化MP4播放器")
        self.root.geometry("800x600")
        
        self.cap = None
        self.setup_ui()
        
        # 防止自动关闭
        self.root.protocol("WM_DELETE_WINDOW", self.safe_close)
        
    def setup_ui(self):
        # 简单的界面
        ttk.Button(self.root, text="选择视频", command=self.open_file).pack(pady=10)
        ttk.Button(self.root, text="播放", command=self.play).pack(pady=5)
        ttk.Button(self.root, text="停止", command=self.stop).pack(pady=5)
        
        self.video_label = ttk.Label(self.root, text="请选择视频文件")
        self.video_label.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
        
        # 状态显示
        self.status = tk.StringVar(value="就绪")
        ttk.Label(self.root, textvariable=self.status).pack(pady=5)
        
        # 保持运行按钮
        ttk.Button(self.root, text="保持运行", 
                  command=lambda: self.status.set("强制保持运行")).pack(pady=5)
    
    def open_file(self):
        try:
            file_path = filedialog.askopenfilename(
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )
            if file_path:
                self.load_video(file_path)
        except Exception as e:
            self.status.set(f"打开文件失败: {e}")
    
    def load_video(self, path):
        try:
            if self.cap:
                self.cap.release()
            
            self.cap = cv2.VideoCapture(path)
            if self.cap.isOpened():
                self.status.set(f"视频已加载: {os.path.basename(path)}")
                self.show_first_frame()
            else:
                self.status.set("无法打开视频文件")
        except Exception as e:
            self.status.set(f"加载失败: {e}")
    
    def show_first_frame(self):
        try:
            ret, frame = self.cap.read()
            if ret:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                image = Image.fromarray(frame_rgb)
                image = image.resize((400, 300), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(image)
                self.video_label.configure(image=photo, text="")
                self.video_label.image = photo
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        except Exception as e:
            self.status.set(f"显示帧失败: {e}")
    
    def play(self):
        self.status.set("播放功能（简化版）")
    
    def stop(self):
        if self.cap:
            self.cap.release()
            self.cap = None
        self.video_label.configure(image="", text="已停止")
        self.video_label.image = None
        self.status.set("已停止")
    
    def safe_close(self):
        try:
            if self.cap:
                self.cap.release()
        except:
            pass
        self.root.destroy()
    
    def run(self):
        print("启动最小化播放器...")
        self.root.mainloop()
        print("程序正常结束")

if __name__ == "__main__":
    try:
        player = MinimalPlayer()
        player.run()
    except Exception as e:
        print(f"错误: {e}")
        input("按回车退出...")
