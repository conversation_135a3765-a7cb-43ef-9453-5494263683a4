#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MP4播放器 - 带实时字幕功能
使用faster-whisper引擎和large-v3模型生成实时字幕
支持字幕位置和大小调整

🛡️ 已修复OpenCV MSMF后端崩溃问题:
- 问题: cap_msmf.cpp:124 Assertion failed (p == NULL)
- 解决: 完全禁用MSMF后端，优先使用FFMPEG
- 保护: 环境变量 + 后端验证 + 智能恢复
- 结果: 支持长时间播放和循环播放，不会自动关闭

🔧 已修复FFmpeg pthread_frame.c错误:
- 问题: Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173
- 原因: FFmpeg多线程异步锁定冲突
- 解决: 强制单线程模式 + 线程安全设置
- 保护: 专门的pthread错误检测和恢复

修复内容:
1. 环境变量禁用MSMF (在导入OpenCV前)
2. pthread安全环境变量设置 (强制单线程)
3. 后端选择优先级调整 (FFMPEG > DSHOW > 其他)
4. 运行时MSMF检测和阻止
5. 播放循环中的MSMF和pthread错误检测
6. 双重紧急恢复机制 (MSMF + pthread专门处理)
"""

import os
import sys

# 🛡️ 在导入OpenCV之前完全禁用MSMF后端，防止崩溃
# 这是解决"cap_msmf.cpp:124 Assertion failed"问题的关键
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '1'  # 启用调试信息

# 🔧 专门解决pthread_frame.c:173错误的环境变量
# 这是解决"Assertion fctx->async_lock failed"问题的关键
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1'
os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
os.environ['FFMPEG_THREAD_SAFE'] = '1'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'

# 🛡️ 额外的安全设置 - 防止视频提前结束
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'

print("🔧 设置最安全的环境变量...")
print("✅ 最安全环境变量设置完成")

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import time
import queue
import os
import sys
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    print(f"✓ faster-whisper 导入成功")
except ImportError as e:
    WHISPER_AVAILABLE = False
    print(f"警告: faster-whisper未安装，字幕功能将被禁用")
    print(f"导入错误详情: {e}")
    print(f"Python 路径: {sys.executable}")
except Exception as e:
    WHISPER_AVAILABLE = False
    print(f"警告: faster-whisper 导入时发生错误: {e}")
    print(f"Python 路径: {sys.executable}")


class MP4PlayerWithSubtitles:
    def __init__(self, root):
        try:
            print("🔧 初始化播放器组件...")

            self.root = root
            self.root.title("MP4播放器 - 实时字幕")
            self.root.geometry("1200x800")

            # 视频相关变量
            self.cap = None
            self.video_path = None
            self.is_playing = False
            self.is_paused = False
            self.current_frame = 0
            self.total_frames = 0
            self.fps = 30
            self.frame_delay = 1/30

            # 字幕相关变量
            self.whisper_model = None
            self.subtitle_text = ""
            self.subtitle_queue = queue.Queue()
            self.audio_thread = None
            self.transcription_thread = None
            self.transcription_running = False

            # 进度相关变量
            self.transcription_progress = 0.0
            self.total_segments = 0
            self.processed_segments = 0

            # 字幕样式设置
            self.subtitle_font_size = 24
            self.subtitle_position_y = 0.85  # 相对位置 (0-1)
            self.subtitle_color = (255, 255, 255)  # 白色
            self.subtitle_bg_color = (0, 0, 0, 128)  # 半透明黑色背景

            # FFmpeg错误监控
            self.ffmpeg_error_risk = False

            # 音频相关
            self.audio_data = []
            self.audio_sample_rate = 16000

            print("🎨 创建用户界面...")
            self.setup_ui()

            print("🤖 初始化Whisper模型...")
            self.load_whisper_model()

            print("📝 检查字幕功能...")
            self.check_subtitle_capability()

            print("✅ 播放器初始化完成")

        except Exception as e:
            print(f"❌ 播放器初始化失败: {e}")
            import traceback
            traceback.print_exc()

            # 尝试显示错误信息给用户
            try:
                import tkinter.messagebox as msgbox
                msgbox.showerror("初始化错误", f"播放器初始化失败:\n{e}\n\n请检查控制台输出获取详细信息。")
            except:
                pass

            raise
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        ttk.Button(control_frame, text="选择MP4文件", 
                  command=self.select_video_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放控制按钮
        self.play_button = ttk.Button(control_frame, text="播放", 
                                     command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="停止", 
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(control_frame, from_=0, to=100, 
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.seek_video)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(control_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 字幕控制面板
        subtitle_frame = ttk.LabelFrame(main_frame, text="字幕设置")
        subtitle_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 字幕大小控制
        ttk.Label(subtitle_frame, text="字幕大小:").grid(row=0, column=0, padx=5, pady=5)
        self.font_size_var = tk.IntVar(value=self.subtitle_font_size)
        font_size_scale = ttk.Scale(subtitle_frame, from_=12, to=48, 
                                   orient=tk.HORIZONTAL, variable=self.font_size_var,
                                   command=self.update_subtitle_font_size)
        font_size_scale.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 字幕位置控制
        ttk.Label(subtitle_frame, text="字幕位置:").grid(row=0, column=2, padx=5, pady=5)
        self.position_var = tk.DoubleVar(value=self.subtitle_position_y)
        position_scale = ttk.Scale(subtitle_frame, from_=0.1, to=0.95, 
                                  orient=tk.HORIZONTAL, variable=self.position_var,
                                  command=self.update_subtitle_position)
        position_scale.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        
        # 字幕开关
        self.subtitle_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(subtitle_frame, text="启用字幕",
                       variable=self.subtitle_enabled).grid(row=0, column=4, padx=5, pady=5)

        # 字幕生成进度显示
        ttk.Label(subtitle_frame, text="字幕生成进度:").grid(row=1, column=0, padx=5, pady=5)

        # 进度条
        self.subtitle_progress_var = tk.DoubleVar()
        self.subtitle_progress_bar = ttk.Progressbar(
            subtitle_frame,
            variable=self.subtitle_progress_var,
            maximum=100,
            mode='determinate'
        )
        self.subtitle_progress_bar.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        # 进度文本
        self.progress_text_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(subtitle_frame, textvariable=self.progress_text_var)
        self.progress_label.grid(row=1, column=3, columnspan=2, padx=5, pady=5)

        subtitle_frame.columnconfigure(1, weight=1)
        subtitle_frame.columnconfigure(3, weight=1)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=2)
        self.video_frame.pack(fill=tk.BOTH, expand=True)
        
        self.video_label = ttk.Label(self.video_frame, text="请选择MP4文件开始播放")
        self.video_label.pack(expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def load_whisper_model(self):
        """加载Whisper模型"""
        if not WHISPER_AVAILABLE:
            self.status_var.set("Whisper未安装，字幕功能不可用")
            return

        def load_model():
            try:
                print("开始加载Whisper模型...")

                # 安全地更新状态
                try:
                    self.root.after(0, lambda: self.status_var.set("正在加载Whisper模型..."))
                except:
                    pass

                # 首先尝试加载较小的模型
                model_options = [
                    ("base", "base模型 (较快)"),
                    ("small", "small模型 (平衡)"),
                    ("medium", "medium模型 (较好)"),
                    ("large-v3", "large-v3模型 (最好)")
                ]

                model_loaded = False
                for model_name, model_desc in model_options:
                    try:
                        print(f"尝试加载 {model_desc}...")

                        # 安全地更新状态
                        try:
                            self.root.after(0, lambda desc=model_desc: self.status_var.set(f"正在加载 {desc}..."))
                        except:
                            pass

                        # 使用CPU版本，确保兼容性
                        self.whisper_model = WhisperModel(model_name, device="cpu", compute_type="int8")

                        success_msg = f"✓ {model_desc} 加载成功"
                        print(success_msg)

                        # 安全地更新状态
                        try:
                            self.root.after(0, lambda msg=success_msg: self.status_var.set(msg))
                        except:
                            pass

                        model_loaded = True
                        break

                    except Exception as model_error:
                        print(f"加载 {model_desc} 失败: {model_error}")
                        continue

                if not model_loaded:
                    raise Exception("所有模型加载尝试都失败")

            except Exception as e:
                error_msg = f"Whisper模型加载失败: {str(e)}"
                print(error_msg)

                # 安全地更新状态
                try:
                    self.root.after(0, lambda: self.status_var.set("模型加载失败"))
                except:
                    pass

                # 提供解决建议
                if "download" in str(e).lower():
                    print("建议: 检查网络连接，模型需要从网络下载")
                elif "memory" in str(e).lower():
                    print("建议: 内存不足，尝试关闭其他程序")
                else:
                    print("建议: 检查 faster-whisper 安装是否正确")

        # 在后台线程中加载模型
        threading.Thread(target=load_model, daemon=True).start()

    def check_subtitle_capability(self):
        """检查字幕功能能力"""
        def check_capability():
            time.sleep(2)  # 等待Whisper模型加载

            # 检查各种能力
            whisper_ready = WHISPER_AVAILABLE and self.whisper_model is not None

            # 检查音频提取能力
            audio_capability = False
            try:
                # 检查moviepy
                from moviepy import VideoFileClip
                audio_capability = True
                audio_method = "moviepy"
            except ImportError:
                try:
                    # 检查ffmpeg
                    import subprocess
                    result = subprocess.run(["ffmpeg", "-version"],
                                          capture_output=True, timeout=5)
                    if result.returncode == 0:
                        audio_capability = True
                        audio_method = "ffmpeg"
                except:
                    audio_method = "无"

            # 更新状态显示
            if whisper_ready and audio_capability:
                status = f"字幕功能就绪 (Whisper + {audio_method})"
            elif whisper_ready:
                status = f"Whisper就绪，但音频提取不可用 (需要moviepy或ffmpeg)"
            elif audio_capability:
                status = f"音频提取就绪 ({audio_method})，但Whisper不可用"
            else:
                status = "字幕功能不可用 (需要安装faster-whisper和moviepy)"

            # 安全地更新状态
            try:
                self.root.after(0, lambda: self.status_var.set(status))
            except:
                print(f"状态更新: {status}")

        threading.Thread(target=check_capability, daemon=True).start()
        
    def select_video_file(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP4文件",
            filetypes=[("MP4文件", "*.mp4"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.load_video(file_path)
            
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            # 停止当前播放
            self.stop_video()

            # 验证文件存在
            if not os.path.exists(video_path):
                raise Exception(f"视频文件不存在: {video_path}")

            # 检查文件大小
            file_size = os.path.getsize(video_path)
            if file_size == 0:
                raise Exception("视频文件为空")

            print(f"正在加载视频: {video_path} (大小: {file_size} 字节)")

            # 设置视频路径（在创建capture之前）
            self.video_path = video_path

            # 打开视频文件 - 智能后端选择
            print("🔧 智能选择最佳视频后端...")
            self.cap = None

            # 🛡️ 使用安全的后端顺序，避免MSMF崩溃问题
            all_methods = [
                ("单线程FFMPEG（推荐）", self.create_single_thread_capture),
                ("标准FFMPEG", self.create_standard_ffmpeg_capture),
                ("DSHOW后端", self.create_dshow_capture),
                ("默认后端", self.create_default_capture),
                ("MSMF后端（已禁用）", self.create_msmf_capture),  # 已禁用，但保留用于说明
            ]

            for method_name, method_func in all_methods:
                try:
                    print(f"尝试 {method_name}...")
                    cap = method_func()
                    if cap and cap.isOpened():
                        # 验证能否读取帧
                        ret, test_frame = cap.read()
                        if ret and test_frame is not None:
                            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置位置
                            self.cap = cap
                            print(f"✓ {method_name} 加载成功")
                            break
                        else:
                            cap.release()
                            print(f"❌ {method_name} 无法读取帧")
                    else:
                        if cap:
                            cap.release()
                        print(f"❌ {method_name} 无法打开")
                except Exception as e:
                    print(f"❌ {method_name} 失败: {e}")
                    continue

            if not self.cap or not self.cap.isOpened():
                # 提供详细的诊断信息
                self.diagnose_loading_failure(video_path)
                raise Exception("所有视频后端都无法打开文件")

            # 安全地获取视频信息
            try:
                self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
                self.fps = self.cap.get(cv2.CAP_PROP_FPS)

                # 验证获取的值
                if self.total_frames <= 0:
                    print("警告: 无法获取准确的帧数，使用默认值")
                    self.total_frames = 1000  # 默认值

                if self.fps <= 0:
                    print("警告: 无法获取准确的帧率，使用默认值")
                    self.fps = 30  # 默认帧率

                self.frame_delay = 1.0 / self.fps

                print(f"视频信息: {self.total_frames}帧, {self.fps}fps")

            except Exception as prop_error:
                print(f"获取视频属性时出错: {prop_error}")
                # 使用默认值
                self.total_frames = 1000
                self.fps = 30
                self.frame_delay = 1/30

            # 重置进度条
            self.progress_bar.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0

            # 显示第一帧
            self.show_frame()

            # 更新状态
            duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.status_var.set(f"视频已加载: {os.path.basename(video_path)} "
                              f"({self.total_frames}帧, {duration:.1f}秒)")

            # 启用播放按钮
            self.play_button.configure(state="normal")

        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)

            # 清理资源
            if hasattr(self, 'cap') and self.cap:
                try:
                    self.cap.release()
                except:
                    pass
                self.cap = None
            
    def show_frame(self):
        """显示当前帧"""
        if self.cap is None:
            return

        try:
            # 安全地读取帧
            ret, frame = self.cap.read()
            if not ret:
                # 更详细的诊断信息
                current_pos = 0
                try:
                    current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                except:
                    pass

                if current_pos >= self.total_frames - 1:
                    print(f"视频播放完毕 (帧 {current_pos}/{self.total_frames})")
                    # 如果在播放状态，停止播放
                    if self.is_playing:
                        self.is_playing = False
                        self.root.after(0, lambda: self.play_button.configure(text="播放"))
                else:
                    print(f"无法读取帧 (当前位置: {current_pos}/{self.total_frames})")
                    # 尝试重新定位到有效帧
                    if self.try_recover_frame_position():
                        return  # 重新尝试显示帧
                return

            # 验证帧数据
            if frame is None:
                print("读取到空帧")
                return

            if frame.size == 0:
                print("读取到空帧数据")
                return

            # 添加字幕到帧
            try:
                if self.subtitle_enabled.get() and self.subtitle_text:
                    frame = self.add_subtitle_to_frame(frame, self.subtitle_text)
            except Exception as subtitle_error:
                print(f"添加字幕时出错: {subtitle_error}")
                # 继续处理原始帧

            # 转换为PIL图像
            try:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(frame_rgb)
            except Exception as convert_error:
                print(f"图像转换时出错: {convert_error}")
                return

            # 调整图像大小以适应显示区域
            try:
                display_width = self.video_frame.winfo_width()
                display_height = self.video_frame.winfo_height()

                if display_width > 1 and display_height > 1:
                    # 保持宽高比
                    img_ratio = pil_image.width / pil_image.height
                    display_ratio = display_width / display_height

                    if img_ratio > display_ratio:
                        new_width = display_width
                        new_height = int(display_width / img_ratio)
                    else:
                        new_height = display_height
                        new_width = int(display_height * img_ratio)

                    # 确保尺寸有效
                    new_width = max(1, new_width)
                    new_height = max(1, new_height)

                    pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            except Exception as resize_error:
                print(f"图像缩放时出错: {resize_error}")
                # 使用原始尺寸

            # 转换为Tkinter图像
            try:
                photo = ImageTk.PhotoImage(pil_image)
                self.video_label.configure(image=photo, text="")
                self.video_label.image = photo  # 保持引用
            except Exception as tk_error:
                print(f"Tkinter图像转换时出错: {tk_error}")
                return

            # 更新进度条和时间
            try:
                self.progress_var.set(self.current_frame)
                current_time = self.current_frame / self.fps if self.fps > 0 else 0
                total_time = self.total_frames / self.fps if self.fps > 0 else 0
                self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            except Exception as ui_error:
                print(f"更新UI时出错: {ui_error}")

        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__
            print(f"显示帧时发生错误: {error_msg}")
            print(f"错误类型: {error_type}")

            # 检测各种类型的OpenCV异常，包括FFmpeg相关错误
            opencv_indicators = [
                "OpenCV", "C++", "cv::", "Mat", "VideoCapture",
                "Unknown C++ exception", "assertion failed",
                "Bad argument", "Unsupported format",
                "libavcodec", "pthread_frame", "async_lock",  # FFmpeg相关错误
                "ffmpeg", "avcodec", "avformat"
            ]

            is_opencv_error = any(indicator in error_msg for indicator in opencv_indicators)
            is_opencv_type = "cv2" in error_type.lower() or "opencv" in error_type.lower()

            if is_opencv_error or is_opencv_type:
                # 检查是否是FFmpeg相关错误
                ffmpeg_indicators = ["libavcodec", "pthread_frame", "async_lock", "ffmpeg", "avcodec"]
                is_ffmpeg_error = any(indicator in error_msg.lower() for indicator in ffmpeg_indicators)

                if is_ffmpeg_error:
                    print("🔧 检测到FFmpeg相关异常，启动专门恢复...")
                    self.handle_ffmpeg_exception(error_msg, error_type)
                else:
                    print("🔧 检测到OpenCV相关异常，启动智能恢复...")
                    self.handle_opencv_exception(error_msg, error_type)
            else:
                print("⚠️  非OpenCV异常，记录错误信息")
                self.log_non_opencv_error(error_msg, error_type)

    def reinitialize_video_capture(self):
        """重新初始化视频捕获（用于处理C++异常）"""
        try:
            if not self.video_path:
                print("没有视频路径，无法重新初始化")
                return False

            print("正在重新初始化视频捕获...")

            # 记录当前状态
            current_pos = self.current_frame
            was_playing = self.is_playing

            # 暂停播放
            if was_playing:
                self.is_playing = False

            # 强制释放当前资源
            self.force_release_video_capture()

            # 等待资源完全释放
            time.sleep(0.2)

            # 尝试多种方式重新创建VideoCapture
            success = self.try_multiple_video_capture_methods()

            if success:
                # 恢复到之前的位置
                self.restore_video_position(current_pos)

                # 如果之前在播放，恢复播放状态
                if was_playing:
                    self.is_playing = True

                print("✓ 视频捕获重新初始化成功")
                return True
            else:
                print("❌ 所有重新初始化方法都失败")
                return False

        except Exception as reinit_error:
            print(f"重新初始化时出错: {reinit_error}")
            return False

    def force_release_video_capture(self):
        """强制释放视频捕获资源"""
        try:
            if self.cap:
                print("强制释放VideoCapture资源...")
                try:
                    self.cap.release()
                except:
                    pass

                # 尝试多次释放
                for i in range(3):
                    try:
                        if hasattr(self.cap, 'release'):
                            self.cap.release()
                    except:
                        pass
                    time.sleep(0.05)

                self.cap = None
                print("✓ VideoCapture资源已释放")
        except Exception as e:
            print(f"释放资源时出错: {e}")

    def try_multiple_video_capture_methods(self):
        """尝试多种方法创建VideoCapture"""
        # 针对FFmpeg断言错误的优化方法 - 完全避免FFMPEG
        methods = [
            ("MSMF后端（推荐）", lambda: self.create_msmf_capture()),
            ("DSHOW后端", lambda: self.create_dshow_capture()),
            ("默认方法（非FFMPEG）", lambda: self.create_default_capture()),
            ("单线程FFMPEG（最后选择）", lambda: self.create_single_thread_capture()),
        ]

        for method_name, method_func in methods:
            try:
                print(f"尝试 {method_name}...")
                cap = method_func()

                if cap and cap.isOpened():
                    # 验证能否读取帧
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        # 重置到开始位置
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        self.cap = cap
                        print(f"✓ {method_name} 成功")
                        return True
                    else:
                        cap.release()
                        print(f"❌ {method_name} 无法读取帧")
                else:
                    if cap:
                        cap.release()
                    print(f"❌ {method_name} 无法打开")

            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
                continue

        return False

    def create_msmf_capture(self):
        """创建MSMF后端VideoCapture（已禁用 - 有循环播放崩溃bug）"""
        print("⚠️ MSMF后端已被禁用 - 存在循环播放时的崩溃问题")
        print("   错误: cap_msmf.cpp:124 Assertion failed (p == NULL)")
        print("   解决: 使用更稳定的FFMPEG或其他后端")
        print("   详情: https://github.com/opencv/opencv/issues/...")
        return None

    def create_dshow_capture(self):
        """创建DirectShow后端VideoCapture"""
        try:
            print("使用DirectShow后端...")
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_DSHOW)

            if cap.isOpened():
                # 设置DirectShow参数
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # 测试读取
                ret, test_frame = cap.read()
                if ret and test_frame is not None:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    print("✓ DirectShow后端创建成功")
                    return cap
                else:
                    cap.release()

            print("❌ DirectShow后端无法读取帧")
            return None

        except Exception as e:
            print(f"❌ DirectShow后端创建失败: {e}")
            return None

    def create_default_capture(self):
        """创建默认后端VideoCapture（避免MSMF和FFMPEG）"""
        try:
            print("使用默认后端（避免MSMF）...")

            # 🛡️ 设置安全的后端优先级，完全避免MSMF
            import os
            original_env = os.environ.get('OPENCV_VIDEOIO_PRIORITY_LIST', '')
            os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG,DSHOW,V4L2'  # 移除MSMF

            try:
                cap = cv2.VideoCapture(self.video_path)

                if cap.isOpened():
                    # 检查后端类型
                    backend = cap.getBackendName()
                    print(f"默认后端使用: {backend}")

                    # 🛡️ 检查是否意外使用了MSMF后端
                    if 'MSMF' in backend.upper():
                        print("⚠️ 默认后端意外使用了MSMF，强制跳过以避免崩溃")
                        cap.release()
                        return None

                    # 设置参数
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                    # 测试读取
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        print(f"✓ 默认后端（{backend}）创建成功")
                        return cap
                    else:
                        cap.release()

                print("❌ 默认后端无法读取帧")
                return None

            finally:
                # 恢复环境变量
                if original_env:
                    os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = original_env
                else:
                    os.environ.pop('OPENCV_VIDEOIO_PRIORITY_LIST', None)

        except Exception as e:
            print(f"❌ 默认后端创建失败: {e}")
            return None

    def create_single_thread_capture(self):
        """创建单线程VideoCapture以避免FFmpeg多线程问题"""
        try:
            print("使用单线程FFMPEG...")
            import os

            # 保存原始环境变量
            original_env = {
                'OPENCV_FFMPEG_CAPTURE_OPTIONS': os.environ.get('OPENCV_FFMPEG_CAPTURE_OPTIONS', ''),
                'OPENCV_FFMPEG_WRITER_OPTIONS': os.environ.get('OPENCV_FFMPEG_WRITER_OPTIONS', ''),
                'OPENCV_FFMPEG_THREAD_COUNT': os.environ.get('OPENCV_FFMPEG_THREAD_COUNT', ''),
                'FFMPEG_THREAD_SAFE': os.environ.get('FFMPEG_THREAD_SAFE', ''),
                'OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION': os.environ.get('OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION', ''),
            }

            # 设置更严格的单线程选项
            os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1'
            os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
            os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
            os.environ['FFMPEG_THREAD_SAFE'] = '1'
            os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'

            try:
                # 创建VideoCapture，强制FFMPEG后端
                cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)

                if cap.isOpened():
                    try:
                        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    except:
                        pass

                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        print("✓ 单线程FFMPEG创建成功")
                        return cap
                    else:
                        cap.release()
                        print("❌ 单线程FFMPEG无法读取帧")
                        return None
                else:
                    print("❌ 单线程FFMPEG无法打开")
                    return None

            finally:
                # 恢复原始环境变量
                for k, v in original_env.items():
                    if v:
                        os.environ[k] = v
                    else:
                        os.environ.pop(k, None)

        except Exception as e:
            print(f"❌ 单线程FFMPEG创建失败: {e}")
            return None

    def toggle_play(self):
        """切换播放/暂停状态"""
        if not self.cap:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()

    def stop_video(self):
        """停止视频"""
        self.is_playing = False
        self.is_paused = False
        self.play_button.configure(text="播放")

        # 停止音频转录（如有相关实现）
        # if hasattr(self, 'stop_audio_transcription'):
        #     self.stop_audio_transcription()

        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.show_frame()

    def seek_video(self, value):
        """跳转到指定帧"""
        if not self.cap:
            return

        try:
            frame_number = int(float(value))

            # 边界检查
            if frame_number < 0:
                frame_number = 0
            elif frame_number >= self.total_frames:
                frame_number = max(0, self.total_frames - 1)

            print(f"跳转到帧: {frame_number}/{self.total_frames}")

            # 尝试跳转
            success = self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            if not success:
                print(f"跳转到帧 {frame_number} 失败，尝试恢复")
                # 尝试恢复到安全位置
                safe_frame = max(0, min(frame_number, self.total_frames // 2))
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, safe_frame)
                frame_number = safe_frame

            self.current_frame = frame_number

            # 验证跳转是否成功
            actual_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
            if abs(actual_pos - frame_number) > 5:  # 允许5帧的误差
                print(f"跳转位置不准确: 期望 {frame_number}, 实际 {actual_pos}")
                self.current_frame = actual_pos

            if not self.is_playing:
                self.show_frame()

        except Exception as e:
            print(f"视频跳转时出错: {e}")
            # 尝试恢复到开始位置
            try:
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                self.current_frame = 0
                if not self.is_playing:
                    self.show_frame()
            except:
                print("无法恢复到开始位置")

    def update_subtitle_font_size(self, value):
        """更新字幕字体大小"""
        try:
            self.subtitle_font_size = int(float(value))
            # 立即刷新当前帧以应用新字体大小
            if not self.is_playing:
                self.show_frame()
        except Exception as e:
            print(f"更新字幕字体大小时出错: {e}")