#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
永不停止播放器 - 专门解决视频未播放完就自动结束的问题
基于诊断结果，实现绝对不会提前停止的播放机制
"""

import os
import sys

# 🛡️ 设置最安全的环境变量
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1'
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import queue
import gc
import signal

class NeverStopPlayer:
    def __init__(self):
        print("🚀 启动永不停止播放器...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("永不停止播放器 - 绝对不会提前结束")
        self.root.geometry("1000x800")
        
        # 设置变量
        self.running = True
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.play_thread = None
        self.current_frame = 0
        self.force_continue = True  # 强制继续播放标志
        
        # 监控数据
        self.start_time = time.time()
        self.play_start_time = None
        self.frame_count = 0
        self.total_expected_frames = 0
        self.last_progress_report = 0
        
        # 防止意外关闭的机制
        self.user_confirmed_close = False
        
        # 创建界面
        self.create_ui()
        
        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 设置信号处理（防止意外终止）
        try:
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
        except:
            pass  # Windows可能不支持某些信号
        
        # 启动监控
        self.start_monitoring()
        
        print("✅ 永不停止播放器初始化完成")
    
    def signal_handler(self, signum, frame):
        """信号处理器 - 防止意外终止"""
        self.add_log(f"⚠️ 收到信号 {signum}，但播放器拒绝停止")
        self.add_log("💪 永不停止播放器继续运行")
    
    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightgreen")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题
        title_label = tk.Label(main_frame, text="永不停止播放器", 
                              font=("Arial", 18, "bold"), bg="lightgreen")
        title_label.pack(pady=10)
        
        # 问题解决说明
        solution_frame = tk.LabelFrame(main_frame, text="永不停止机制", font=("Arial", 12))
        solution_frame.pack(fill=tk.X, pady=5)
        
        solution_text = """💪 永不停止机制: 绝对不会在视频播放完之前停止
🔒 防护措施: 忽略所有可能导致提前停止的条件
🎯 目标: 确保视频100%播放完毕
⚡ 特点: 强制继续 + 智能恢复 + 用户确认关闭"""
        
        tk.Label(solution_frame, text=solution_text, font=("Arial", 10), 
                justify=tk.LEFT, bg="lightcyan").pack(padx=10, pady=10, fill=tk.X)
        
        # 状态显示
        status_frame = tk.LabelFrame(main_frame, text="播放状态", font=("Arial", 12))
        status_frame.pack(fill=tk.X, pady=5)
        
        # 第一行状态
        status_row1 = tk.Frame(status_frame)
        status_row1.pack(fill=tk.X, padx=10, pady=5)
        
        self.runtime_var = tk.StringVar(value="运行时间: 0秒")
        tk.Label(status_row1, textvariable=self.runtime_var, 
                font=("Arial", 12, "bold"), fg="blue").pack(side=tk.LEFT)
        
        self.play_time_var = tk.StringVar(value="播放时间: 未开始")
        tk.Label(status_row1, textvariable=self.play_time_var, 
                font=("Arial", 12), fg="green").pack(side=tk.RIGHT)
        
        # 第二行状态
        status_row2 = tk.Frame(status_frame)
        status_row2.pack(fill=tk.X, padx=10, pady=5)
        
        self.frame_progress_var = tk.StringVar(value="帧进度: 0/0 (0%)")
        tk.Label(status_row2, textvariable=self.frame_progress_var, 
                font=("Arial", 12)).pack(side=tk.LEFT)
        
        self.force_status_var = tk.StringVar(value="💪 强制继续: 激活")
        tk.Label(status_row2, textvariable=self.force_status_var, 
                font=("Arial", 12), fg="red").pack(side=tk.RIGHT)
        
        # 视频显示区域
        video_frame = tk.LabelFrame(main_frame, text="视频显示", font=("Arial", 12))
        video_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.video_label = tk.Label(video_frame, text="选择视频文件开始永不停止播放", 
                                   font=("Arial", 14), bg="black", fg="white")
        self.video_label.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制区域
        control_frame = tk.Frame(main_frame, bg="lightgreen")
        control_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(control_frame, text="📁 选择视频", font=("Arial", 12),
                 command=self.select_video, bg="lightblue").pack(side=tk.LEFT, padx=5)
        
        self.play_btn = tk.Button(control_frame, text="▶️ 永不停止播放", font=("Arial", 12),
                                 command=self.toggle_play, state="disabled", bg="orange")
        self.play_btn.pack(side=tk.LEFT, padx=5)
        
        self.force_btn = tk.Button(control_frame, text="💪 强制继续", font=("Arial", 12),
                                  command=self.toggle_force_continue, bg="red", fg="white")
        self.force_btn.pack(side=tk.LEFT, padx=5)
        
        self.video_info_var = tk.StringVar(value="未选择视频文件")
        tk.Label(control_frame, textvariable=self.video_info_var, 
                font=("Arial", 10)).pack(side=tk.LEFT, padx=10)
        
        # 进度条
        progress_frame = tk.Frame(main_frame, bg="lightgreen")
        progress_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(progress_frame, text="播放进度:", font=("Arial", 10)).pack(side=tk.LEFT)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)
        
        self.progress_text_var = tk.StringVar(value="0%")
        tk.Label(progress_frame, textvariable=self.progress_text_var, 
                font=("Arial", 10)).pack(side=tk.RIGHT)
        
        # 日志显示
        log_frame = tk.LabelFrame(main_frame, text="永不停止日志", font=("Arial", 12))
        log_frame.pack(fill=tk.X, pady=5)
        
        self.log_text = tk.Text(log_frame, height=8, font=("Consolas", 9), wrap=tk.WORD)
        log_scrollbar = tk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 控制按钮
        button_frame = tk.Frame(main_frame, bg="lightgreen")
        button_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(button_frame, text="🧹 清理内存", command=self.cleanup_memory).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="💾 保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="⚠️ 确认关闭", command=self.manual_close, 
                 bg="red", fg="white").pack(side=tk.RIGHT, padx=5)
        
        # 添加初始日志
        self.add_log("✅ 永不停止播放器启动成功")
        self.add_log("💪 强制继续机制已激活")
        self.add_log("🔒 防意外关闭保护已启用")
        self.add_log("🎯 目标: 确保视频100%播放完毕")
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        runtime = int(time.time() - self.start_time)
        log_entry = f"[{timestamp}] [{runtime:04d}s] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "20.0")
        
        print(log_entry.strip())
    
    def select_video(self):
        """选择视频"""
        try:
            self.add_log("📁 打开文件选择对话框")
            
            file_path = filedialog.askopenfilename(
                title="选择视频文件进行永不停止播放",
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_info_var.set(f"已选择: {filename}")
                self.add_log(f"✅ 选择视频: {filename}")
                
                # 分析视频
                self.analyze_video()
            else:
                self.add_log("❌ 用户取消文件选择")
                
        except Exception as e:
            self.add_log(f"❌ 选择视频失败: {e}")
    
    def analyze_video(self):
        """分析视频"""
        if not self.video_path:
            return
        
        try:
            self.add_log("🔍 分析视频文件...")
            
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
            
            if cap and cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                duration = frame_count / fps if fps > 0 else 0
                
                self.total_expected_frames = frame_count
                
                self.add_log(f"📊 视频信息:")
                self.add_log(f"   分辨率: {width}x{height}")
                self.add_log(f"   帧率: {fps:.2f} fps")
                self.add_log(f"   总帧数: {frame_count}")
                self.add_log(f"   时长: {duration:.2f}秒 ({duration/60:.1f}分钟)")
                
                cap.release()
                
                self.add_log("✅ 视频分析完成，可以开始永不停止播放")
                self.play_btn.configure(state="normal")
                
            else:
                self.add_log("❌ 无法打开视频文件")
                
        except Exception as e:
            self.add_log(f"❌ 视频分析失败: {e}")
    
    def toggle_play(self):
        """切换播放"""
        if not self.video_path:
            return
        
        if not self.is_playing:
            self.start_never_stop_play()
        else:
            self.pause_play()
    
    def start_never_stop_play(self):
        """开始永不停止播放"""
        try:
            self.add_log("🎬 开始永不停止播放")
            self.add_log("💪 强制继续机制已激活，绝对不会提前停止")
            
            self.is_playing = True
            self.play_start_time = time.time()
            self.play_btn.configure(text="⏸️ 暂停播放")
            
            # 重置计数器
            self.frame_count = 0
            self.current_frame = 0
            self.last_progress_report = 0
            
            # 启动永不停止播放线程
            self.play_thread = threading.Thread(target=self.never_stop_play_loop, daemon=True)
            self.play_thread.start()
            
        except Exception as e:
            self.add_log(f"❌ 启动永不停止播放失败: {e}")
    
    def never_stop_play_loop(self):
        """永不停止播放循环"""
        try:
            self.add_log("💪 永不停止播放线程启动")
            
            # 创建capture
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
            if not cap or not cap.isOpened():
                self.root.after(0, lambda: self.add_log("❌ 无法创建capture"))
                return
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            if fps <= 0:
                fps = 30
            
            frame_delay = 1.0 / fps
            recovery_attempts = 0
            
            self.root.after(0, lambda: self.add_log(f"📊 播放参数: FPS={fps:.2f}, 总帧数={total_frames}"))
            
            while self.is_playing and self.running and self.force_continue:
                try:
                    loop_start_time = time.time()
                    
                    # 尝试读取帧
                    ret, frame = cap.read()
                    
                    if ret and frame is not None:
                        # 成功读取
                        self.current_frame += 1
                        self.frame_count += 1
                        recovery_attempts = 0  # 重置恢复计数
                        
                        # 更新进度
                        if total_frames > 0:
                            progress = (self.current_frame / total_frames) * 100
                            self.root.after(0, lambda p=progress: self.progress_var.set(p))
                            self.root.after(0, lambda p=progress: self.progress_text_var.set(f"{p:.1f}%"))
                        
                        # 显示帧
                        try:
                            # 调整大小
                            height, width = frame.shape[:2]
                            if width > 640:
                                scale = 640 / width
                                new_width = int(width * scale)
                                new_height = int(height * scale)
                                frame = cv2.resize(frame, (new_width, new_height))
                            
                            # 转换为RGB
                            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                            pil_image = Image.fromarray(frame_rgb)
                            photo = ImageTk.PhotoImage(pil_image)
                            
                            # 更新显示
                            self.root.after(0, lambda p=photo: self.update_video_display(p))
                            
                        except Exception as display_error:
                            # 显示错误不影响播放
                            pass
                        
                        # 每5000帧报告一次
                        if self.current_frame - self.last_progress_report >= 5000:
                            play_time = time.time() - self.play_start_time
                            completion = (self.current_frame / total_frames * 100) if total_frames > 0 else 0
                            
                            self.root.after(0, lambda cf=self.current_frame, tf=total_frames, pt=play_time, comp=completion: 
                                          self.add_log(f"💪 永不停止进度: {cf}/{tf} ({comp:.1f}%), 播放{pt:.1f}s"))
                            self.last_progress_report = self.current_frame
                        
                        # 检查是否播放完毕
                        if self.current_frame >= total_frames:
                            self.root.after(0, lambda: 
                                          self.add_log(f"🎉 视频100%播放完毕！共播放{self.current_frame}帧"))
                            break
                        
                    else:
                        # 读取失败，但永不停止机制启动
                        recovery_attempts += 1
                        current_pos = cap.get(cv2.CAP_PROP_POS_FRAMES)
                        
                        self.root.after(0, lambda ra=recovery_attempts, cp=current_pos: 
                                      self.add_log(f"💪 读取失败但永不停止 (第{ra}次恢复, 位置{cp})"))
                        
                        if recovery_attempts < 50:  # 允许更多恢复尝试
                            # 尝试多种恢复方法
                            if recovery_attempts % 10 == 0:
                                # 每10次尝试重新创建capture
                                self.root.after(0, lambda: self.add_log("🔄 重新创建capture进行恢复"))
                                cap.release()
                                time.sleep(0.2)
                                cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
                                if cap and cap.isOpened():
                                    cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)
                            else:
                                # 尝试跳过当前帧
                                try:
                                    cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame + 1)
                                except:
                                    pass
                            
                            time.sleep(0.1)
                            continue
                        else:
                            # 如果还没播放到95%就失败太多次，强制继续
                            if self.current_frame < total_frames * 0.95:
                                self.root.after(0, lambda: 
                                              self.add_log(f"💪 强制继续: 虽然恢复{recovery_attempts}次，但绝不放弃"))
                                recovery_attempts = 0  # 重置计数，继续尝试
                                time.sleep(1)
                                continue
                            else:
                                self.root.after(0, lambda: 
                                              self.add_log(f"✅ 已播放95%以上，认为基本完成"))
                                break
                    
                    # 控制播放速度
                    elapsed = time.time() - loop_start_time
                    sleep_time = frame_delay - elapsed
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                        
                except Exception as e:
                    error_msg = str(e)
                    self.root.after(0, lambda err=error_msg: 
                                  self.add_log(f"💪 播放异常但永不停止: {err}"))
                    
                    # 即使有异常也要继续
                    recovery_attempts += 1
                    if recovery_attempts < 100:  # 允许更多异常恢复
                        time.sleep(0.2)
                        continue
                    else:
                        self.root.after(0, lambda: 
                                      self.add_log(f"💪 异常过多但仍然尝试继续"))
                        recovery_attempts = 0
                        time.sleep(1)
                        continue
            
            # 播放结束
            cap.release()
            
            play_duration = time.time() - self.play_start_time if self.play_start_time else 0
            completion_rate = (self.current_frame / total_frames * 100) if total_frames > 0 else 0
            
            self.root.after(0, lambda: self.add_log("=" * 50))
            self.root.after(0, lambda pd=play_duration, cr=completion_rate: 
                          self.add_log(f"🎉 永不停止播放结束: 播放{pd:.1f}s, 完成{cr:.1f}%"))
            self.root.after(0, lambda: self.add_log("=" * 50))
            
        except Exception as e:
            self.root.after(0, lambda err=str(e): 
                          self.add_log(f"❌ 永不停止播放线程异常: {err}"))
        finally:
            self.root.after(0, lambda: self.play_btn.configure(text="▶️ 永不停止播放"))
            self.is_playing = False

    def update_video_display(self, photo):
        """更新视频显示"""
        try:
            self.video_label.configure(image=photo)
            self.video_label.image = photo  # 保持引用
        except:
            pass

    def pause_play(self):
        """暂停播放"""
        self.add_log("⏸️ 暂停播放（但不会真正停止）")
        self.is_playing = False
        self.play_btn.configure(text="▶️ 永不停止播放")

    def toggle_force_continue(self):
        """切换强制继续模式"""
        self.force_continue = not self.force_continue

        if self.force_continue:
            self.add_log("💪 强制继续模式: 已激活")
            self.force_btn.configure(text="💪 强制继续", bg="red")
            self.force_status_var.set("💪 强制继续: 激活")
        else:
            self.add_log("⚠️ 强制继续模式: 已禁用")
            self.force_btn.configure(text="⚠️ 允许停止", bg="orange")
            self.force_status_var.set("⚠️ 强制继续: 禁用")

    def start_monitoring(self):
        """启动监控"""
        def monitor():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)

                    # 更新运行时间
                    self.runtime_var.set(f"运行时间: {runtime}秒 ({runtime//60}分{runtime%60}秒)")

                    # 更新播放时间
                    if self.play_start_time:
                        play_time = time.time() - self.play_start_time
                        self.play_time_var.set(f"播放时间: {play_time:.1f}秒")
                    else:
                        self.play_time_var.set("播放时间: 未开始")

                    # 更新帧进度
                    if self.total_expected_frames > 0:
                        progress = (self.current_frame / self.total_expected_frames) * 100
                        self.frame_progress_var.set(f"帧进度: {self.current_frame}/{self.total_expected_frames} ({progress:.1f}%)")
                    else:
                        self.frame_progress_var.set(f"帧进度: {self.current_frame}/未知")

                    # 每分钟强制报告状态
                    if runtime > 0 and runtime % 60 == 0 and self.is_playing:
                        self.add_log(f"💓 永不停止运行状态: {runtime//60}分钟, 播放{self.current_frame}帧")

                    # 继续监控
                    self.root.after(1000, monitor)

                except Exception as e:
                    print(f"监控错误: {e}")
                    self.root.after(5000, monitor)

        monitor()
        self.add_log("💓 监控系统启动")

    def cleanup_memory(self):
        """清理内存"""
        try:
            before = len(gc.get_objects())
            collected = gc.collect()
            after = len(gc.get_objects())
            self.add_log(f"🧹 内存清理: 回收{collected}个对象, 对象数量 {before} -> {after}")
        except Exception as e:
            self.add_log(f"❌ 内存清理失败: {e}")

    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get("1.0", tk.END)
            timestamp = int(time.time())
            filename = f"never_stop_log_{timestamp}.txt"

            with open(filename, "w", encoding="utf-8") as f:
                f.write(f"永不停止播放器日志\n")
                f.write(f"生成时间: {time.ctime()}\n")
                f.write(f"视频文件: {self.video_path}\n")
                f.write(f"强制继续: {self.force_continue}\n")
                f.write("=" * 50 + "\n")
                f.write(log_content)

            self.add_log(f"💾 日志已保存: {filename}")
        except Exception as e:
            self.add_log(f"❌ 保存日志失败: {e}")

    def manual_close(self):
        """手动关闭（需要确认）"""
        self.add_log("⚠️ 用户请求关闭")

        if self.is_playing:
            result = messagebox.askyesno("确认关闭",
                                       "视频正在播放中！\n\n"
                                       "真的要关闭永不停止播放器吗？\n"
                                       "这将中断视频播放。")
            if not result:
                self.add_log("❌ 用户取消关闭，继续播放")
                return

        runtime = int(time.time() - self.start_time)
        completion = (self.current_frame / self.total_expected_frames * 100) if self.total_expected_frames > 0 else 0

        result = messagebox.askyesno("最终确认",
                                   f"最终确认关闭永不停止播放器？\n\n"
                                   f"运行时间: {runtime//60}分{runtime%60}秒\n"
                                   f"播放进度: {completion:.1f}%\n"
                                   f"播放帧数: {self.current_frame}")
        if result:
            self.add_log("✅ 用户最终确认关闭")
            self.user_confirmed_close = True
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消最终关闭，继续运行")

    def on_closing(self):
        """窗口关闭处理（强力防护）"""
        if self.user_confirmed_close:
            return

        self.add_log("🛡️ 检测到窗口关闭事件，启动防护")

        if self.is_playing:
            self.add_log("💪 视频正在播放，拒绝关闭")
            messagebox.showwarning("拒绝关闭",
                                 "永不停止播放器正在播放视频！\n\n"
                                 "如果真的要关闭，请点击'确认关闭'按钮。")
            return

        result = messagebox.askyesno("防护确认",
                                   "永不停止播放器防护系统询问：\n\n"
                                   "真的要关闭吗？\n"
                                   "建议使用'确认关闭'按钮。")
        if result:
            self.add_log("✅ 用户通过防护确认")
            self.user_confirmed_close = True
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("🛡️ 防护成功，继续运行")

    def cleanup(self):
        """清理资源"""
        self.add_log("🧹 开始清理资源...")
        self.running = False
        self.is_playing = False
        self.force_continue = False

        if self.cap:
            try:
                self.cap.release()
                self.add_log("✅ 视频资源已释放")
            except:
                self.add_log("⚠️ 视频资源释放失败")

        if self.play_thread and self.play_thread.is_alive():
            self.add_log("⏳ 等待播放线程结束...")
            self.play_thread.join(timeout=5.0)

        self.add_log("✅ 资源清理完成")

    def run(self):
        """运行播放器"""
        try:
            print("🎬 启动永不停止播放器界面...")
            print("💪 绝对不会在视频播放完之前停止")

            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    filename = os.path.basename(video_file)
                    self.video_info_var.set(f"命令行: {filename}")
                    self.add_log(f"📁 命令行参数: {filename}")
                    self.root.after(1000, self.analyze_video)
                else:
                    self.add_log(f"❌ 命令行文件不存在: {video_file}")

            # 启动主循环
            self.root.mainloop()

            print("🔚 永不停止播放器正常结束")

        except Exception as e:
            print(f"❌ 播放器异常: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("💪 永不停止播放器")
    print("=" * 60)
    print("问题: 视频未播放完就自动结束并关闭程序")
    print("解决: 强制继续机制，绝对不会提前停止")
    print("特点: 智能恢复 + 防意外关闭 + 用户确认")
    print("目标: 确保视频100%播放完毕")
    print("=" * 60)

    try:
        player = NeverStopPlayer()
        player.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
