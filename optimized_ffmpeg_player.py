#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的ffmpeg播放器 - 不使用音频剪切方案
通过改进ffmpeg配置和同步策略来优化性能
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk

# 🚀 优化的ffmpeg环境配置
print("🔧 设置优化的ffmpeg环境...")

# 基础ffmpeg配置 - 平衡性能和稳定性
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '100'

# 优化的线程配置 - 不完全禁用线程，而是优化线程使用
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;2|thread_type;1|thread_count;2'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '2'  # 使用2个线程而不是1个

# 启用适度的硬件加速
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '0'  # 启用硬件加速

# 优化的缓冲配置
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;2|buffer_size;32768|max_delay;500000'

# 改进的seek配置 - 不完全禁用异步，而是优化
os.environ['OPENCV_FFMPEG_SEEK_BUFFER_SIZE'] = '4096'  # 适中的缓冲区大小
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '0'  # 启用异步，但控制好

# 错误恢复配置
os.environ['OPENCV_FFMPEG_THREAD_SAFE'] = '1'  # 启用线程安全
os.environ['FFMPEG_THREAD_SAFE'] = '1'

print("✅ 优化ffmpeg环境设置完成")

# 检查依赖
try:
    import pygame
    pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=2048)
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用")
except Exception as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")

class OptimizedFFmpegPlayer:
    def __init__(self, root, video_path=None):
        self.root = root
        self.root.title("优化ffmpeg播放器 - 无音频剪切版本")
        self.root.geometry("1000x700")
        
        # 播放状态
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.duration = 0
        
        # 视频相关
        self.cap = None
        self.video_path = None
        self.video_thread = None
        
        # 音频相关 - 不使用剪切方案
        self.audio_clip = None
        self.audio_start_time = None
        self.audio_offset = 0  # 音频偏移量（秒）
        self.audio_enabled = True
        
        # 同步控制
        self.play_start_time = None
        self.video_start_frame = 0
        self.sync_lock = threading.Lock()
        
        # 性能优化
        self.frame_cache = {}  # 帧缓存
        self.cache_size = 30   # 缓存30帧
        self.last_seek_time = 0
        self.seek_debounce = 0.3  # 300ms防抖
        
        # seek优化
        self.seek_in_progress = False
        self.pending_seek_frame = None
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示区域
        video_container = ttk.Frame(main_frame)
        video_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = tk.Label(video_container, bg='black', fg='white',
                                   text="优化ffmpeg播放器\n\n特点:\n• 优化的ffmpeg配置\n• 不使用音频剪切\n• 改进的同步策略\n• 智能缓存机制\n\n点击'打开视频'开始")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="播放控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮行
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="📁 打开视频", command=self.open_video).pack(side=tk.LEFT, padx=(0, 5))
        
        self.play_btn = ttk.Button(btn_frame, text="▶️ 播放", command=self.toggle_play)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(btn_frame, text="⏹️ 停止", command=self.stop).pack(side=tk.LEFT, padx=(0, 5))
        
        # 音频控制
        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="🔊 音频", variable=self.audio_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 进度控制
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_seek)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))
        
        self.time_label = ttk.Label(progress_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 信息面板
        info_frame = ttk.LabelFrame(main_frame, text="播放信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_label = ttk.Label(info_frame, text="未加载视频")
        self.info_label.pack(padx=5, pady=5)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪 - 优化ffmpeg播放器", relief=tk.SUNKEN)
        self.status_label.pack(fill=tk.X)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def open_video(self):
        """打开视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("常见视频", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv"),
                ("MP4文件", "*.mp4"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频文件 - 优化版本"""
        try:
            print(f"📁 加载视频: {video_path}")
            self.status_label.configure(text="正在加载视频...")
            
            # 停止当前播放
            self.stop()
            
            # 清理缓存
            self.frame_cache.clear()
            
            # 使用优化的ffmpeg后端
            print("🔧 使用优化的ffmpeg后端...")
            self.cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
            
            if not self.cap.isOpened():
                print("⚠️ ffmpeg后端失败，尝试默认后端...")
                self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 验证参数
            if self.total_frames <= 0:
                self.total_frames = 10000
            if self.fps <= 0 or self.fps > 120:
                self.fps = 30
            
            self.duration = self.total_frames / self.fps
            
            print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps, {width}x{height}")
            
            # 设置进度条
            self.progress_bar.configure(to=self.total_frames-1)
            self.current_frame = 0
            
            # 显示第一帧
            self.show_current_frame()
            
            # 加载音频 - 不使用剪切方案
            self.load_audio_no_cutting(video_path)
            
            # 更新界面
            self.video_path = video_path
            filename = os.path.basename(video_path)
            self.info_label.configure(text=f"文件: {filename}\n分辨率: {width}x{height}\n帧率: {self.fps}fps\n时长: {self.format_time(self.duration)}")
            self.status_label.configure(text=f"已加载: {filename}")
            
        except Exception as e:
            error_msg = f"加载视频失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            self.status_label.configure(text="加载失败")
    
    def load_audio_no_cutting(self, video_path):
        """加载音频 - 不使用剪切方案"""
        if not self.audio_var.get() or not MOVIEPY_AVAILABLE:
            print("🔇 音频已禁用或moviepy不可用")
            return
        
        def load_in_thread():
            try:
                print("🔊 加载完整音频...")
                self.root.after(0, lambda: self.status_label.configure(text="正在加载音频..."))
                
                # 使用moviepy加载完整音频
                video_clip = VideoFileClip(video_path)
                
                if video_clip.audio is not None:
                    self.audio_clip = video_clip.audio
                    print(f"✅ 音频加载成功: {self.audio_clip.duration:.2f}秒")
                    self.root.after(0, lambda: self.status_label.configure(text="音频加载完成"))
                else:
                    print("⚠️ 视频文件没有音频轨道")
                    self.audio_clip = None
                    self.root.after(0, lambda: self.status_label.configure(text="无音频轨道"))
                    
            except Exception as e:
                print(f"❌ 音频加载失败: {e}")
                self.audio_clip = None
                self.root.after(0, lambda: self.status_label.configure(text="音频加载失败"))
        
        # 在后台线程中加载音频
        threading.Thread(target=load_in_thread, daemon=True).start()

    def show_current_frame(self):
        """显示当前帧 - 带缓存优化"""
        if not self.cap:
            return

        try:
            # 检查缓存
            if self.current_frame in self.frame_cache:
                frame = self.frame_cache[self.current_frame]
            else:
                # 设置帧位置
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)

                ret, frame = self.cap.read()
                if not ret:
                    return

                # 添加到缓存
                if len(self.frame_cache) < self.cache_size:
                    self.frame_cache[self.current_frame] = frame.copy()

            # 转换颜色空间
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 获取显示区域大小
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()

            if label_width > 1 and label_height > 1:
                # 计算缩放比例
                h, w = frame_rgb.shape[:2]
                scale = min(label_width/w, label_height/h)
                new_w, new_h = int(w*scale), int(h*scale)

                # 调整大小
                if new_w > 0 and new_h > 0:
                    frame_resized = cv2.resize(frame_rgb, (new_w, new_h))

                    # 转换为PIL图像并显示
                    pil_image = Image.fromarray(frame_resized)
                    photo = ImageTk.PhotoImage(pil_image)

                    self.video_label.configure(image=photo, text="")
                    self.video_label.image = photo

            # 更新进度条和时间
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            total_time = self.duration
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")

        except Exception as e:
            print(f"❌ 显示帧失败: {e}")

    def format_time(self, seconds):
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"

    def toggle_play(self):
        """播放/暂停切换"""
        if not self.cap:
            messagebox.showwarning("警告", "请先加载视频文件")
            return

        if self.is_playing:
            self.pause()
        else:
            self.play()

    def play(self):
        """开始播放 - 优化版本"""
        if not self.cap:
            return

        self.is_playing = True
        self.play_btn.configure(text="⏸️ 暂停")

        # 设置播放起始时间和帧
        with self.sync_lock:
            self.play_start_time = time.time()
            self.video_start_frame = self.current_frame
            self.audio_offset = self.current_frame / self.fps

        # 播放音频 - 不使用剪切方案
        self.play_audio_no_cutting()

        # 启动视频播放线程
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.optimized_playback_loop, daemon=True)
            self.video_thread.start()

        self.status_label.configure(text="正在播放...")
        print("▶️ 开始播放")

    def pause(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 暂停音频
        self.pause_audio()

        self.status_label.configure(text="已暂停")
        print("⏸️ 暂停播放")

    def stop(self):
        """停止播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 停止音频
        self.stop_audio()

        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.show_current_frame()

        self.status_label.configure(text="已停止")
        print("⏹️ 停止播放")

    def optimized_playback_loop(self):
        """优化的播放循环 - 改进同步策略"""
        print("🎬 启动优化播放循环")

        while self.is_playing and self.cap:
            try:
                current_time = time.time()

                # 计算应该显示的帧
                with self.sync_lock:
                    if self.play_start_time:
                        elapsed_time = current_time - self.play_start_time
                        target_frame = self.video_start_frame + int(elapsed_time * self.fps)

                        # 智能帧跳跃 - 更保守的策略
                        frame_diff = target_frame - self.current_frame

                        if frame_diff > 10:  # 只有差距很大时才跳帧
                            print(f"🔄 大幅跳帧: {self.current_frame} -> {target_frame}")
                            self.current_frame = target_frame
                            # 清理缓存，因为跳帧了
                            self.frame_cache.clear()
                        elif frame_diff > 0:
                            # 正常前进
                            self.current_frame = min(target_frame, self.current_frame + 3)  # 限制跳跃幅度

                        # 检查播放结束
                        if self.current_frame >= self.total_frames:
                            self.root.after(0, self.stop)
                            break

                # 更新显示
                self.root.after(0, self.show_current_frame)

                # 控制播放速度 - 更精确的时间控制
                frame_time = 1.0 / self.fps
                time.sleep(frame_time * 0.8)  # 稍微快一点，让同步算法有调整空间

            except Exception as e:
                print(f"❌ 播放循环错误: {e}")
                break

        print("🎬 播放循环结束")

    def play_audio_no_cutting(self):
        """播放音频 - 不使用剪切方案"""
        if not self.audio_var.get() or not self.audio_clip or not PYGAME_AVAILABLE:
            return

        try:
            print(f"🔊 播放完整音频，偏移: {self.audio_offset:.2f}秒")

            # 创建临时音频文件
            import tempfile
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_audio_path = temp_file.name

            # 导出完整音频
            self.audio_clip.write_audiofile(
                temp_audio_path,
                verbose=False,
                logger=None,
                codec='pcm_s16le',
                ffmpeg_params=['-ar', '44100', '-ac', '2']
            )

            # 使用pygame播放
            pygame.mixer.music.load(temp_audio_path)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()

            # 记录音频开始时间，考虑偏移
            self.audio_start_time = time.time() - self.audio_offset

            print("✅ 音频播放开始")

            # 清理临时文件（延迟删除）
            def cleanup():
                time.sleep(2)
                try:
                    if os.path.exists(temp_audio_path):
                        os.unlink(temp_audio_path)
                except:
                    pass

            threading.Thread(target=cleanup, daemon=True).start()

        except Exception as e:
            print(f"❌ 音频播放失败: {e}")

    def pause_audio(self):
        """暂停音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.pause()
            print("🔊 音频已暂停")
        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.stop()
            self.audio_start_time = None
            self.audio_offset = 0
            print("🔊 音频已停止")
        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def on_seek(self, value):
        """进度条拖动处理 - 优化版本，减少音频重启"""
        if not self.cap:
            return

        current_time = time.time()

        # 防抖处理
        if current_time - self.last_seek_time < self.seek_debounce:
            self.pending_seek_frame = int(float(value))
            return

        self.last_seek_time = current_time

        try:
            frame_num = int(float(value))

            # 防止重复seek
            if self.seek_in_progress:
                self.pending_seek_frame = frame_num
                return

            self.seek_in_progress = True

            try:
                # 计算跳转距离
                frame_diff = abs(frame_num - self.current_frame)
                seek_time = frame_num / self.fps

                # 清理缓存
                self.frame_cache.clear()

                with self.sync_lock:
                    self.current_frame = frame_num

                    # 如果正在播放，更新时间基准
                    if self.is_playing:
                        self.play_start_time = time.time()
                        self.video_start_frame = frame_num
                        self.audio_offset = frame_num / self.fps

                        # 只有大幅跳转时才重新开始音频
                        if frame_diff > 30:  # 超过1秒的跳转才重新开始音频
                            print(f"🔊 大幅跳转({frame_diff}帧)，重新开始音频")
                            self.stop_audio()
                            self.play_audio_no_cutting()
                        else:
                            print(f"🔊 小幅跳转({frame_diff}帧)，保持音频播放")

                # 显示当前帧
                if not self.is_playing:
                    self.show_current_frame()

                print(f"🎬 跳转到: 帧{frame_num} ({seek_time:.2f}秒)")

            finally:
                self.seek_in_progress = False

                # 处理待处理的seek
                if self.pending_seek_frame is not None:
                    pending = self.pending_seek_frame
                    self.pending_seek_frame = None
                    self.root.after(100, lambda: self.on_seek(str(pending)))

        except Exception as e:
            print(f"❌ 跳转失败: {e}")
            self.seek_in_progress = False

    def on_close(self):
        """程序关闭处理"""
        print("🧹 清理资源...")

        # 停止播放
        self.stop()

        # 释放视频资源
        if self.cap:
            self.cap.release()

        # 清理音频资源
        if self.audio_clip:
            self.audio_clip.close()

        # 退出pygame
        if PYGAME_AVAILABLE:
            pygame.mixer.quit()

        print("✅ 资源清理完成")
        self.root.destroy()


def main():
    """主函数"""
    print("=" * 60)
    print("🎬 优化ffmpeg播放器 - 无音频剪切版本")
    print("=" * 60)
    print("优化特点:")
    print("• 改进的ffmpeg配置 - 平衡性能和稳定性")
    print("• 不使用音频剪切 - 播放完整音频文件")
    print("• 智能缓存机制 - 提高播放流畅度")
    print("• 优化的同步策略 - 减少跳帧和卡顿")
    print("• 防抖seek处理 - 避免频繁操作")
    print("=" * 60)

    root = tk.Tk()

    # 检查命令行参数
    video_path = None
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"⚠️ 文件不存在: {video_path}")
            video_path = None

    app = OptimizedFFmpegPlayer(root, video_path)
    root.mainloop()


if __name__ == "__main__":
    main()
