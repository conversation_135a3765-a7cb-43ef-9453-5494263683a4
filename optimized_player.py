#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的视频播放器 - 完全避免ffmpeg，优化音视频同步
使用Windows MSMF后端播放视频，pygame播放音频
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk

# 设置最优环境变量
print("🔧 设置优化环境...")
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '100'      # 最高优先级MSMF
os.environ['OPENCV_VIDEOIO_PRIORITY_DSHOW'] = '90'      # 次优先级DirectShow
os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '0'      # 完全禁用FFMPEG
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '1'  # 硬件加速
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = ''        # 清空ffmpeg选项
print("✅ 优化环境设置完成")

# 检查依赖
try:
    import pygame
    pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=1024)
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 音频引擎可用")
except Exception as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")

class OptimizedPlayer:
    def __init__(self, root, video_path=None):
        self.root = root
        self.root.title("优化视频播放器 - 无ffmpeg版本")
        self.root.geometry("1000x700")
        
        # 播放状态
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.duration = 0
        
        # 视频相关
        self.cap = None
        self.video_path = None
        self.video_thread = None
        
        # 音频相关
        self.audio_file = None
        self.audio_enabled = True
        
        # 同步控制
        self.play_start_time = None
        self.video_start_frame = 0
        self.sync_lock = threading.Lock()
        
        # 性能优化
        self.frame_skip_threshold = 5  # 帧跳跃阈值
        self.last_update_time = 0
        self.update_interval = 1/60    # 60fps更新频率
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示区域
        video_container = ttk.Frame(main_frame)
        video_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = tk.Label(video_container, bg='black', fg='white',
                                   text="优化视频播放器\n\n特点:\n• 使用Windows MSMF后端\n• 避免ffmpeg相关问题\n• 优化的音视频同步\n\n点击'打开视频'开始")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="播放控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮行
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="📁 打开视频", command=self.open_video).pack(side=tk.LEFT, padx=(0, 5))
        
        self.play_btn = ttk.Button(btn_frame, text="▶️ 播放", command=self.toggle_play)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(btn_frame, text="⏹️ 停止", command=self.stop).pack(side=tk.LEFT, padx=(0, 5))
        
        # 音频控制
        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="🔊 音频", variable=self.audio_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 进度控制
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_seek)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))
        
        self.time_label = ttk.Label(progress_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 信息面板
        info_frame = ttk.LabelFrame(main_frame, text="视频信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_label = ttk.Label(info_frame, text="未加载视频")
        self.info_label.pack(padx=5, pady=5)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪 - 请选择视频文件", relief=tk.SUNKEN)
        self.status_label.pack(fill=tk.X)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def open_video(self):
        """打开视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("常见视频", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv"),
                ("MP4文件", "*.mp4"),
                ("AVI文件", "*.avi"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            print(f"📁 加载视频: {video_path}")
            self.status_label.configure(text="正在加载视频...")
            
            # 停止当前播放
            self.stop()
            
            # 尝试多种后端
            backends = [
                ("MSMF", cv2.CAP_MSMF),
                ("DirectShow", cv2.CAP_DSHOW),
                ("默认", None)
            ]
            
            self.cap = None
            for name, backend in backends:
                try:
                    print(f"🔧 尝试{name}后端...")
                    
                    if backend is not None:
                        self.cap = cv2.VideoCapture(video_path, backend)
                    else:
                        self.cap = cv2.VideoCapture(video_path)
                    
                    if self.cap.isOpened():
                        # 测试读取
                        ret, test_frame = self.cap.read()
                        if ret and test_frame is not None:
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置
                            print(f"✅ {name}后端成功")
                            break
                        else:
                            self.cap.release()
                            self.cap = None
                    else:
                        if self.cap:
                            self.cap.release()
                        self.cap = None
                        
                except Exception as e:
                    print(f"❌ {name}后端失败: {e}")
                    if self.cap:
                        self.cap.release()
                    self.cap = None
                    continue
            
            if not self.cap:
                raise Exception("所有视频后端都无法打开文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 验证和修正参数
            if self.total_frames <= 0:
                self.total_frames = 10000  # 默认值
            if self.fps <= 0 or self.fps > 120:
                self.fps = 30  # 默认帧率
            
            self.duration = self.total_frames / self.fps
            
            print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps, {width}x{height}, {self.duration:.1f}秒")
            
            # 设置进度条
            self.progress_bar.configure(to=self.total_frames-1)
            self.current_frame = 0
            
            # 显示第一帧
            self.show_current_frame()
            
            # 提取音频
            self.extract_audio(video_path)
            
            # 更新界面
            self.video_path = video_path
            filename = os.path.basename(video_path)
            self.info_label.configure(text=f"文件: {filename}\n分辨率: {width}x{height}\n帧率: {self.fps}fps\n时长: {self.format_time(self.duration)}")
            self.status_label.configure(text=f"已加载: {filename}")
            
        except Exception as e:
            error_msg = f"加载视频失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            self.status_label.configure(text="加载失败")
    
    def extract_audio(self, video_path):
        """提取音频"""
        if not self.audio_var.get():
            print("🔇 音频已禁用")
            return
        
        def extract_in_thread():
            try:
                print("🔊 开始提取音频...")
                self.root.after(0, lambda: self.status_label.configure(text="正在提取音频..."))
                
                import tempfile
                import subprocess
                
                # 创建临时音频文件
                self.audio_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False).name
                
                # 使用ffmpeg提取音频（如果可用）
                cmd = [
                    'ffmpeg', '-i', video_path,
                    '-vn', '-acodec', 'pcm_s16le',
                    '-ar', '44100', '-ac', '2',
                    '-y', self.audio_file
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0 and os.path.exists(self.audio_file):
                    file_size = os.path.getsize(self.audio_file)
                    print(f"✅ 音频提取成功: {file_size} 字节")
                    self.root.after(0, lambda: self.status_label.configure(text="音频提取完成"))
                else:
                    print("⚠️ 音频提取失败，将无音频播放")
                    self.audio_file = None
                    self.root.after(0, lambda: self.status_label.configure(text="音频提取失败"))
                    
            except subprocess.TimeoutExpired:
                print("⚠️ 音频提取超时")
                self.audio_file = None
            except FileNotFoundError:
                print("⚠️ 系统未安装ffmpeg，跳过音频")
                self.audio_file = None
            except Exception as e:
                print(f"❌ 音频提取异常: {e}")
                self.audio_file = None
        
        # 在后台线程中提取音频
        threading.Thread(target=extract_in_thread, daemon=True).start()

    def show_current_frame(self):
        """显示当前帧"""
        if not self.cap:
            return

        try:
            # 设置帧位置
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)

            ret, frame = self.cap.read()
            if not ret:
                return

            # 转换颜色空间
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 获取显示区域大小
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()

            if label_width > 1 and label_height > 1:
                # 计算缩放比例，保持宽高比
                h, w = frame_rgb.shape[:2]
                scale = min(label_width/w, label_height/h)
                new_w, new_h = int(w*scale), int(h*scale)

                # 调整大小
                if new_w > 0 and new_h > 0:
                    frame_resized = cv2.resize(frame_rgb, (new_w, new_h), interpolation=cv2.INTER_LINEAR)

                    # 转换为PIL图像并显示
                    pil_image = Image.fromarray(frame_resized)
                    photo = ImageTk.PhotoImage(pil_image)

                    self.video_label.configure(image=photo, text="")
                    self.video_label.image = photo  # 保持引用

            # 更新进度条和时间显示
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            total_time = self.duration
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")

        except Exception as e:
            print(f"❌ 显示帧失败: {e}")

    def format_time(self, seconds):
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"

    def toggle_play(self):
        """播放/暂停切换"""
        if not self.cap:
            messagebox.showwarning("警告", "请先加载视频文件")
            return

        if self.is_playing:
            self.pause()
        else:
            self.play()

    def play(self):
        """开始播放"""
        if not self.cap:
            return

        self.is_playing = True
        self.play_btn.configure(text="⏸️ 暂停")

        # 设置播放起始时间
        with self.sync_lock:
            self.play_start_time = time.time()
            self.video_start_frame = self.current_frame

        # 播放音频
        self.play_audio()

        # 启动视频播放线程
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.video_playback_loop, daemon=True)
            self.video_thread.start()

        self.status_label.configure(text="正在播放...")
        print("▶️ 开始播放")

    def pause(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 暂停音频
        self.pause_audio()

        self.status_label.configure(text="已暂停")
        print("⏸️ 暂停播放")

    def stop(self):
        """停止播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 停止音频
        self.stop_audio()

        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.show_current_frame()

        self.status_label.configure(text="已停止")
        print("⏹️ 停止播放")

    def video_playback_loop(self):
        """优化的视频播放循环"""
        print("🎬 启动优化播放循环")

        while self.is_playing and self.cap:
            try:
                current_time = time.time()

                # 控制更新频率
                if current_time - self.last_update_time < self.update_interval:
                    time.sleep(0.001)  # 短暂休眠
                    continue

                self.last_update_time = current_time

                # 计算应该显示的帧
                with self.sync_lock:
                    if self.play_start_time:
                        elapsed_time = current_time - self.play_start_time
                        target_frame = self.video_start_frame + int(elapsed_time * self.fps)

                        # 检查是否需要跳帧
                        frame_diff = target_frame - self.current_frame

                        if frame_diff > self.frame_skip_threshold:
                            # 跳帧到目标位置
                            self.current_frame = target_frame
                            print(f"🔄 跳帧到: {target_frame}")
                        elif frame_diff >= 1:
                            # 正常前进
                            self.current_frame = target_frame

                        # 检查是否播放结束
                        if self.current_frame >= self.total_frames:
                            self.root.after(0, self.stop)
                            break

                # 更新显示
                self.root.after(0, self.show_current_frame)

                # 控制播放速度
                time.sleep(1 / (self.fps * 2))  # 稍微快一点的循环

            except Exception as e:
                print(f"❌ 播放循环错误: {e}")
                break

        print("🎬 播放循环结束")

    def play_audio(self):
        """播放音频"""
        if not self.audio_var.get() or not self.audio_file or not PYGAME_AVAILABLE:
            return

        try:
            # 计算音频开始位置
            audio_start_time = self.current_frame / self.fps

            print(f"🔊 播放音频，起始位置: {audio_start_time:.2f}秒")

            # 加载并播放音频
            pygame.mixer.music.load(self.audio_file)
            pygame.mixer.music.set_volume(1.0)

            # pygame不支持指定开始位置，这里使用简单的播放
            pygame.mixer.music.play()

            print("✅ 音频播放开始")

        except Exception as e:
            print(f"❌ 音频播放失败: {e}")

    def pause_audio(self):
        """暂停音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.pause()
            print("🔊 音频已暂停")
        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.stop()
            print("🔊 音频已停止")
        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def on_seek(self, value):
        """进度条拖动处理"""
        if not self.cap:
            return

        try:
            frame_num = int(float(value))

            with self.sync_lock:
                self.current_frame = frame_num

                # 如果正在播放，更新时间基准
                if self.is_playing:
                    self.play_start_time = time.time()
                    self.video_start_frame = frame_num

                    # 重新开始音频
                    self.stop_audio()
                    self.play_audio()

            # 显示当前帧
            if not self.is_playing:
                self.show_current_frame()

            seek_time = frame_num / self.fps
            print(f"🎬 跳转到: 帧{frame_num} ({seek_time:.2f}秒)")

        except Exception as e:
            print(f"❌ 跳转失败: {e}")

    def on_close(self):
        """程序关闭处理"""
        print("🧹 清理资源...")

        # 停止播放
        self.stop()

        # 释放视频资源
        if self.cap:
            self.cap.release()

        # 清理音频文件
        if self.audio_file and os.path.exists(self.audio_file):
            try:
                os.unlink(self.audio_file)
                print("🧹 临时音频文件已清理")
            except Exception as e:
                print(f"⚠️ 清理音频文件失败: {e}")

        # 退出pygame
        if PYGAME_AVAILABLE:
            pygame.mixer.quit()

        print("✅ 资源清理完成")
        self.root.destroy()


def main():
    """主函数"""
    print("=" * 60)
    print("🎬 优化视频播放器 - 无ffmpeg版本")
    print("=" * 60)
    print("特点:")
    print("• 使用Windows MSMF/DirectShow后端")
    print("• 完全避免ffmpeg相关问题")
    print("• 优化的音视频同步算法")
    print("• 智能帧跳跃和性能优化")
    print("=" * 60)

    root = tk.Tk()

    # 检查命令行参数
    video_path = None
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"⚠️ 文件不存在: {video_path}")
            video_path = None

    app = OptimizedPlayer(root, video_path)
    root.mainloop()


if __name__ == "__main__":
    main()
