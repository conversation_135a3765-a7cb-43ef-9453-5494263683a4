#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯Python视频播放器 - 完全避免ffmpeg
使用OpenCV MSMF后端播放视频，pygame播放音频
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk

# 设置环境变量 - 完全禁用ffmpeg
print("🔧 设置纯Python环境...")
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '100'
os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_DSHOW'] = '90'
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '1'
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = ''  # 清空ffmpeg选项
print("✅ 纯Python环境设置完成")

# 检查依赖
try:
    import pygame
    pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用")
except Exception as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")

try:
    import winsound
    WINSOUND_AVAILABLE = True
    print("✅ winsound 可用")
except ImportError:
    WINSOUND_AVAILABLE = False
    print("❌ winsound 不可用")

class PurePythonPlayer:
    def __init__(self, root, video_path=None):
        self.root = root
        self.root.title("纯Python视频播放器")
        self.root.geometry("900x600")
        
        # 播放状态
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        
        # 视频相关
        self.cap = None
        self.video_path = None
        self.video_thread = None
        
        # 音频相关
        self.audio_file = None
        self.audio_start_time = None
        self.audio_enabled = True
        
        # 同步控制
        self.play_start_time = None
        self.seek_lock = threading.Lock()
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path)
    
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示
        self.video_frame = ttk.Frame(main_frame)
        self.video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = tk.Label(self.video_frame, bg='black', 
                                   text="纯Python视频播放器\n点击'打开'选择视频文件")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X)
        
        # 按钮
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(btn_frame, text="打开", command=self.open_file).pack(side=tk.LEFT, padx=(0, 5))
        
        self.play_btn = ttk.Button(btn_frame, text="播放", command=self.toggle_play)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(btn_frame, text="停止", command=self.stop).pack(side=tk.LEFT, padx=(0, 5))
        
        # 音频开关
        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="音频", variable=self.audio_var).pack(side=tk.LEFT, padx=(10, 0))
        
        # 进度条
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_seek)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        self.time_label = ttk.Label(progress_frame, text="00:00/00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.pack(fill=tk.X, pady=(5, 0))
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def open_file(self):
        """打开文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频"""
        try:
            print(f"📁 加载视频: {video_path}")
            
            # 停止当前播放
            self.stop()
            
            # 尝试使用MSMF后端
            print("🔧 尝试MSMF后端...")
            self.cap = cv2.VideoCapture(video_path, cv2.CAP_MSMF)
            
            if not self.cap.isOpened():
                print("🔧 MSMF失败，尝试DirectShow...")
                self.cap = cv2.VideoCapture(video_path, cv2.CAP_DSHOW)
            
            if not self.cap.isOpened():
                print("🔧 DirectShow失败，尝试默认后端...")
                self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            if self.total_frames <= 0:
                self.total_frames = 1000
            if self.fps <= 0:
                self.fps = 30
            
            print(f"✅ 视频加载成功: {self.total_frames}帧, {self.fps}fps")
            
            # 设置进度条
            self.progress_bar.configure(to=self.total_frames-1)
            self.current_frame = 0
            
            # 显示第一帧
            self.show_current_frame()
            
            # 提取音频
            self.extract_audio(video_path)
            
            self.video_path = video_path
            self.status_label.configure(text=f"已加载: {os.path.basename(video_path)}")
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            messagebox.showerror("错误", f"加载视频失败:\n{e}")
    
    def extract_audio(self, video_path):
        """提取音频"""
        if not self.audio_var.get():
            return
        
        try:
            print("🔊 提取音频...")
            
            # 使用简单的方法提取音频
            import tempfile
            import subprocess
            
            # 创建临时音频文件
            self.audio_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False).name
            
            # 使用系统的ffmpeg提取音频（如果可用）
            try:
                cmd = [
                    'ffmpeg', '-i', video_path, 
                    '-vn', '-acodec', 'pcm_s16le', 
                    '-ar', '22050', '-ac', '1',
                    '-y', self.audio_file
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0 and os.path.exists(self.audio_file):
                    print("✅ 音频提取成功")
                else:
                    print("⚠️ ffmpeg提取失败，跳过音频")
                    self.audio_file = None
                    
            except (subprocess.TimeoutExpired, FileNotFoundError):
                print("⚠️ 系统没有ffmpeg，跳过音频提取")
                self.audio_file = None
                
        except Exception as e:
            print(f"❌ 音频提取失败: {e}")
            self.audio_file = None
    
    def show_current_frame(self):
        """显示当前帧"""
        if not self.cap:
            return
        
        try:
            # 设置帧位置
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)
            
            ret, frame = self.cap.read()
            if not ret:
                return
            
            # 转换颜色
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 调整大小
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()
            
            if label_width > 1 and label_height > 1:
                h, w = frame_rgb.shape[:2]
                scale = min(label_width/w, label_height/h)
                new_w, new_h = int(w*scale), int(h*scale)
                
                frame_resized = cv2.resize(frame_rgb, (new_w, new_h))
                
                # 显示
                pil_image = Image.fromarray(frame_resized)
                photo = ImageTk.PhotoImage(pil_image)
                
                self.video_label.configure(image=photo, text="")
                self.video_label.image = photo
            
            # 更新进度和时间
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            total_time = self.total_frames / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)}/{self.format_time(total_time)}")
            
        except Exception as e:
            print(f"❌ 显示帧失败: {e}")
    
    def format_time(self, seconds):
        """格式化时间"""
        m, s = divmod(int(seconds), 60)
        return f"{m:02d}:{s:02d}"
    
    def toggle_play(self):
        """播放/暂停切换"""
        if not self.cap:
            messagebox.showwarning("警告", "请先加载视频")
            return
        
        if self.is_playing:
            self.pause()
        else:
            self.play()
    
    def play(self):
        """开始播放"""
        if not self.cap:
            return
        
        self.is_playing = True
        self.play_btn.configure(text="暂停")
        
        # 记录播放开始时间
        self.play_start_time = time.time() - (self.current_frame / self.fps)
        
        # 播放音频
        self.play_audio()
        
        # 启动视频线程
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.video_loop, daemon=True)
            self.video_thread.start()
        
        print("▶️ 开始播放")
    
    def pause(self):
        """暂停"""
        self.is_playing = False
        self.play_btn.configure(text="播放")
        self.pause_audio()
        print("⏸️ 暂停")
    
    def stop(self):
        """停止"""
        self.is_playing = False
        self.play_btn.configure(text="播放")
        self.stop_audio()
        
        if self.cap:
            self.current_frame = 0
            self.show_current_frame()
        
        print("⏹️ 停止")
    
    def video_loop(self):
        """视频播放循环"""
        while self.is_playing and self.cap:
            try:
                # 计算应该显示的帧
                if self.play_start_time:
                    elapsed = time.time() - self.play_start_time
                    target_frame = int(elapsed * self.fps)
                    
                    if target_frame < self.total_frames:
                        self.current_frame = target_frame
                        self.root.after(0, self.show_current_frame)
                    else:
                        # 播放结束
                        self.root.after(0, self.stop)
                        break
                
                time.sleep(1/self.fps)
                
            except Exception as e:
                print(f"❌ 播放循环错误: {e}")
                break

    def play_audio(self):
        """播放音频"""
        if not self.audio_var.get() or not self.audio_file:
            return

        try:
            # 计算音频开始位置
            audio_pos = self.current_frame / self.fps

            if PYGAME_AVAILABLE:
                pygame.mixer.music.load(self.audio_file)
                pygame.mixer.music.play(start=audio_pos)
                print(f"🔊 pygame音频播放开始，位置: {audio_pos:.2f}s")

            elif WINSOUND_AVAILABLE:
                import winsound
                winsound.PlaySound(self.audio_file, winsound.SND_FILENAME | winsound.SND_ASYNC)
                print("🔊 winsound音频播放开始")

        except Exception as e:
            print(f"❌ 音频播放失败: {e}")

    def pause_audio(self):
        """暂停音频"""
        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.music.pause()
            elif WINSOUND_AVAILABLE:
                import winsound
                winsound.PlaySound(None, winsound.SND_PURGE)
        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.music.stop()
            elif WINSOUND_AVAILABLE:
                import winsound
                winsound.PlaySound(None, winsound.SND_PURGE)
        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def on_seek(self, value):
        """进度条拖动"""
        if not self.cap:
            return

        with self.seek_lock:
            try:
                frame_num = int(float(value))
                self.current_frame = frame_num

                # 更新播放时间基准
                if self.is_playing:
                    self.play_start_time = time.time() - (frame_num / self.fps)
                    # 重新开始音频
                    self.stop_audio()
                    self.play_audio()

                # 显示当前帧
                if not self.is_playing:
                    self.show_current_frame()

                print(f"🎬 跳转到帧: {frame_num}")

            except Exception as e:
                print(f"❌ 跳转失败: {e}")

    def on_close(self):
        """关闭程序"""
        print("🧹 清理资源...")

        self.stop()

        if self.cap:
            self.cap.release()

        if self.audio_file and os.path.exists(self.audio_file):
            try:
                os.unlink(self.audio_file)
            except:
                pass

        if PYGAME_AVAILABLE:
            pygame.mixer.quit()

        self.root.destroy()


def main():
    """主函数"""
    print("=" * 50)
    print("🎬 纯Python视频播放器")
    print("=" * 50)
    print("特点: 不使用ffmpeg，使用MSMF/DirectShow")
    print("音频: pygame或winsound")
    print("=" * 50)

    root = tk.Tk()

    # 检查命令行参数
    video_path = None
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"⚠️ 文件不存在: {video_path}")
            video_path = None

    app = PurePythonPlayer(root, video_path)
    root.mainloop()


if __name__ == "__main__":
    main()
