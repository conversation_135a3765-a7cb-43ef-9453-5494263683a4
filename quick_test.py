#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试播放器功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("正在导入播放器模块...")
    from simple_mp4_player import SimpleMP4Player
    print("✓ 播放器模块导入成功")
    
    import tkinter as tk
    print("✓ tkinter 可用")
    
    print("\n正在启动播放器...")
    root = tk.Tk()
    app = SimpleMP4Player(root)
    
    print("✓ 播放器创建成功")
    print("✓ 界面已显示，包含字幕生成进度功能")
    
    # 设置关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # 启动主循环
    root.mainloop()
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")
