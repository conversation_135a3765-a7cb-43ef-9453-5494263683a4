#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时字幕播放器 - 专门解决字幕生成问题
确保faster-whisper和moviepy正常工作
"""

import os
import sys

# 🛡️ 设置最安全的环境变量
print("🔧 设置安全环境变量...")
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1'
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'
print("✅ 安全环境变量设置完成")

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import time
import queue
import tempfile
import gc

# 检查依赖
print("🔍 检查字幕生成依赖...")

try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    print("✅ faster-whisper 可用")
except ImportError as e:
    WHISPER_AVAILABLE = False
    print(f"❌ faster-whisper 不可用: {e}")

try:
    import moviepy
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")
    print("尝试安装: pip install moviepy")
except Exception as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 导入异常: {e}")

class RealtimeSubtitlePlayer:
    def __init__(self):
        print("🚀 启动实时字幕播放器...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("实时字幕播放器 - 专业版")
        self.root.geometry("1200x900")
        
        # 视频相关变量
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.frame_delay = 1/30
        
        # 字幕相关变量
        self.whisper_model = None
        self.subtitle_text = ""
        self.subtitle_segments = []
        self.current_subtitle_index = 0
        self.subtitle_generation_complete = False
        
        # 字幕样式
        self.subtitle_font_size = 24
        self.subtitle_position_y = 0.85
        self.subtitle_color = (255, 255, 255)
        self.subtitle_bg_color = (0, 0, 0, 128)
        
        # 音频相关
        self.audio_path = None
        
        # 创建界面
        self.create_ui()
        
        # 加载Whisper模型
        self.load_whisper_model()
        
        print("✅ 实时字幕播放器初始化完成")
    
    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightblue")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题
        title_label = tk.Label(main_frame, text="实时字幕播放器", 
                              font=("Arial", 18, "bold"), bg="lightblue")
        title_label.pack(pady=10)
        
        # 功能说明
        info_frame = tk.LabelFrame(main_frame, text="功能说明", font=("Arial", 12))
        info_frame.pack(fill=tk.X, pady=5)
        
        info_text = """🎬 实时字幕生成: 使用faster-whisper引擎自动生成中文字幕
🔧 音频提取: 使用moviepy从视频中提取音频
🎯 智能识别: 支持中文语音识别和字幕同步
⚡ 实时显示: 字幕与视频播放同步显示"""
        
        tk.Label(info_frame, text=info_text, font=("Arial", 10), 
                justify=tk.LEFT, bg="lightyellow").pack(padx=10, pady=10, fill=tk.X)
        
        # 控制面板
        control_frame = tk.Frame(main_frame, bg="lightblue")
        control_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(control_frame, text="📁 选择视频", font=("Arial", 12),
                 command=self.select_video, bg="lightgreen").pack(side=tk.LEFT, padx=5)
        
        self.play_btn = tk.Button(control_frame, text="▶️ 播放", font=("Arial", 12),
                                 command=self.toggle_play, state="disabled", bg="orange")
        self.play_btn.pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="🎤 生成字幕", font=("Arial", 12),
                 command=self.generate_subtitles, bg="yellow").pack(side=tk.LEFT, padx=5)
        
        self.video_info_var = tk.StringVar(value="未选择视频文件")
        tk.Label(control_frame, textvariable=self.video_info_var, 
                font=("Arial", 10)).pack(side=tk.LEFT, padx=10)
        
        # 字幕控制
        subtitle_frame = tk.LabelFrame(main_frame, text="字幕设置", font=("Arial", 12))
        subtitle_frame.pack(fill=tk.X, pady=5)
        
        # 字幕大小
        tk.Label(subtitle_frame, text="字幕大小:").grid(row=0, column=0, padx=5, pady=5)
        self.font_size_var = tk.IntVar(value=self.subtitle_font_size)
        font_size_scale = ttk.Scale(subtitle_frame, from_=12, to=48, 
                                   orient=tk.HORIZONTAL, variable=self.font_size_var,
                                   command=self.update_font_size)
        font_size_scale.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 字幕位置
        tk.Label(subtitle_frame, text="字幕位置:").grid(row=0, column=2, padx=5, pady=5)
        self.position_var = tk.DoubleVar(value=self.subtitle_position_y)
        position_scale = ttk.Scale(subtitle_frame, from_=0.1, to=0.95, 
                                  orient=tk.HORIZONTAL, variable=self.position_var,
                                  command=self.update_position)
        position_scale.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        
        # 字幕开关
        self.subtitle_enabled = tk.BooleanVar(value=True)
        tk.Checkbutton(subtitle_frame, text="启用字幕",
                      variable=self.subtitle_enabled).grid(row=0, column=4, padx=5, pady=5)
        
        subtitle_frame.columnconfigure(1, weight=1)
        subtitle_frame.columnconfigure(3, weight=1)
        
        # 进度显示
        progress_frame = tk.LabelFrame(main_frame, text="字幕生成进度", font=("Arial", 12))
        progress_frame.pack(fill=tk.X, pady=5)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=10, pady=5)
        
        self.progress_text_var = tk.StringVar(value="就绪")
        tk.Label(progress_frame, textvariable=self.progress_text_var, 
                font=("Arial", 10)).pack(pady=5)
        
        # 视频显示区域
        video_frame = tk.LabelFrame(main_frame, text="视频显示", font=("Arial", 12))
        video_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.video_label = tk.Label(video_frame, text="选择视频文件开始播放\n\n实时字幕功能：\n• 自动提取音频\n• AI语音识别\n• 实时字幕显示", 
                                   font=("Arial", 14), bg="black", fg="white")
        self.video_label.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 状态栏
        status_frame = tk.Frame(main_frame, bg="lightblue")
        status_frame.pack(fill=tk.X, pady=5)
        
        self.status_var = tk.StringVar(value="就绪")
        tk.Label(status_frame, textvariable=self.status_var, 
                font=("Arial", 12), bg="lightgray").pack(fill=tk.X)
        
        # 日志显示
        log_frame = tk.LabelFrame(main_frame, text="详细日志", font=("Arial", 12))
        log_frame.pack(fill=tk.X, pady=5)
        
        self.log_text = tk.Text(log_frame, height=6, font=("Consolas", 9), wrap=tk.WORD)
        log_scrollbar = tk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 控制按钮
        button_frame = tk.Frame(main_frame, bg="lightblue")
        button_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(button_frame, text="🧹 清理内存", command=self.cleanup_memory).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="💾 保存字幕", command=self.save_subtitles).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="❌ 关闭", command=self.on_closing, 
                 bg="red", fg="white").pack(side=tk.RIGHT, padx=5)
        
        # 添加初始日志
        self.add_log("✅ 实时字幕播放器启动成功")
        self.add_log(f"🔍 faster-whisper: {'可用' if WHISPER_AVAILABLE else '不可用'}")
        self.add_log(f"🔍 moviepy: {'可用' if MOVIEPY_AVAILABLE else '不可用'}")
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "20.0")
        
        print(log_entry.strip())
    
    def load_whisper_model(self):
        """加载Whisper模型"""
        if not WHISPER_AVAILABLE:
            self.add_log("❌ faster-whisper不可用，无法生成字幕")
            self.status_var.set("Whisper不可用")
            return
        
        def load_model():
            try:
                self.add_log("🔄 开始加载Whisper模型...")
                self.root.after(0, lambda: self.status_var.set("正在加载Whisper模型..."))
                
                # 尝试加载不同大小的模型
                model_options = [
                    ("base", "base模型 (快速)"),
                    ("small", "small模型 (平衡)"),
                    ("medium", "medium模型 (较好)"),
                ]
                
                for model_name, model_desc in model_options:
                    try:
                        self.add_log(f"🔄 尝试加载{model_desc}...")
                        self.root.after(0, lambda desc=model_desc: self.status_var.set(f"加载{desc}..."))
                        
                        # 使用CPU版本确保兼容性
                        self.whisper_model = WhisperModel(model_name, device="cpu", compute_type="int8")
                        
                        success_msg = f"✅ {model_desc}加载成功"
                        self.add_log(success_msg)
                        self.root.after(0, lambda: self.status_var.set("Whisper模型就绪"))
                        return
                        
                    except Exception as e:
                        self.add_log(f"❌ {model_desc}加载失败: {e}")
                        continue
                
                raise Exception("所有模型加载都失败")
                
            except Exception as e:
                error_msg = f"❌ Whisper模型加载失败: {e}"
                self.add_log(error_msg)
                self.root.after(0, lambda: self.status_var.set("模型加载失败"))
        
        # 在后台线程中加载
        threading.Thread(target=load_model, daemon=True).start()
    
    def select_video(self):
        """选择视频"""
        try:
            self.add_log("📁 打开文件选择对话框")
            
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[("视频文件", "*.mp4 *.avi *.mov"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_info_var.set(f"已选择: {filename}")
                self.add_log(f"✅ 选择视频: {filename}")
                
                # 加载视频
                self.load_video()
            else:
                self.add_log("❌ 用户取消文件选择")
                
        except Exception as e:
            self.add_log(f"❌ 选择视频失败: {e}")
    
    def load_video(self):
        """加载视频"""
        if not self.video_path:
            return
        
        try:
            self.add_log("🎬 加载视频文件...")
            
            # 创建安全的capture
            self.cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
            if not self.cap or not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            if self.total_frames <= 0:
                self.total_frames = 1000
            if self.fps <= 0:
                self.fps = 30
            
            self.frame_delay = 1.0 / self.fps
            duration = self.total_frames / self.fps
            
            self.add_log(f"📊 视频信息: {self.total_frames}帧, {self.fps:.1f}fps, {duration:.1f}秒")
            
            # 显示第一帧
            self.show_frame()
            
            # 启用播放按钮
            self.play_btn.configure(state="normal")
            self.status_var.set("视频已加载，可以播放和生成字幕")
            
        except Exception as e:
            error_msg = f"❌ 加载视频失败: {e}"
            self.add_log(error_msg)
            messagebox.showerror("错误", error_msg)

    def show_frame(self):
        """显示当前帧"""
        if not self.cap:
            return

        try:
            # 读取当前帧
            ret, frame = self.cap.read()
            if not ret or frame is None:
                return

            # 添加字幕到帧上
            if self.subtitle_enabled.get() and self.subtitle_text:
                frame = self.add_subtitle_to_frame(frame, self.subtitle_text)

            # 转换为PIL图像
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)

            # 调整大小以适应显示
            display_width = 800
            display_height = int(display_width * pil_image.height / pil_image.width)
            pil_image = pil_image.resize((display_width, display_height), Image.Resampling.LANCZOS)

            # 转换为tkinter可用的格式
            photo = ImageTk.PhotoImage(pil_image)

            # 更新显示
            self.video_label.configure(image=photo, text="")
            self.video_label.image = photo  # 保持引用

        except Exception as e:
            self.add_log(f"❌ 显示帧失败: {e}")

    def add_subtitle_to_frame(self, frame, subtitle_text):
        """在帧上添加字幕"""
        try:
            # 转换为PIL图像
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)

            # 创建绘图对象
            draw = ImageDraw.Draw(pil_image)

            # 尝试加载中文字体
            try:
                # Windows系统字体
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", self.subtitle_font_size)
            except:
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttf", self.subtitle_font_size)
                except:
                    # 使用默认字体
                    font = ImageFont.load_default()

            # 计算字幕位置
            img_width, img_height = pil_image.size

            # 分行处理长字幕
            lines = self.wrap_text(subtitle_text, font, img_width - 40)

            # 计算总高度
            line_height = self.subtitle_font_size + 5
            total_height = len(lines) * line_height

            # 计算起始Y位置
            start_y = int(img_height * self.subtitle_position_y) - total_height // 2

            # 绘制每一行
            for i, line in enumerate(lines):
                # 计算文本大小
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                # 居中位置
                x = (img_width - text_width) // 2
                y = start_y + i * line_height

                # 绘制背景
                padding = 5
                draw.rectangle([x - padding, y - padding,
                               x + text_width + padding, y + text_height + padding],
                              fill=(0, 0, 0, 180))

                # 绘制文字
                draw.text((x, y), line, font=font, fill=self.subtitle_color)

            # 转换回OpenCV格式
            frame_with_subtitle = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return frame_with_subtitle

        except Exception as e:
            self.add_log(f"❌ 添加字幕失败: {e}")
            return frame

    def wrap_text(self, text, font, max_width):
        """文本换行"""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            test_line = current_line + (" " if current_line else "") + word
            bbox = font.getbbox(test_line)
            if bbox[2] - bbox[0] <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        return lines if lines else [text]

    def generate_subtitles(self):
        """生成字幕"""
        if not self.video_path:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if not WHISPER_AVAILABLE or not self.whisper_model:
            messagebox.showerror("错误", "Whisper模型不可用，无法生成字幕")
            return

        if not MOVIEPY_AVAILABLE:
            messagebox.showerror("错误", "moviepy不可用，无法提取音频")
            return

        def generate():
            try:
                self.add_log("🎤 开始生成字幕...")
                self.root.after(0, lambda: self.progress_text_var.set("正在提取音频..."))
                self.root.after(0, lambda: self.progress_var.set(10))

                # 提取音频
                self.add_log("🔊 从视频中提取音频...")
                video_clip = VideoFileClip(self.video_path)

                # 创建临时音频文件
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                    self.audio_path = temp_audio.name

                # 提取音频到临时文件
                audio_clip = video_clip.audio
                audio_clip.write_audiofile(self.audio_path, verbose=False, logger=None)

                # 关闭视频文件
                video_clip.close()
                audio_clip.close()

                self.add_log("✅ 音频提取完成")
                self.root.after(0, lambda: self.progress_text_var.set("正在进行语音识别..."))
                self.root.after(0, lambda: self.progress_var.set(30))

                # 使用Whisper进行语音识别
                self.add_log("🤖 开始AI语音识别...")
                segments, info = self.whisper_model.transcribe(
                    self.audio_path,
                    language="zh",  # 中文
                    beam_size=5,
                    word_timestamps=True
                )

                self.add_log(f"🔍 检测到语言: {info.language} (置信度: {info.language_probability:.2f})")

                # 处理识别结果
                self.subtitle_segments = []
                total_segments = 0

                self.root.after(0, lambda: self.progress_text_var.set("正在处理识别结果..."))
                self.root.after(0, lambda: self.progress_var.set(60))

                for segment in segments:
                    total_segments += 1
                    subtitle_info = {
                        'start': segment.start,
                        'end': segment.end,
                        'text': segment.text.strip()
                    }
                    self.subtitle_segments.append(subtitle_info)

                    self.add_log(f"📝 [{segment.start:.1f}s-{segment.end:.1f}s] {segment.text.strip()}")

                # 清理临时文件
                try:
                    os.unlink(self.audio_path)
                except:
                    pass

                self.subtitle_generation_complete = True
                self.current_subtitle_index = 0

                success_msg = f"✅ 字幕生成完成！共生成{total_segments}条字幕"
                self.add_log(success_msg)
                self.root.after(0, lambda: self.progress_text_var.set(f"完成 - {total_segments}条字幕"))
                self.root.after(0, lambda: self.progress_var.set(100))
                self.root.after(0, lambda: self.status_var.set("字幕生成完成，可以播放"))

            except Exception as e:
                error_msg = f"❌ 字幕生成失败: {e}"
                self.add_log(error_msg)
                self.root.after(0, lambda: self.progress_text_var.set("生成失败"))
                self.root.after(0, lambda: self.progress_var.set(0))
                self.root.after(0, lambda err=error_msg: messagebox.showerror("错误", err))

        # 在后台线程中生成字幕
        threading.Thread(target=generate, daemon=True).start()

    def toggle_play(self):
        """切换播放状态"""
        if not self.cap:
            return

        if not self.is_playing:
            self.start_play()
        else:
            self.stop_play()

    def start_play(self):
        """开始播放"""
        try:
            self.add_log("▶️ 开始播放")
            self.is_playing = True
            self.play_btn.configure(text="⏸️ 暂停")

            # 启动播放线程
            threading.Thread(target=self.play_loop, daemon=True).start()

        except Exception as e:
            self.add_log(f"❌ 开始播放失败: {e}")

    def play_loop(self):
        """播放循环 - 永不停止版本"""
        consecutive_errors = 0
        max_consecutive_errors = 50
        recovery_attempts = 0
        max_recovery_attempts = 100
        force_continue = True

        self.add_log("🎬 启动永不停止播放循环")

        try:
            while self.is_playing and self.cap and force_continue:
                try:
                    start_time = time.time()

                    # 获取当前播放时间
                    current_time = self.current_frame / self.fps

                    # 更新字幕
                    if self.subtitle_generation_complete:
                        self.update_current_subtitle(current_time)

                    # 读取并显示帧
                    ret, frame = self.cap.read()
                    if not ret:
                        # 检查是否真的到了视频末尾
                        current_pos = 0
                        try:
                            current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                        except:
                            pass

                        if current_pos >= self.total_frames * 0.95:  # 允许5%的误差
                            # 正常到达视频末尾
                            self.add_log(f"✅ 视频正常播放完毕: {current_pos}/{self.total_frames}")
                            self.is_playing = False
                            self.root.after(0, lambda: self.play_btn.configure(text="▶️ 播放"))
                            break
                        else:
                            # 视频未播放完就无法读取，启动恢复
                            recovery_attempts += 1
                            self.add_log(f"💪 视频未播放完但读取失败，启动恢复 (第{recovery_attempts}次)")

                            if recovery_attempts < max_recovery_attempts:
                                # 尝试多种恢复方法
                                if recovery_attempts % 10 == 0:
                                    # 每10次尝试重新创建capture
                                    self.add_log("🔄 重新创建capture进行恢复")
                                    if self.recreate_capture_for_recovery():
                                        recovery_attempts = 0
                                        consecutive_errors = 0
                                        continue
                                else:
                                    # 尝试跳过当前帧
                                    try:
                                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos + 1)
                                        time.sleep(0.1)
                                        continue
                                    except:
                                        pass

                                time.sleep(0.2)
                                continue
                            else:
                                self.add_log(f"💪 恢复尝试{recovery_attempts}次，但永不放弃")
                                recovery_attempts = 0
                                time.sleep(1)
                                continue

                    # 验证帧数据
                    if frame is None or frame.size == 0:
                        self.add_log("💪 读取到无效帧，但永不停止")
                        consecutive_errors += 1
                        recovery_attempts += 1

                        if consecutive_errors >= max_consecutive_errors:
                            self.add_log(f"💪 连续错误{consecutive_errors}次，但强制继续")
                            consecutive_errors = 0
                            time.sleep(0.5)
                        continue

                    # 重置错误计数
                    consecutive_errors = 0
                    recovery_attempts = 0

                    self.current_frame += 1

                    # 在主线程中更新显示
                    self.root.after(0, self.show_frame)

                    # 控制播放速度
                    elapsed = time.time() - start_time
                    sleep_time = max(0, self.frame_delay - elapsed)
                    time.sleep(sleep_time)

                except Exception as e:
                    error_msg = str(e)
                    self.add_log(f"💪 播放循环异常但永不停止: {error_msg}")
                    consecutive_errors += 1
                    recovery_attempts += 1

                    # 检查是否是严重错误
                    if any(keyword in error_msg.lower() for keyword in ['pthread', 'async_lock', 'msmf']):
                        self.add_log("🚨 检测到严重错误，启动恢复")
                        if self.recreate_capture_for_recovery():
                            consecutive_errors = 0
                            recovery_attempts = 0
                            continue

                    # 永不停止机制
                    if consecutive_errors >= max_consecutive_errors:
                        if recovery_attempts < max_recovery_attempts:
                            self.add_log(f"💪 连续错误{consecutive_errors}次，但永不停止")
                            consecutive_errors = 0
                            time.sleep(0.8)
                            continue
                        else:
                            self.add_log(f"💪 错误和恢复都过多，但仍然尝试继续")
                            consecutive_errors = 0
                            recovery_attempts = 0
                            time.sleep(2)
                            continue

                    time.sleep(0.2)

        except Exception as e:
            self.add_log(f"❌ 播放循环严重异常: {e}")
        finally:
            self.is_playing = False
            self.root.after(0, lambda: self.play_btn.configure(text="▶️ 播放"))

    def recreate_capture_for_recovery(self):
        """重新创建capture进行恢复"""
        try:
            self.add_log("🔄 重新创建capture...")

            # 保存当前位置
            current_pos = 0
            try:
                current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
            except:
                pass

            # 释放当前capture
            if self.cap:
                try:
                    self.cap.release()
                except:
                    pass

            # 等待资源释放
            time.sleep(0.3)

            # 重新创建capture
            self.cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
            if self.cap and self.cap.isOpened():
                # 恢复到之前的位置
                try:
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos)
                except:
                    pass

                self.add_log(f"✅ capture重新创建成功，恢复到位置{current_pos}")
                return True
            else:
                self.add_log("❌ capture重新创建失败")
                return False

        except Exception as e:
            self.add_log(f"❌ 重新创建capture异常: {e}")
            return False

    def update_current_subtitle(self, current_time):
        """更新当前字幕"""
        try:
            # 查找当前时间对应的字幕
            current_subtitle = ""

            for segment in self.subtitle_segments:
                if segment['start'] <= current_time <= segment['end']:
                    current_subtitle = segment['text']
                    break

            # 更新字幕文本
            if current_subtitle != self.subtitle_text:
                self.subtitle_text = current_subtitle

        except Exception as e:
            self.add_log(f"❌ 更新字幕失败: {e}")

    def stop_play(self):
        """停止播放"""
        self.add_log("⏸️ 暂停播放")
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

    def update_font_size(self, value):
        """更新字幕字体大小"""
        self.subtitle_font_size = int(float(value))

    def update_position(self, value):
        """更新字幕位置"""
        self.subtitle_position_y = float(value)

    def save_subtitles(self):
        """保存字幕文件"""
        if not self.subtitle_segments:
            messagebox.showwarning("警告", "没有字幕可保存")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="保存字幕文件",
                defaultextension=".srt",
                filetypes=[("SRT字幕", "*.srt"), ("所有文件", "*.*")]
            )

            if file_path:
                with open(file_path, "w", encoding="utf-8") as f:
                    for i, segment in enumerate(self.subtitle_segments, 1):
                        start_time = self.format_time(segment['start'])
                        end_time = self.format_time(segment['end'])

                        f.write(f"{i}\n")
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{segment['text']}\n\n")

                self.add_log(f"💾 字幕已保存: {file_path}")
                messagebox.showinfo("成功", f"字幕已保存到:\n{file_path}")

        except Exception as e:
            error_msg = f"保存字幕失败: {e}"
            self.add_log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def format_time(self, seconds):
        """格式化时间为SRT格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"

    def cleanup_memory(self):
        """清理内存"""
        try:
            before = len(gc.get_objects())
            collected = gc.collect()
            after = len(gc.get_objects())
            self.add_log(f"🧹 内存清理: 回收{collected}个对象, 对象数量 {before} -> {after}")
        except Exception as e:
            self.add_log(f"❌ 内存清理失败: {e}")

    def on_closing(self):
        """关闭处理"""
        try:
            self.is_playing = False

            if self.cap:
                self.cap.release()

            # 清理临时文件
            if hasattr(self, 'audio_path') and self.audio_path and os.path.exists(self.audio_path):
                try:
                    os.unlink(self.audio_path)
                except:
                    pass

            self.root.destroy()

        except Exception as e:
            print(f"关闭时出错: {e}")
            self.root.destroy()

    def run(self):
        """运行播放器"""
        try:
            print("🎬 启动实时字幕播放器界面...")

            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    filename = os.path.basename(video_file)
                    self.video_info_var.set(f"命令行: {filename}")
                    self.add_log(f"📁 命令行参数: {filename}")
                    self.root.after(1000, self.load_video)
                else:
                    self.add_log(f"❌ 命令行文件不存在: {video_file}")

            # 启动主循环
            self.root.mainloop()

            print("🔚 实时字幕播放器正常结束")

        except Exception as e:
            print(f"❌ 播放器异常: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 实时字幕播放器")
    print("=" * 60)
    print("功能: 使用AI自动生成实时中文字幕")
    print("引擎: faster-whisper + moviepy")
    print("特点: 实时显示 + 字幕同步 + 可保存SRT")
    print("=" * 60)

    try:
        player = RealtimeSubtitlePlayer()
        player.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
