#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import sys

# 🛡️ 超级安全环境设置 - 修复pthread错误
print("🔧 设置超级安全环境变量...")

# 完全禁用多线程
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1'
os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
os.environ['OPENCV_VIDEOIO_FFMPEG_THREAD_COUNT'] = '1'
os.environ['FFMPEG_THREAD_SAFE'] = '0'

# 禁用异步处理
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_VIDEOIO_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'

# 禁用硬件加速
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'

# 强制使用单线程解码
os.environ['OPENCV_FFMPEG_THREAD_TYPE'] = '1'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'

# 禁用MSMF
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'

print("✅ 超级安全环境变量设置完成")

# 启动播放器
if __name__ == "__main__":
    import subprocess
    
    # 传递所有命令行参数
    args = [sys.executable, "simple_mp4_player.py"] + sys.argv[1:]
    
    print(f"🚀 启动安全播放器: {' '.join(args)}")
    subprocess.run(args)
