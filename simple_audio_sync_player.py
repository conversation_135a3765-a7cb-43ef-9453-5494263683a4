#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单音频同步播放器 - 解决音频同步问题
使用简化但有效的音频同步策略
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
from PIL import Image, ImageTk

# 🎵 简化的音频同步配置
print("🔧 设置简化音频同步环境...")

# 音频同步优化配置 - 平衡性能和稳定性
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '100'

# 使用2线程，平衡性能和稳定性
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;2|thread_type;1|thread_count;2|buffer_size;32768'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '2'

# 启用硬件加速
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '0'

# 音频同步关键配置
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '0'  # 启用异步
os.environ['OPENCV_FFMPEG_SEEK_BUFFER_SIZE'] = '4096'

# 线程安全
os.environ['OPENCV_FFMPEG_THREAD_SAFE'] = '1'

print("✅ 简化音频同步环境设置完成")

# 检查依赖
try:
    import pygame
    pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=1024)
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用")
except Exception as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")

class SimpleAudioSyncPlayer:
    def __init__(self, root, video_path=None):
        self.root = root
        self.root.title("简单音频同步播放器")
        self.root.geometry("1000x700")
        
        # 播放状态
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.duration = 0
        
        # 视频相关
        self.cap = None
        self.video_path = None
        self.video_thread = None
        
        # 简化的音频同步
        self.audio_file = None
        self.audio_start_time = None
        self.video_start_time = None
        self.sync_offset = 0  # 手动同步偏移（毫秒）
        
        # 同步控制
        self.sync_lock = threading.Lock()
        
        # 性能优化
        self.frame_cache = {}
        self.cache_size = 50
        
        # seek控制
        self.last_seek_time = 0
        self.seek_debounce = 0.2
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示区域
        video_container = ttk.Frame(main_frame)
        video_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = tk.Label(video_container, bg='black', fg='white',
                                   text="简单音频同步播放器\n\n特点:\n• 简化的音频同步策略\n• 手动同步偏移调整\n• 稳定的播放性能\n• 易于使用的界面\n\n点击'打开视频'开始")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="播放控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮行
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="📁 打开视频", command=self.open_video).pack(side=tk.LEFT, padx=(0, 5))
        
        self.play_btn = ttk.Button(btn_frame, text="▶️ 播放", command=self.toggle_play)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(btn_frame, text="⏹️ 停止", command=self.stop).pack(side=tk.LEFT, padx=(0, 5))
        
        # 音频控制
        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="🔊 音频", variable=self.audio_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 进度控制
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_seek)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))
        
        self.time_label = ttk.Label(progress_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 同步调整面板
        sync_frame = ttk.LabelFrame(main_frame, text="音频同步调整")
        sync_frame.pack(fill=tk.X, pady=(0, 10))
        
        sync_control_frame = ttk.Frame(sync_frame)
        sync_control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(sync_control_frame, text="音频偏移(ms):").pack(side=tk.LEFT)
        
        self.offset_var = tk.DoubleVar(value=0)
        self.offset_scale = ttk.Scale(sync_control_frame, from_=-2000, to=2000,
                                     orient=tk.HORIZONTAL, variable=self.offset_var,
                                     command=self.adjust_sync_offset)
        self.offset_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))
        
        self.offset_label = ttk.Label(sync_control_frame, text="0ms")
        self.offset_label.pack(side=tk.RIGHT)
        
        # 快速调整按钮
        quick_frame = ttk.Frame(sync_frame)
        quick_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Button(quick_frame, text="音频提前100ms", command=lambda: self.quick_adjust(-100)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_frame, text="音频延后100ms", command=lambda: self.quick_adjust(100)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_frame, text="重置同步", command=self.reset_sync).pack(side=tk.LEFT, padx=(0, 5))
        
        # 信息面板
        info_frame = ttk.LabelFrame(main_frame, text="播放信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_label = ttk.Label(info_frame, text="未加载视频")
        self.info_label.pack(padx=5, pady=5)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪 - 简单音频同步播放器", relief=tk.SUNKEN)
        self.status_label.pack(fill=tk.X)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def open_video(self):
        """打开视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("常见视频", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv"),
                ("MP4文件", "*.mp4"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            print(f"📁 加载视频: {video_path}")
            self.status_label.configure(text="正在加载视频...")
            
            # 停止当前播放
            self.stop()
            
            # 清理缓存
            self.frame_cache.clear()
            
            # 使用ffmpeg后端
            print("🔧 使用音频同步ffmpeg后端...")
            self.cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
            
            if not self.cap.isOpened():
                print("⚠️ ffmpeg后端失败，尝试默认后端...")
                self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 验证参数
            if self.total_frames <= 0:
                self.total_frames = 10000
            if self.fps <= 0 or self.fps > 120:
                self.fps = 30
            
            self.duration = self.total_frames / self.fps
            
            print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps, {width}x{height}")
            
            # 设置进度条
            self.progress_bar.configure(to=self.total_frames-1)
            self.current_frame = 0
            
            # 显示第一帧
            self.show_current_frame()
            
            # 提取音频
            self.extract_audio_simple(video_path)
            
            # 更新界面
            self.video_path = video_path
            filename = os.path.basename(video_path)
            self.info_label.configure(text=f"文件: {filename}\n分辨率: {width}x{height}\n帧率: {self.fps}fps\n时长: {self.format_time(self.duration)}")
            self.status_label.configure(text=f"已加载: {filename}")
            
        except Exception as e:
            error_msg = f"加载视频失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            self.status_label.configure(text="加载失败")
    
    def extract_audio_simple(self, video_path):
        """简单的音频提取"""
        if not self.audio_var.get():
            print("🔇 音频已禁用")
            return
        
        def extract_in_thread():
            try:
                print("🔊 提取音频...")
                self.root.after(0, lambda: self.status_label.configure(text="正在提取音频..."))
                
                import tempfile
                import subprocess
                
                # 创建临时音频文件
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    self.audio_file = temp_file.name
                
                # 使用ffmpeg提取音频
                cmd = [
                    'ffmpeg', '-i', video_path,
                    '-vn', '-acodec', 'pcm_s16le',
                    '-ar', '44100', '-ac', '2',
                    '-y', self.audio_file
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0 and os.path.exists(self.audio_file):
                    file_size = os.path.getsize(self.audio_file)
                    print(f"✅ 音频提取成功: {file_size} 字节")
                    self.root.after(0, lambda: self.status_label.configure(text="音频提取完成"))
                else:
                    print("⚠️ 音频提取失败")
                    self.audio_file = None
                    
            except subprocess.TimeoutExpired:
                print("⚠️ 音频提取超时")
                self.audio_file = None
            except FileNotFoundError:
                print("⚠️ 系统未安装ffmpeg，跳过音频")
                self.audio_file = None
            except Exception as e:
                print(f"❌ 音频提取异常: {e}")
                self.audio_file = None
        
        threading.Thread(target=extract_in_thread, daemon=True).start()

    def show_current_frame(self):
        """显示当前帧"""
        if not self.cap:
            return

        try:
            # 检查缓存
            if self.current_frame in self.frame_cache:
                frame = self.frame_cache[self.current_frame]
            else:
                # 设置帧位置并读取
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)
                ret, frame = self.cap.read()
                if not ret:
                    return

                # 缓存管理
                if len(self.frame_cache) >= self.cache_size:
                    oldest_frame = min(self.frame_cache.keys())
                    del self.frame_cache[oldest_frame]

                self.frame_cache[self.current_frame] = frame.copy()

            # 转换并显示
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 获取显示区域大小
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()

            if label_width > 1 and label_height > 1:
                h, w = frame_rgb.shape[:2]
                scale = min(label_width/w, label_height/h)
                new_w, new_h = int(w*scale), int(h*scale)

                if new_w > 0 and new_h > 0:
                    frame_resized = cv2.resize(frame_rgb, (new_w, new_h))
                    pil_image = Image.fromarray(frame_resized)
                    photo = ImageTk.PhotoImage(pil_image)

                    self.video_label.configure(image=photo, text="")
                    self.video_label.image = photo

            # 更新进度和时间
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(self.duration)}")

        except Exception as e:
            print(f"❌ 显示帧失败: {e}")

    def format_time(self, seconds):
        """格式化时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"

    def toggle_play(self):
        """播放/暂停切换"""
        if not self.cap:
            messagebox.showwarning("警告", "请先加载视频文件")
            return

        if self.is_playing:
            self.pause()
        else:
            self.play()

    def play(self):
        """开始播放 - 简单同步版本"""
        if not self.cap:
            return

        self.is_playing = True
        self.play_btn.configure(text="⏸️ 暂停")

        # 设置播放基准时间
        with self.sync_lock:
            current_time = time.time()
            self.video_start_time = current_time
            # 考虑当前播放位置和同步偏移
            video_offset = self.current_frame / self.fps
            audio_offset_seconds = self.sync_offset / 1000.0
            self.audio_start_time = current_time - video_offset + audio_offset_seconds

        # 播放音频
        self.play_audio_simple()

        # 启动视频播放线程
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.simple_playback_loop, daemon=True)
            self.video_thread.start()

        self.status_label.configure(text="正在播放...")
        print("▶️ 开始简单同步播放")

    def pause(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 暂停音频
        self.pause_audio()

        self.status_label.configure(text="已暂停")
        print("⏸️ 暂停播放")

    def stop(self):
        """停止播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 停止音频
        self.stop_audio()

        # 重置位置
        if self.cap:
            self.current_frame = 0
            self.show_current_frame()

        # 重置时间基准
        with self.sync_lock:
            self.video_start_time = None
            self.audio_start_time = None

        self.status_label.configure(text="已停止")
        print("⏹️ 停止播放")

    def simple_playback_loop(self):
        """简单同步播放循环"""
        print("🎬 启动简单同步播放循环")

        while self.is_playing and self.cap:
            try:
                current_time = time.time()

                # 计算基于视频开始时间的目标帧
                with self.sync_lock:
                    if self.video_start_time:
                        elapsed_time = current_time - self.video_start_time
                        # 从当前帧开始计算，而不是从0开始
                        start_frame = self.current_frame if hasattr(self, '_play_start_frame') else self.current_frame
                        if not hasattr(self, '_play_start_frame'):
                            self._play_start_frame = self.current_frame
                            self._play_start_time = elapsed_time

                        target_frame = self._play_start_frame + int((elapsed_time - self._play_start_time) * self.fps)

                        # 简单的帧控制
                        frame_diff = target_frame - self.current_frame

                        if frame_diff > 5:  # 落后超过5帧
                            print(f"🔄 跳帧同步: {self.current_frame} -> {target_frame}")
                            self.current_frame = target_frame
                            self.frame_cache.clear()
                        elif frame_diff > 0:
                            # 正常前进
                            self.current_frame = target_frame

                        # 检查播放结束
                        if self.current_frame >= self.total_frames:
                            self.root.after(0, self.stop)
                            break

                # 更新显示
                self.root.after(0, self.show_current_frame)

                # 控制播放速度
                frame_time = 1.0 / self.fps
                time.sleep(frame_time * 0.9)

            except Exception as e:
                print(f"❌ 简单播放循环错误: {e}")
                break

        print("🎬 简单播放循环结束")

    def play_audio_simple(self):
        """简单播放音频"""
        if not self.audio_var.get() or not self.audio_file or not PYGAME_AVAILABLE:
            return

        # 检查音频文件是否存在
        if not os.path.exists(self.audio_file):
            print("⚠️ 音频文件不存在")
            return

        try:
            print("🔊 开始简单音频播放")

            # 加载并播放音频
            pygame.mixer.music.load(self.audio_file)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()

            print("✅ 简单音频播放开始")

        except Exception as e:
            print(f"❌ 简单音频播放失败: {e}")

    def pause_audio(self):
        """暂停音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.pause()
            print("🔊 音频已暂停")
        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.stop()
            print("🔊 音频已停止")
        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def on_seek(self, value):
        """进度条拖动处理"""
        if not self.cap:
            return

        current_time = time.time()

        # 防抖处理
        if current_time - self.last_seek_time < self.seek_debounce:
            return

        self.last_seek_time = current_time

        try:
            frame_num = int(float(value))
            seek_time = frame_num / self.fps

            print(f"🎬 简单跳转: 帧{frame_num} ({seek_time:.2f}秒)")

            # 清理缓存
            self.frame_cache.clear()

            self.current_frame = frame_num

            if self.is_playing:
                # 重新设置播放基准
                with self.sync_lock:
                    current_time = time.time()
                    self.video_start_time = current_time
                    audio_offset_seconds = self.sync_offset / 1000.0
                    self.audio_start_time = current_time - seek_time + audio_offset_seconds

                    # 重置播放起始点
                    self._play_start_frame = frame_num
                    self._play_start_time = 0

                # 重新开始音频
                self.stop_audio()
                self.play_audio_simple()

            # 显示当前帧
            if not self.is_playing:
                self.show_current_frame()

        except Exception as e:
            print(f"❌ 简单跳转失败: {e}")

    def adjust_sync_offset(self, value):
        """调整同步偏移"""
        self.sync_offset = float(value)
        self.offset_label.configure(text=f"{self.sync_offset:.0f}ms")
        print(f"🔧 同步偏移调整为: {self.sync_offset:.0f}ms")

        if self.is_playing:
            # 实时调整音频同步
            with self.sync_lock:
                if self.video_start_time:
                    current_time = time.time()
                    video_elapsed = current_time - self.video_start_time
                    audio_offset_seconds = self.sync_offset / 1000.0
                    self.audio_start_time = current_time - video_elapsed + audio_offset_seconds

    def quick_adjust(self, offset_ms):
        """快速调整同步偏移"""
        current_offset = self.offset_var.get()
        new_offset = current_offset + offset_ms
        new_offset = max(-2000, min(2000, new_offset))  # 限制范围
        self.offset_var.set(new_offset)
        self.adjust_sync_offset(new_offset)

    def reset_sync(self):
        """重置同步"""
        self.offset_var.set(0)
        self.adjust_sync_offset(0)
        print("🔄 同步已重置")

    def on_close(self):
        """程序关闭处理"""
        print("🧹 清理资源...")

        # 停止播放
        self.stop()

        # 释放视频资源
        if self.cap:
            self.cap.release()

        # 清理音频文件
        if self.audio_file and os.path.exists(self.audio_file):
            try:
                os.unlink(self.audio_file)
                print("🧹 临时音频文件已清理")
            except Exception as e:
                print(f"⚠️ 清理音频文件失败: {e}")

        # 退出pygame
        if PYGAME_AVAILABLE:
            pygame.mixer.quit()

        print("✅ 资源清理完成")
        self.root.destroy()


def main():
    """主函数"""
    print("=" * 60)
    print("🎬 简单音频同步播放器")
    print("=" * 60)
    print("特点:")
    print("• 简化的音频同步策略 - 稳定可靠")
    print("• 手动同步偏移调整 - 精确控制")
    print("• 快速同步调整按钮 - 方便操作")
    print("• 优化的播放性能 - 流畅播放")
    print("• 易于使用的界面 - 简单直观")
    print("=" * 60)

    root = tk.Tk()

    # 检查命令行参数
    video_path = None
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"⚠️ 文件不存在: {video_path}")
            video_path = None

    app = SimpleAudioSyncPlayer(root, video_path)
    root.mainloop()


if __name__ == "__main__":
    main()
