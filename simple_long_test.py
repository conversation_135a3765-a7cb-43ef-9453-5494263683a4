#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化长时间运行测试 - 不依赖额外包
专门找出播放一段时间后自动关闭的原因
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import os
import time
import threading
import traceback
import gc

class SimpleLongTest:
    def __init__(self):
        print("🚀 启动简化长时间运行测试...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("长时间运行测试 - 找出自动关闭原因")
        self.root.geometry("800x600")
        
        # 设置变量
        self.running = True
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.play_thread = None
        
        # 监控数据
        self.start_time = time.time()
        self.frame_count = 0
        self.error_count = 0
        self.last_heartbeat = time.time()
        
        # 创建界面
        self.create_ui()
        
        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 启动心跳监控
        self.start_heartbeat()
        
        print("✅ 简化长时间运行测试初始化完成")
    
    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightblue")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题
        title_label = tk.Label(main_frame, text="长时间运行测试", 
                              font=("Arial", 18, "bold"), bg="lightblue")
        title_label.pack(pady=10)
        
        # 状态显示
        status_frame = tk.LabelFrame(main_frame, text="运行状态", font=("Arial", 12))
        status_frame.pack(fill=tk.X, pady=10)
        
        # 运行时间和统计
        info_frame = tk.Frame(status_frame)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.runtime_var = tk.StringVar(value="运行时间: 0秒")
        tk.Label(info_frame, textvariable=self.runtime_var, 
                font=("Arial", 14, "bold"), fg="blue").pack(anchor=tk.W)
        
        self.stats_var = tk.StringVar(value="统计: 帧数=0, 错误=0")
        tk.Label(info_frame, textvariable=self.stats_var, 
                font=("Arial", 12)).pack(anchor=tk.W)
        
        self.heartbeat_var = tk.StringVar(value="💓 心跳正常")
        tk.Label(info_frame, textvariable=self.heartbeat_var, 
                font=("Arial", 12), fg="green").pack(anchor=tk.W)
        
        # 视频控制
        video_frame = tk.LabelFrame(main_frame, text="视频播放测试", font=("Arial", 12))
        video_frame.pack(fill=tk.X, pady=10)
        
        control_frame = tk.Frame(video_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Button(control_frame, text="📁 选择视频", font=("Arial", 12),
                 command=self.select_video, bg="lightgreen").pack(side=tk.LEFT, padx=5)
        
        self.play_btn = tk.Button(control_frame, text="▶️ 开始播放", font=("Arial", 12),
                                 command=self.toggle_play, state="disabled", bg="orange")
        self.play_btn.pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="⏹️ 停止", font=("Arial", 12),
                 command=self.stop_play, bg="red", fg="white").pack(side=tk.LEFT, padx=5)
        
        self.video_info_var = tk.StringVar(value="未选择视频文件")
        tk.Label(control_frame, textvariable=self.video_info_var, 
                font=("Arial", 10)).pack(side=tk.LEFT, padx=10)
        
        # 日志显示
        log_frame = tk.LabelFrame(main_frame, text="详细日志", font=("Arial", 12))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 文本框和滚动条
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(text_frame, font=("Consolas", 10), wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 控制按钮
        button_frame = tk.Frame(main_frame, bg="lightblue")
        button_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(button_frame, text="🧹 清理内存", command=self.cleanup_memory).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="💾 保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="🔒 强制保持运行", command=self.force_keep_alive).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="❌ 手动关闭", command=self.manual_close, 
                 bg="red", fg="white").pack(side=tk.RIGHT, padx=5)
        
        # 添加初始日志
        self.add_log("程序启动成功")
        self.add_log("开始长时间运行测试")
        self.add_log("目标: 找出播放一段时间后自动关闭的原因")
        self.add_log("建议: 让程序运行30分钟以上")
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        runtime = int(time.time() - self.start_time)
        log_entry = f"[{timestamp}] [{runtime:04d}s] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 150:
            self.log_text.delete("1.0", "30.0")
        
        print(log_entry.strip())
        
        # 更新心跳
        self.last_heartbeat = time.time()
    
    def select_video(self):
        """选择视频"""
        try:
            self.add_log("打开文件选择对话框")
            
            file_path = filedialog.askopenfilename(
                title="选择视频文件进行长时间播放测试",
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_info_var.set(f"已选择: {filename}")
                self.add_log(f"选择视频: {filename}")
                
                # 测试视频
                self.test_video()
            else:
                self.add_log("用户取消文件选择")
                
        except Exception as e:
            self.add_log(f"❌ 选择视频失败: {e}")
            self.error_count += 1
    
    def test_video(self):
        """测试视频"""
        if not self.video_path:
            return
        
        try:
            self.add_log("开始测试视频文件...")
            
            import cv2
            
            # 尝试打开视频
            cap = cv2.VideoCapture(self.video_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                duration = frame_count / fps if fps > 0 else 0
                
                self.add_log(f"✅ 视频信息: {width}x{height}, {fps:.1f}fps")
                self.add_log(f"   总帧数: {frame_count}, 时长: {duration:.1f}秒")
                
                # 测试读取前几帧
                success_count = 0
                for i in range(min(10, frame_count)):
                    ret, frame = cap.read()
                    if ret:
                        success_count += 1
                    else:
                        break
                
                self.add_log(f"   测试读取: {success_count}/10 帧成功")
                
                cap.release()
                
                if success_count > 0:
                    self.add_log("✅ 视频测试通过，可以开始长时间播放测试")
                    self.play_btn.configure(state="normal")
                else:
                    self.add_log("❌ 视频无法正常读取")
                    self.error_count += 1
            else:
                self.add_log("❌ 无法打开视频文件")
                self.error_count += 1
                
        except Exception as e:
            self.add_log(f"❌ 视频测试失败: {e}")
            self.error_count += 1
    
    def toggle_play(self):
        """切换播放"""
        if not self.video_path:
            return
        
        if not self.is_playing:
            self.start_play()
        else:
            self.stop_play()
    
    def start_play(self):
        """开始播放"""
        try:
            self.add_log("🎬 开始长时间播放测试")
            self.is_playing = True
            self.play_btn.configure(text="⏸️ 暂停播放")
            
            # 启动播放线程
            self.play_thread = threading.Thread(target=self.play_loop, daemon=True)
            self.play_thread.start()
            
        except Exception as e:
            self.add_log(f"❌ 启动播放失败: {e}")
            self.error_count += 1
    
    def play_loop(self):
        """播放循环 - 这里可能是自动关闭的原因"""
        try:
            import cv2
            
            self.add_log("播放线程启动")
            
            cap = cv2.VideoCapture(self.video_path)
            if not cap.isOpened():
                self.root.after(0, lambda: self.add_log("❌ 播放线程: 无法打开视频"))
                return
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                fps = 30
            
            frame_delay = 1.0 / fps
            local_frame_count = 0
            loop_count = 0
            
            self.root.after(0, lambda: self.add_log(f"播放参数: FPS={fps:.1f}, 延迟={frame_delay:.3f}秒"))
            
            while self.is_playing and self.running:
                try:
                    start_time = time.time()
                    
                    ret, frame = cap.read()
                    if ret:
                        local_frame_count += 1
                        self.frame_count += 1
                        
                        # 每500帧报告一次（约16秒）
                        if local_frame_count % 500 == 0:
                            self.root.after(0, lambda lfc=local_frame_count: 
                                          self.add_log(f"📊 播放进度: {lfc}帧 (循环{loop_count+1}次)"))
                        
                        # 控制播放速度
                        elapsed = time.time() - start_time
                        sleep_time = frame_delay - elapsed
                        if sleep_time > 0:
                            time.sleep(sleep_time)
                    else:
                        # 视频结束，重新开始
                        loop_count += 1
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        self.root.after(0, lambda lc=loop_count: 
                                      self.add_log(f"🔄 视频播放完毕，开始第{lc+1}次循环"))
                        
                        # 每次循环后稍微休息
                        time.sleep(0.1)
                        
                except Exception as e:
                    self.root.after(0, lambda err=str(e): 
                                  self.add_log(f"❌ 播放循环错误: {err}"))
                    self.error_count += 1
                    time.sleep(1)  # 出错后等待1秒
            
            cap.release()
            self.root.after(0, lambda: self.add_log("播放线程正常结束"))
            
        except Exception as e:
            self.root.after(0, lambda err=str(e): 
                          self.add_log(f"❌ 播放线程异常: {err}"))
            self.error_count += 1
        finally:
            # 确保UI状态正确
            self.root.after(0, lambda: self.play_btn.configure(text="▶️ 开始播放"))
    
    def stop_play(self):
        """停止播放"""
        self.add_log("⏹️ 停止播放测试")
        self.is_playing = False
        self.play_btn.configure(text="▶️ 开始播放")
        
        if self.play_thread and self.play_thread.is_alive():
            self.add_log("等待播放线程结束...")
            self.play_thread.join(timeout=3.0)
            if self.play_thread.is_alive():
                self.add_log("⚠️ 播放线程未能及时结束")
            else:
                self.add_log("✅ 播放线程已结束")
    
    def start_heartbeat(self):
        """启动心跳监控"""
        def heartbeat():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)
                    
                    # 更新运行时间
                    self.runtime_var.set(f"运行时间: {runtime}秒 ({runtime//60}分{runtime%60}秒)")
                    
                    # 更新统计
                    self.stats_var.set(f"统计: 帧数={self.frame_count}, 错误={self.error_count}")
                    
                    # 更新心跳状态
                    heartbeat_age = time.time() - self.last_heartbeat
                    if heartbeat_age < 5:
                        self.heartbeat_var.set("💓 心跳正常")
                    elif heartbeat_age < 30:
                        self.heartbeat_var.set("⚠️ 心跳缓慢")
                    else:
                        self.heartbeat_var.set("❌ 心跳异常")
                    
                    # 每60秒报告状态
                    if runtime > 0 and runtime % 60 == 0:
                        self.add_log(f"💓 程序运行正常 - {runtime//60}分钟, 处理{self.frame_count}帧, {self.error_count}个错误")
                    
                    # 每10分钟强制垃圾回收
                    if runtime > 0 and runtime % 600 == 0:
                        self.cleanup_memory()
                    
                    # 继续心跳
                    self.root.after(1000, heartbeat)
                    
                except Exception as e:
                    print(f"心跳监控错误: {e}")
                    self.root.after(5000, heartbeat)  # 出错后5秒重试
        
        heartbeat()
        self.add_log("💓 心跳监控启动")
    
    def cleanup_memory(self):
        """清理内存"""
        try:
            before = len(gc.get_objects())
            collected = gc.collect()
            after = len(gc.get_objects())
            self.add_log(f"🧹 内存清理: 回收{collected}个对象, 对象数量 {before} -> {after}")
        except Exception as e:
            self.add_log(f"❌ 内存清理失败: {e}")
    
    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get("1.0", tk.END)
            timestamp = int(time.time())
            filename = f"long_test_log_{timestamp}.txt"
            
            with open(filename, "w", encoding="utf-8") as f:
                f.write(f"长时间运行测试日志\n")
                f.write(f"开始时间: {time.ctime(self.start_time)}\n")
                f.write(f"运行时长: {int(time.time() - self.start_time)}秒\n")
                f.write(f"处理帧数: {self.frame_count}\n")
                f.write(f"错误次数: {self.error_count}\n")
                f.write("=" * 50 + "\n")
                f.write(log_content)
            
            self.add_log(f"💾 日志已保存: {filename}")
        except Exception as e:
            self.add_log(f"❌ 保存日志失败: {e}")
    
    def force_keep_alive(self):
        """强制保持运行"""
        self.add_log("🔒 强制保持运行模式激活")
        self.add_log("程序将继续运行，监控自动关闭问题")
        messagebox.showinfo("保持运行", "程序已设置为强制保持运行模式！\n将继续监控自动关闭问题。")
    
    def manual_close(self):
        """手动关闭"""
        self.add_log("用户请求手动关闭")
        
        runtime = int(time.time() - self.start_time)
        result = messagebox.askyesno("手动关闭", 
                                   f"确定要关闭程序吗？\n\n运行时间: {runtime//60}分{runtime%60}秒\n处理帧数: {self.frame_count}\n错误次数: {self.error_count}")
        if result:
            self.add_log("✅ 用户确认手动关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")
    
    def on_closing(self):
        """窗口关闭处理"""
        self.add_log("⚠️ 检测到窗口关闭事件")
        
        runtime = int(time.time() - self.start_time)
        result = messagebox.askyesno("确认关闭", 
                                   f"真的要关闭长时间运行测试吗？\n\n运行时间: {runtime//60}分{runtime%60}秒\n\n选择'否'继续监控自动关闭问题")
        if result:
            self.add_log("✅ 用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")
    
    def cleanup(self):
        """清理资源"""
        self.add_log("开始清理资源...")
        self.running = False
        self.is_playing = False
        
        if self.cap:
            try:
                self.cap.release()
                self.add_log("✅ 视频资源已释放")
            except:
                self.add_log("⚠️ 视频资源释放失败")
        
        # 等待播放线程结束
        if self.play_thread and self.play_thread.is_alive():
            self.add_log("等待播放线程结束...")
            self.play_thread.join(timeout=3.0)
        
        self.add_log("✅ 资源清理完成")
    
    def run(self):
        """运行测试"""
        try:
            print("🎬 启动长时间运行测试界面...")
            print("💡 这个测试专门监控播放一段时间后是否会自动关闭")
            print("💡 建议运行30分钟以上，观察是否会自动关闭")
            
            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    filename = os.path.basename(video_file)
                    self.video_info_var.set(f"命令行: {filename}")
                    self.add_log(f"命令行参数: {filename}")
                    self.root.after(1000, self.test_video)
                else:
                    self.add_log(f"命令行文件不存在: {video_file}")
            
            # 启动主循环
            self.root.mainloop()
            
            print("🔚 长时间运行测试结束")
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 简化长时间运行测试")
    print("=" * 60)
    print("目的: 监控程序播放一段时间后是否会自动关闭")
    print("方法: 循环播放视频，详细记录运行状态")
    print("建议: 让程序运行30分钟以上")
    print("注意: 如果程序自动关闭，请检查保存的日志文件")
    print("=" * 60)
    
    try:
        test = SimpleLongTest()
        test.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        traceback.print_exc()
        
        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
