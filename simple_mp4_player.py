#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版MP4播放器 - 带字幕功能演示
不依赖Whisper模型，可以立即运行展示界面
已修复OpenCV MSMF和pthread错误
"""

import os
import threading
import time
import tempfile
import tkinter as tk
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont
from tkinter import ttk, filedialog, messagebox

# 检查字幕生成依赖
print("🔍 检查字幕生成依赖...")

try:
    from faster_whisper import WhisperModel  # noqa: F401
    WHISPER_AVAILABLE = True
    print("✅ faster-whisper 可用")
except ImportError as e:
    WHISPER_AVAILABLE = False
    print(f"❌ faster-whisper 不可用: {e}")

try:
    import moviepy  # noqa: F401
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")
    print("安装命令: pip install moviepy==1.0.3")
except Exception as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 导入异常: {e}")

try:
    from opencc import OpenCC
    OPENCC_AVAILABLE = True
    print("✅ opencc 可用 - 支持繁简转换")
except ImportError as e:
    OPENCC_AVAILABLE = False
    print(f"❌ opencc 不可用: {e}")
    print("安装命令: pip install opencc-python-reimplemented")

# 检查音频播放库的可用性
PYGAME_AVAILABLE = False
VLC_AVAILABLE = False
PLAYSOUND_AVAILABLE = False

# 检查VLC (最优选择 - 支持位置播放)
try:
    import vlc
    VLC_AVAILABLE = True
    print("✅ python-vlc 可用 - 支持位置播放")
except (ImportError, FileNotFoundError, OSError) as e:
    VLC_AVAILABLE = False
    print("❌ python-vlc 不可用或VLC未安装")
    print("需要安装VLC媒体播放器: https://www.videolan.org/vlc/")

# 检查playsound (简单播放)
try:
    import playsound
    PLAYSOUND_AVAILABLE = True
    print("✅ playsound 可用 - 简单音频播放")
except ImportError:
    print("❌ playsound 不可用")

# 最后检查pygame作为备选
try:
    import pygame
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用 - 备选音频播放")
except ImportError:
    print("❌ pygame 不可用")

try:
    import winsound
    WINSOUND_AVAILABLE = True
    print("✅ winsound 可用 - Windows内置音频播放")
except ImportError as e:
    WINSOUND_AVAILABLE = False
    print(f"❌ winsound 不可用: {e}")

# 音频播放优先级：VLC > playsound > pygame > winsound
AUDIO_AVAILABLE = VLC_AVAILABLE or PLAYSOUND_AVAILABLE or PYGAME_AVAILABLE or WINSOUND_AVAILABLE


class PygamePositionAudioPlayer:
    """使用pygame.mixer.music.play(start=pos)的音频播放器"""

    def __init__(self):
        self.current_audio_path = None
        self.is_playing = False

    def load_audio(self, audio_path):
        """加载音频文件"""
        try:
            self.current_audio_path = audio_path
            print(f"✅ 加载音频文件: {audio_path}")
            return True
        except Exception as e:
            print(f"❌ 加载音频失败: {e}")
            return False

    def play(self, start_position_seconds=0):
        """播放音频，强制使用pygame的start参数"""
        try:
            if not self.current_audio_path:
                print("❌ 没有加载音频文件")
                return False

            if not PYGAME_AVAILABLE:
                print("❌ pygame不可用")
                return False

            print(f"🔊 强制使用pygame start参数播放，位置: {start_position_seconds:.2f}秒")

            import pygame

            # 停止当前播放
            pygame.mixer.music.stop()

            # 加载音频文件
            pygame.mixer.music.load(self.current_audio_path)
            pygame.mixer.music.set_volume(1.0)

            # 强制使用start参数，即使是0秒也使用
            print(f"🔊 pygame.mixer.music.play(start={start_position_seconds:.2f})")
            pygame.mixer.music.play(start=start_position_seconds)

            self.is_playing = True

            # 检查播放状态
            import time
            time.sleep(0.2)  # 等待更长时间确保播放开始
            is_actually_playing = pygame.mixer.music.get_busy()

            if is_actually_playing:
                print(f"✅ pygame start参数播放成功，位置: {start_position_seconds:.2f}秒")
                return True
            else:
                print(f"❌ pygame start参数播放失败，音频可能太短或格式不支持")
                # 尝试降级方案：不使用start参数
                print("🔄 尝试降级方案：不使用start参数")
                pygame.mixer.music.play()
                time.sleep(0.1)
                if pygame.mixer.music.get_busy():
                    print("✅ 降级方案成功（从头播放）")
                    return True
                else:
                    print("❌ 降级方案也失败")
                    return False

        except Exception as e:
            print(f"❌ pygame start参数播放失败: {e}")
            print(f"   错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()

            # 尝试降级方案
            try:
                print("🔄 尝试降级方案：不使用start参数")
                import pygame
                pygame.mixer.music.play()
                import time
                time.sleep(0.1)
                if pygame.mixer.music.get_busy():
                    print("✅ 降级方案成功（从头播放）")
                    return True
                else:
                    print("❌ 降级方案也失败")
                    return False
            except Exception as e2:
                print(f"❌ 降级方案失败: {e2}")
                return False

    def stop(self):
        """停止播放"""
        try:
            if PYGAME_AVAILABLE:
                import pygame
                pygame.mixer.music.stop()

            self.is_playing = False
            print("✅ 音频播放已停止")

        except Exception as e:
            print(f"❌ 停止音频失败: {e}")

    def is_audio_playing(self):
        """检查是否正在播放"""
        try:
            if PYGAME_AVAILABLE:
                import pygame
                return pygame.mixer.music.get_busy()
            else:
                return self.is_playing
        except:
            return False


class SimpleMP4Player:
    def __init__(self, root):
        self.root = root
        self.root.title("MP4播放器 - 实时字幕演示")
        self.root.geometry("1200x800")
        
        # 视频相关变量
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.is_paused = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.frame_delay = 1/30
        
        # 字幕相关变量
        self.subtitle_text = ""
        self.demo_subtitle_thread = None

        # 实时字幕生成变量
        self.whisper_model = None
        self.subtitle_segments = []
        self.current_subtitle_index = 0
        self.subtitle_generation_complete = False
        self.subtitle_streaming_mode = False  # 流式字幕模式
        self.audio_path = None
        self.auto_generate_subtitles = True  # 自动生成字幕
        self.gpu_available = False  # GPU可用性
        self.preferred_device = "cpu"  # 首选设备
        self.current_model_size = "large-v3"  # 默认使用large-v3

        # 简体中文转换器
        self.opencc_converter = None
        if OPENCC_AVAILABLE:
            try:
                self.opencc_converter = OpenCC('t2s')  # 繁体转简体
                print("✅ 繁简转换器初始化成功")
            except Exception as e:
                print(f"❌ 繁简转换器初始化失败: {e}")
                self.opencc_converter = None

        # 音频播放相关变量
        self.audio_file_path = None
        self.audio_start_time = None
        self.audio_paused_position = 0
        self.audio_enabled = True
        self.audio_extraction_complete = False  # 音频提取完成标志

        # 新的音频播放器
        self.audio_player = PygamePositionAudioPlayer()

        # 新的正确音频同步系统
        self.video_start_real_time = None  # 视频开始播放的真实时间
        self.video_start_position = 0      # 视频开始播放时的位置（秒）
        self.audio_start_real_time = None  # 音频开始播放的真实时间
        self.audio_start_position = 0      # 音频开始播放时的位置（秒）
        self.sync_offset = 0               # 同步偏移（毫秒）
        self.sync_lock = threading.Lock()  # 同步锁

        # 线程安全锁 - 增强版
        self.video_lock = threading.RLock()  # 使用可重入锁
        self.frame_read_lock = threading.RLock()
        self.capture_lock = threading.RLock()  # 专门用于VideoCapture操作的锁

        # 进度条拖动防抖
        self.seek_timer = None
        self.last_seek_time = 0
        self.seek_debounce_delay = 0.5  # 增加到500ms防抖延迟，减少频繁操作

        # 临时音频文件管理
        self.temp_audio_files = []

        # 拖动状态管理
        self.is_seeking = False
        self.seek_in_progress = False
        self.last_seek_position = -1
        self.pause_progress_updates = False

        # 音频重启管理
        self.audio_needs_restart = False
        self.audio_restart_position = 0

        # 字幕样式设置
        self.subtitle_font_size = 24
        self.subtitle_position_y = 0.85  # 相对位置 (0-1)
        self.subtitle_position_x = 0.5   # 水平位置 (0-1)
        self.subtitle_color = (0, 0, 0)  # 黑色字幕
        self.subtitle_bg_color = (255, 255, 255, 0)  # 完全透明背景
        self.subtitle_draggable = True   # 字幕可拖动
        self.subtitle_drag_start = None  # 拖动开始位置

        # 播放速度控制
        self.playback_speed = 1.0  # 默认播放速度
        self.speed_adjusted_frame_delay = None  # 速度调整后的帧延迟
        
        self.setup_ui()

        # 检查GPU可用性
        self.check_gpu_availability()

        # 加载Whisper模型
        if WHISPER_AVAILABLE:
            self.load_whisper_model("large-v3")
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        ttk.Button(control_frame, text="选择MP4文件", 
                  command=self.select_video_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放控制按钮
        self.play_button = ttk.Button(control_frame, text="播放", 
                                     command=self.toggle_play, state="disabled")
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="停止",
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))

        # 播放速度控制
        ttk.Label(control_frame, text="速度:").pack(side=tk.LEFT, padx=(10, 5))
        self.speed_var = tk.DoubleVar(value=1.0)
        speed_scale = ttk.Scale(control_frame, from_=0.25, to=2.0,
                               orient=tk.HORIZONTAL, variable=self.speed_var,
                               command=self.update_playback_speed, length=100)
        speed_scale.pack(side=tk.LEFT, padx=(0, 5))

        self.speed_label = ttk.Label(control_frame, text="1.0x")
        self.speed_label.pack(side=tk.LEFT, padx=(0, 10))



        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(control_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_progress_drag)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # 绑定进度条事件
        self.progress_bar.bind("<Button-1>", self.on_progress_click)
        self.progress_bar.bind("<B1-Motion>", self.on_progress_drag_motion)
        self.progress_bar.bind("<ButtonRelease-1>", self.on_progress_end_drag)
        
        # 时间显示
        self.time_label = ttk.Label(control_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 字幕控制面板（简化版）
        subtitle_frame = ttk.LabelFrame(main_frame, text="字幕控制")
        subtitle_frame.pack(fill=tk.X, pady=(0, 10))

        # 字幕生成按钮
        self.generate_subtitle_btn = ttk.Button(subtitle_frame, text="🎤 生成字幕",
                                               command=self.generate_subtitles)
        self.generate_subtitle_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # 停止生成按钮
        self.stop_generation_btn = ttk.Button(subtitle_frame, text="⏹️ 停止生成",
                                             command=self.stop_subtitle_generation, state="disabled")
        self.stop_generation_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # 保存字幕按钮
        self.save_subtitle_btn = ttk.Button(subtitle_frame, text="💾 保存字幕",
                                           command=self.save_subtitles, state="disabled")
        self.save_subtitle_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # 字幕开关
        self.subtitle_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(subtitle_frame, text="显示字幕",
                       variable=self.subtitle_enabled).pack(side=tk.LEFT, padx=10, pady=5)

        # 流式状态指示器
        self.streaming_status_var = tk.StringVar(value="")
        self.streaming_status_label = ttk.Label(subtitle_frame, textvariable=self.streaming_status_var,
                                               foreground="green", font=("Arial", 8))
        self.streaming_status_label.pack(side=tk.RIGHT, padx=5, pady=5)

        # 根据依赖可用性设置按钮状态
        if not (WHISPER_AVAILABLE and MOVIEPY_AVAILABLE):
            self.generate_subtitle_btn.configure(state="disabled")

        # 初始化字幕颜色变量（内部使用，不显示在界面）
        self.color_var = tk.StringVar(value="黑色")

        # 初始化进度文本变量
        if WHISPER_AVAILABLE and MOVIEPY_AVAILABLE:
            initial_status = "就绪 - 可生成字幕"
        else:
            missing = []
            if not WHISPER_AVAILABLE:
                missing.append("faster-whisper")
            if not MOVIEPY_AVAILABLE:
                missing.append("moviepy")
            initial_status = f"缺少依赖: {', '.join(missing)}"

        self.progress_text_var = tk.StringVar(value=initial_status)

        # 音频控制（简化版）
        self.audio_enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(subtitle_frame, text="启用音频",
                       variable=self.audio_enabled_var).pack(side=tk.LEFT, padx=10, pady=5)

        # 初始化内部变量（不显示在界面）
        self.sync_offset_var = tk.DoubleVar(value=0)
        self.seek_mode_var = tk.StringVar(value="位置播放")
        self.sync_status_var = tk.StringVar(value="未开始播放")
        self.position_x_var = tk.DoubleVar(value=self.subtitle_position_x)
        self.position_y_var = tk.DoubleVar(value=self.subtitle_position_y)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=2)
        self.video_frame.pack(fill=tk.BOTH, expand=True)
        
        self.video_label = ttk.Label(self.video_frame, text="请选择MP4文件开始播放\n\n支持功能：\n• 视频播放控制\n• 实时字幕显示\n• 点击拖动字幕位置\n• 播放速度调整\n• 音视频同步播放")
        self.video_label.pack(expand=True)

        # 绑定字幕拖动事件
        self.video_label.bind("<Button-1>", self.on_subtitle_drag_start)
        self.video_label.bind("<B1-Motion>", self.on_subtitle_drag_motion)
        self.video_label.bind("<ButtonRelease-1>", self.on_subtitle_drag_end)

        # 绑定窗口大小变化事件，用于最大化时调整视频画面
        self.root.bind("<Configure>", self.on_window_configure)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪 - 选择视频文件开始播放")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def select_video_file(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP4文件",
            filetypes=[("MP4文件", "*.mp4"), ("AVI文件", "*.avi"), ("MOV文件", "*.mov"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.load_video(file_path)
            
    def load_video(self, video_path):
        """加载视频文件 - 安全版本"""
        try:
            # 停止当前播放
            self.stop_video()

            print(f"🎬 加载视频: {video_path}")

            # 使用最安全的方式打开视频文件
            self.cap = self.create_safe_capture(video_path)
            if not self.cap or not self.cap.isOpened():
                raise Exception("无法打开视频文件")

            self.video_path = video_path

            # 安全地获取视频信息
            try:
                self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
                self.fps = self.cap.get(cv2.CAP_PROP_FPS)

                if self.total_frames <= 0:
                    self.total_frames = 1000  # 默认值
                if self.fps <= 0:
                    self.fps = 30  # 默认帧率

                self.frame_delay = 1.0 / self.fps

                print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps")

            except Exception as prop_error:
                print(f"⚠️ 获取视频属性失败: {prop_error}")
                # 使用默认值
                self.total_frames = 1000
                self.fps = 30
                self.frame_delay = 1/30

            # 重置进度条
            self.progress_bar.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0

            # 显示第一帧
            self.show_frame()

            # 更新状态
            duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.status_var.set(f"视频已加载: {os.path.basename(video_path)} "
                              f"({self.total_frames}帧, {duration:.1f}秒)")

            # 启用播放按钮
            self.play_button.configure(state="normal")

            # 提取音频文件用于播放
            if AUDIO_AVAILABLE and MOVIEPY_AVAILABLE:
                self.extract_audio_for_playback(video_path)

        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)

    def create_safe_capture(self, video_path):
        """创建最安全的VideoCapture"""
        try:
            # 尝试多种安全的后端
            safe_methods = [
                ("FFMPEG", cv2.CAP_FFMPEG),
                ("默认", None),
            ]

            for method_name, backend in safe_methods:
                try:
                    print(f"🔧 尝试{method_name}后端...")

                    # 使用capture_lock确保线程安全
                    with self.capture_lock:
                        if backend is not None:
                            cap = cv2.VideoCapture(video_path, backend)
                        else:
                            cap = cv2.VideoCapture(video_path)

                        if cap and cap.isOpened():
                            # 设置线程安全的缓冲区大小
                            try:
                                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲区，避免线程冲突
                            except:
                                pass

                            # 验证能否读取帧
                            ret, test_frame = cap.read()
                            if ret and test_frame is not None:
                                try:
                                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置位置
                                except Exception as e:
                                    print(f"⚠️ 重置位置失败: {e}")
                                print(f"✅ {method_name}后端成功")
                                return cap
                            else:
                                cap.release()
                                print(f"❌ {method_name}后端无法读取帧")
                        else:
                            if cap:
                                cap.release()
                            print(f"❌ {method_name}后端无法打开")

                except Exception as e:
                    print(f"❌ {method_name}后端异常: {e}")
                    continue

            print("❌ 所有安全后端都失败")
            return None

        except Exception as e:
            print(f"❌ 创建安全capture失败: {e}")
            return None
            
    def show_frame(self, frame=None):
        """显示当前帧 - 线程安全版本"""
        if self.cap is None:
            return

        # 如果没有传入帧，则读取当前帧
        if frame is None:
            with self.frame_read_lock:  # 线程安全读取
                try:
                    ret, frame = self.cap.read()
                    if not ret:
                        return
                except Exception as e:
                    print(f"❌ 读取帧失败: {e}")
                    return

        # 添加字幕到帧
        if self.subtitle_enabled.get():
            # 检查是否有字幕需要显示
            if self.subtitle_text and self.subtitle_text.strip():
                frame = self.add_subtitle_to_frame(frame, self.subtitle_text)

        # 转换为PIL图像
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)

        # 调整图像大小以适应显示区域
        display_width = self.video_frame.winfo_width()
        display_height = self.video_frame.winfo_height()

        if display_width > 1 and display_height > 1:
            # 保持宽高比
            img_ratio = pil_image.width / pil_image.height
            display_ratio = display_width / display_height

            # 自适应边距：根据窗口大小动态调整
            if display_width > 1200 or display_height > 800:
                # 大窗口或全屏时减少边距
                margin = 10
            else:
                # 小窗口时保持适当边距
                margin = 20

            if img_ratio > display_ratio:
                new_width = display_width - margin
                new_height = int(new_width / img_ratio)
            else:
                new_height = display_height - margin
                new_width = int(new_height * img_ratio)

            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 转换为Tkinter图像
        photo = ImageTk.PhotoImage(pil_image)
        self.video_label.configure(image=photo, text="")
        self.video_label.image = photo  # 保持引用

        # 更新进度条和时间（除非正在拖动）
        if not self.pause_progress_updates:
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps if self.fps > 0 else 0
            total_time = self.total_frames / self.fps if self.fps > 0 else 0
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")

            # 更新同步状态
            self.update_sync_status()

            # 更新字幕显示
            if self.subtitle_segments:
                current_time = self.current_frame / self.fps if self.fps > 0 else 0
                self.update_current_subtitle(current_time)
            
    def add_subtitle_to_frame(self, frame, text):
        """在帧上添加字幕"""
        if not text.strip():
            return frame
            
        # 转换为PIL图像以便添加文字
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载字体
        try:
            # Windows系统字体路径
            font_paths = [
                "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/arial.ttf"   # Arial
            ]
            
            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, self.subtitle_font_size)
                    break
            
            if font is None:
                font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置
        img_width, img_height = pil_image.size
        
        # 分行处理长文本
        words = text.split()
        lines = []
        current_line = ""
        max_width = img_width * 0.9  # 最大宽度为图像宽度的90%
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = draw.textbbox((0, 0), test_line, font=font)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # 绘制每一行
        line_height = self.subtitle_font_size + 5
        total_text_height = len(lines) * line_height
        start_y = int(img_height * self.subtitle_position_y - total_text_height / 2)

        for i, line in enumerate(lines):
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # 使用水平位置设置
            x = int(img_width * self.subtitle_position_x - text_width / 2)
            y = start_y + i * line_height

            # 不绘制背景（透明背景）
            # 只绘制文字，使用黑色或其他颜色
            draw.text((x, y), line, font=font, fill=self.subtitle_color)
        
        # 转换回OpenCV格式
        frame_with_subtitle = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        return frame_with_subtitle

    def toggle_play(self):
        """切换播放/暂停状态"""
        if not self.cap:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        # 检查音频提取状态
        if hasattr(self, 'audio_extraction_complete') and not self.audio_extraction_complete:
            # 如果音频正在提取中，检查按钮状态
            if self.play_button.cget("state") == "disabled":
                messagebox.showinfo("提示", "音频正在提取中，请稍候...")
                return

        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()

    def play_video(self):
        """开始播放视频 - 音频同步版本"""
        if not self.cap:
            return

        self.is_playing = True
        self.is_paused = False
        self.play_button.configure(text="暂停")

        # 设置正确的音频同步基准
        with self.sync_lock:
            current_time = time.time()
            video_position = self.current_frame / self.fps if self.fps > 0 else 0

            # 正确的同步逻辑
            self.video_start_real_time = current_time
            self.video_start_position = video_position
            self.audio_start_real_time = current_time
            self.audio_start_position = video_position  # 音频也应该从相同位置开始

            print(f"🔄 正确同步基准设置: 位置{video_position:.2f}s, 偏移{self.sync_offset:.0f}ms")

        # 启动音频播放（延迟重试机制）
        def try_play_audio():
            print(f"🔊 尝试播放音频 - 启用状态: {self.audio_enabled_var.get()}")
            print(f"🔊 音频文件路径: {self.audio_file_path}")
            print(f"🔊 同步偏移: {self.sync_offset}ms")

            if self.audio_enabled_var.get():
                if self.audio_file_path and os.path.exists(self.audio_file_path):
                    print("🔊 音频文件存在，开始同步播放")
                    if self.audio_paused_position > 0:
                        self.resume_audio_sync()
                    else:
                        self.play_audio_sync()
                else:
                    print("🔊 音频文件不存在，1秒后重试")
                    self.root.after(1000, try_play_audio)
            else:
                print("🔊 音频已禁用")

        # 立即尝试一次，然后延迟重试
        try_play_audio()

        # 启动视频播放线程
        self.start_playback_loop()

        # 启动字幕显示
        if self.subtitle_enabled.get():
            # 如果有AI字幕（完成或流式模式），不启动演示字幕
            if not (self.subtitle_generation_complete or self.subtitle_streaming_mode or self.subtitle_segments):
                self.start_demo_subtitles()

    def start_playback_loop(self):
        """启动播放循环"""
        # 检查是否已有播放线程在运行
        if hasattr(self, 'video_thread') and self.video_thread.is_alive():
            print("🔄 播放循环已在运行")
            return

        print("🔄 启动新的播放循环线程")
        self.video_thread = threading.Thread(target=self.video_playback_loop, daemon=True)
        self.video_thread.start()

    def pause_video(self):
        """暂停视频"""
        self.is_playing = False
        self.is_paused = True
        self.play_button.configure(text="播放")

        # 暂停音频
        self.pause_audio()

    def stop_video(self):
        """停止视频"""
        self.is_playing = False
        self.is_paused = False
        self.play_button.configure(text="播放")

        # 停止音频
        self.stop_audio()

        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.show_frame()

    def seek_video(self, value):
        """跳转到指定帧 - 优化版本，减少卡顿"""
        if not self.cap:
            return

        frame_number = int(float(value))
        current_time = time.time()

        # 检查是否正在seek中，避免重复操作
        if self.seek_in_progress:
            print("🔄 seek正在进行中，跳过此次操作")
            return

        # 检查是否与上次位置相同，避免重复操作
        if abs(frame_number - self.last_seek_position) < 5:  # 5帧以内认为是相同位置
            return

        # 防抖机制：取消之前的定时器
        if self.seek_timer:
            self.root.after_cancel(self.seek_timer)

        # 如果距离上次seek时间太短，延迟执行
        time_since_last_seek = current_time - self.last_seek_time
        if time_since_last_seek < self.seek_debounce_delay:
            # 延迟执行seek
            delay_ms = int((self.seek_debounce_delay - time_since_last_seek) * 1000)
            self.seek_timer = self.root.after(delay_ms, lambda: self.perform_seek(frame_number))
        else:
            # 立即执行seek
            self.perform_seek(frame_number)

    def perform_seek(self, frame_number):
        """执行实际的seek操作 - 优化版本"""
        if self.seek_in_progress:
            print("🔄 seek已在进行中，跳过")
            return

        self.seek_in_progress = False
        print(f"🎬 视频跳转到帧: {frame_number}")
        self.last_seek_time = time.time()
        self.last_seek_position = frame_number

        try:
            # 智能选择seek策略
            current_frame = self.current_frame
            frame_diff = abs(frame_number - current_frame)

            # 如果跳转距离较小，使用轻量级方法
            if frame_diff < 1000:  # 小于1000帧使用轻量级方法
                print(f"🔄 跳转距离较小({frame_diff}帧)，使用轻量级方法")
                success = self.lightweight_seek_to_frame(frame_number)
            else:
                print(f"🔄 跳转距离较大({frame_diff}帧)，直接使用安全方法")
                success = False

            # 如果轻量级方法失败，使用安全方法
            if not success:
                print("🔄 轻量级跳转失败，尝试安全跳转...")
                success = self.safe_seek_to_frame(frame_number)

            if success:
                # 智能选择音频跳转策略 - 音频同步版本
                if self.fps > 0:
                    position_seconds = frame_number / self.fps
                    # 拖动进度条时音频同步跳转
                    if PYGAME_AVAILABLE:
                        self.seek_audio_sync(position_seconds)
                    else:
                        self.seek_audio(position_seconds)

                # 如果视频暂停，显示当前帧
                if not self.is_playing:
                    self.show_frame()

                print(f"🎬 视频跳转完成: 帧{frame_number}, 时间{position_seconds:.2f}秒")
            else:
                print(f"❌ 视频跳转失败: 帧{frame_number}")

        finally:
            self.seek_in_progress = False

    def on_progress_click(self, event):
        """进度条点击事件 - 支持点击跳转"""
        print("🔄 进度条点击事件")

        # 计算点击位置对应的帧数
        widget = event.widget
        widget_width = widget.winfo_width()
        click_x = event.x

        # 计算点击位置的百分比
        if widget_width > 0:
            click_ratio = max(0, min(1, click_x / widget_width))
            target_frame = int(click_ratio * (self.total_frames - 1))

            print(f"🔄 点击位置: {click_x}/{widget_width}, 比例: {click_ratio:.3f}, 目标帧: {target_frame}")

            # 立即跳转到点击位置
            self.is_seeking = True
            self.pause_progress_updates = True

            # 更新进度条显示
            self.progress_var.set(target_frame)

            # 执行跳转
            self.perform_seek(target_frame)

            # 标记为拖动开始（为后续拖动做准备）
            self.drag_start_frame = target_frame

    def on_progress_drag_motion(self, event):
        """进度条拖动过程中"""
        if not hasattr(self, 'is_seeking'):
            self.is_seeking = False

        if not self.is_seeking:
            # 如果不是在拖动状态，开始拖动
            self.is_seeking = True
            self.pause_progress_updates = True
            print("🔄 开始拖动进度条")

        # 计算拖动位置对应的帧数
        widget = event.widget
        widget_width = widget.winfo_width()
        drag_x = max(0, min(widget_width, event.x))

        if widget_width > 0:
            drag_ratio = drag_x / widget_width
            target_frame = int(drag_ratio * (self.total_frames - 1))

            # 更新进度条显示（但不立即跳转，避免频繁跳转）
            self.progress_var.set(target_frame)

            # 更新时间显示
            current_time = target_frame / self.fps if self.fps > 0 else 0
            total_time = self.total_frames / self.fps if self.fps > 0 else 0
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")

    def on_progress_start_drag(self, _event):
        """进度条开始拖动"""
        print("🔄 开始拖动进度条")
        self.is_seeking = True
        # 暂停播放循环更新进度条
        self.pause_progress_updates = True

    def on_progress_end_drag(self, event):
        """进度条拖动结束"""
        print("🔄 结束拖动进度条")

        # 计算最终位置
        widget = event.widget
        widget_width = widget.winfo_width()
        end_x = max(0, min(widget_width, event.x))

        if widget_width > 0:
            end_ratio = end_x / widget_width
            target_frame = int(end_ratio * (self.total_frames - 1))

            print(f"🔄 拖动结束位置: {end_x}/{widget_width}, 比例: {end_ratio:.3f}, 目标帧: {target_frame}")

            # 更新进度条并执行最终跳转
            self.progress_var.set(target_frame)
            self.perform_seek(target_frame)
        else:
            # 降级方案：使用进度条当前值
            frame_number = int(self.progress_var.get())
            self.perform_seek(frame_number)

        # 恢复正常状态
        self.is_seeking = False
        self.pause_progress_updates = False

        # 清理临时变量
        if hasattr(self, 'drag_start_frame'):
            delattr(self, 'drag_start_frame')

    def on_progress_drag(self, value):
        """进度条拖动中 - 只更新显示，不执行seek"""
        if not self.is_seeking:
            # 如果不是在拖动中，执行正常的seek
            self.seek_video(value)
        else:
            # 拖动中只更新时间显示，不执行seek
            frame_number = int(float(value))
            current_time = frame_number / self.fps if self.fps > 0 else 0
            total_time = self.total_frames / self.fps if self.fps > 0 else 0
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")

    def lightweight_seek_to_frame(self, frame_number):
        """轻量级帧跳转方法 - 线程安全版本"""
        try:
            print(f"🔄 尝试轻量级跳转到帧{frame_number}...")

            # 使用双重锁保护，避免FFmpeg线程冲突
            with self.capture_lock:
                with self.video_lock:
                    # 检查capture是否有效
                    if not self.cap or not self.cap.isOpened():
                        print("❌ capture无效，跳转失败")
                        return False

                    try:
                        # 直接设置帧位置，不重新创建capture
                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                        self.current_frame = frame_number

                        # 验证跳转是否成功
                        actual_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                        frame_diff = abs(actual_frame - frame_number)

                        if frame_diff <= 10:  # 允许10帧误差
                            print(f"✅ 轻量级跳转成功: 目标{frame_number}, 实际{actual_frame}, 误差{frame_diff}")
                            return True
                        else:
                            print(f"⚠️ 轻量级跳转误差过大: 目标{frame_number}, 实际{actual_frame}, 误差{frame_diff}")
                            return False
                    except Exception as seek_error:
                        print(f"❌ 轻量级跳转操作失败: {seek_error}")
                        return False

        except Exception as e:
            print(f"❌ 轻量级跳转失败: {e}")
            return False

    def lightweight_seek_audio(self, position_seconds):
        """轻量级音频跳转 - 避免创建音频片段"""
        try:
            print(f"🔊 轻量级音频跳转到: {position_seconds:.2f}秒")
            if not self.audio_enabled_var.get():
                print("🔊 音频已禁用，跳过跳转")
                return
            if not AUDIO_AVAILABLE or not self.audio_file_path:
                print("🔊 音频不可用，跳过跳转")
                return
            # 使用简单的重新播放方法，避免创建音频片段
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.stop()
                    pygame.mixer.music.load(self.audio_file_path)
                    pygame.mixer.music.set_volume(1.0)
                    if self.is_playing:
                        print("🔊 尝试pygame播放音频,不带start参数...")
                        pygame.mixer.music.play()
                        self.audio_start_time = time.time() - position_seconds
                        self.audio_paused_position = 0
                        print(f"🔊 轻量级音频跳转完成（偏移方法）")
                    else:
                        self.audio_paused_position = position_seconds
                        print(f"🔊 视频暂停中，记录音频位置: {position_seconds:.2f}秒")
                except Exception as e:
                    print(f"❌ 轻量级音频跳转失败: {e}")
        except Exception as e:
            print(f"❌ 轻量级音频跳转异常: {e}")

    def safe_seek_to_frame(self, frame_number):
        """超级安全的帧跳转方法 - 线程安全版本"""
        print(f"🔄 开始安全跳转到帧{frame_number}...")
        try:
            # 使用双重锁保护，避免FFmpeg线程冲突
            with self.capture_lock:
                with self.video_lock:
                    print("🔄 使用完全重新创建capture的方法...")
                    was_playing = self.is_playing
                    print(f"🔄 保存播放状态: was_playing={was_playing}")
                    if self.is_playing:
                        self.is_playing = False
                    if self.cap:
                        try:
                            self.cap.release()
                            print("✅ 旧capture已释放")
                        except:
                            pass
                    time.sleep(0.2)
                    print("🔄 重新创建capture...")
                    self.cap = self.create_safe_capture(self.video_path)
                    if not self.cap or not self.cap.isOpened():
                        print("❌ 重新创建capture失败")
                        if was_playing:
                            self.is_playing = True
                        return False
                    print("✅ capture重新创建成功")
                    try:
                        if self.fps > 0:
                            time_seconds = frame_number / self.fps
                            print(f"🔄 使用精确帧跳转: 目标帧{frame_number}, 时间{time_seconds:.2f}秒")

                            # 先尝试直接帧跳转（更精确）
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                            actual_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))

                            # 如果帧跳转不准确，尝试时间跳转
                            frame_diff = abs(actual_frame - frame_number)
                            if frame_diff > 10:  # 如果误差超过10帧
                                print(f"⚠️ 帧跳转误差过大({frame_diff}帧)，尝试时间跳转")
                                self.cap.set(cv2.CAP_PROP_POS_MSEC, time_seconds * 1000)
                                actual_time = self.cap.get(cv2.CAP_PROP_POS_MSEC) / 1000
                                actual_frame = int(actual_time * self.fps)
                                print(f"🔄 时间跳转结果: 目标{time_seconds:.2f}s, 实际{actual_time:.2f}s")

                            print(f"✅ 最终跳转结果: 目标帧{frame_number}, 实际帧{actual_frame}, 误差{abs(actual_frame - frame_number)}帧")
                            self.current_frame = actual_frame

                            if was_playing:
                                self.is_playing = True
                                print("✅ 播放状态已恢复")
                                if not hasattr(self, 'video_thread') or not self.video_thread.is_alive():
                                    print("🔄 重新启动播放循环...")
                                    self.start_playback_loop()
                            return True
                        else:
                            print("❌ 无效的fps，无法计算时间位置")
                            if was_playing:
                                self.is_playing = True
                            return False
                    except Exception as e:
                        print(f"❌ 时间跳转失败: {e}")
                        try:
                            print("🔄 尝试备用帧跳转方法...")
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                            actual_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                            print(f"✅ 备用跳转成功: 目标{frame_number}, 实际{actual_frame}")
                            self.current_frame = actual_frame
                            if was_playing:
                                self.is_playing = True
                                print("✅ 播放状态已恢复（备用方法）")
                                if not hasattr(self, 'video_thread') or not self.video_thread.is_alive():
                                    print("🔄 重新启动播放循环（备用方法）...")
                                    self.start_playback_loop()
                            return True
                        except Exception as e2:
                            print(f"❌ 备用跳转也失败: {e2}")
                            if was_playing:
                                self.is_playing = True
                                print("✅ 播放状态已恢复（失败情况）")
                            return False
        except Exception as e:
            print(f"❌ 安全跳转完全失败: {e}")
            return False

    def video_playback_loop(self):
        """视频播放循环 - 永不停止版本"""
        consecutive_errors = 0
        max_consecutive_errors = 50
        recovery_attempts = 0
        max_recovery_attempts = 100
        force_continue = True
        print("🎬 启动永不停止播放循环")
        while self.is_playing and self.cap and force_continue:
            try:
                start_time = time.time()
                # 使用capture_lock保护VideoCapture操作，避免FFmpeg线程冲突
                with self.capture_lock:
                    with self.frame_read_lock:
                        ret, frame = self.cap.read()
                if not ret:
                    current_pos = 0
                    try:
                        current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                    except:
                        pass
                    if current_pos >= self.total_frames * 0.95:
                        print(f"✅ 视频正常播放完毕: {current_pos}/{self.total_frames}")
                        self.is_playing = False
                        self.root.after(0, lambda: self.play_button.configure(text="播放"))
                        break
                    else:
                        recovery_attempts += 1
                        print(f"💪 视频未播放完但读取失败，启动恢复 (第{recovery_attempts}次)")
                        print(f"   当前位置: {current_pos}/{self.total_frames} ({current_pos/self.total_frames*100:.1f}%)")
                        if recovery_attempts < max_recovery_attempts:
                            if recovery_attempts % 10 == 0:
                                print("🔄 重新创建capture进行恢复")
                                if self.recreate_capture_for_recovery():
                                    recovery_attempts = 0
                                    consecutive_errors = 0
                                    continue
                            else:
                                try:
                                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos + 1)
                                    time.sleep(0.1)
                                    continue
                                except:
                                    pass
                            time.sleep(0.2)
                            continue
                        else:
                            print(f"💪 恢复尝试{recovery_attempts}次，但永不放弃")
                            recovery_attempts = 0
                            time.sleep(1)
                            continue
                if frame is None or frame.size == 0:
                    print("💪 读取到无效帧，但永不停止")
                    consecutive_errors += 1
                    recovery_attempts += 1
                    if consecutive_errors >= max_consecutive_errors:
                        print(f"💪 连续错误{consecutive_errors}次，但强制继续")
                        consecutive_errors = 0
                        time.sleep(0.5)
                    continue
                consecutive_errors = 0
                recovery_attempts = 0
                try:
                    self.current_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                except:
                    self.current_frame += 1
                if (self.subtitle_generation_complete or self.subtitle_streaming_mode) and self.subtitle_segments:
                    current_time = self.current_frame / self.fps if self.fps > 0 else 0
                    # 字幕时间不需要调整，因为它们是基于视频时间的
                    self.update_current_subtitle(current_time)
                self.root.after(0, lambda f=frame: self.show_frame(f))
                elapsed = time.time() - start_time

                # 使用速度调整后的帧延迟
                if self.speed_adjusted_frame_delay is not None:
                    target_delay = self.speed_adjusted_frame_delay
                else:
                    target_delay = self.frame_delay

                sleep_time = max(0, target_delay - elapsed)
                time.sleep(sleep_time)
            except Exception as e:
                error_msg = str(e)
                print(f"💪 播放循环异常但永不停止: {error_msg}")
                consecutive_errors += 1
                recovery_attempts += 1
                if any(keyword in error_msg.lower() for keyword in ['pthread', 'async_lock', 'msmf']):
                    print("🚨 检测到严重错误，启动恢复")
                    if self.recreate_capture_for_recovery():
                        consecutive_errors = 0
                        recovery_attempts = 0
                        continue
                if consecutive_errors >= max_consecutive_errors:
                    if recovery_attempts < max_recovery_attempts:
                        print(f"💪 连续错误{consecutive_errors}次，但永不放弃")
                        consecutive_errors = 0
                        time.sleep(0.8)
                        continue
                    else:
                        print(f"💪 错误和恢复都过多，但仍然尝试继续")
                        consecutive_errors = 0
                        recovery_attempts = 0
                        time.sleep(2)
                        continue
                time.sleep(0.2)

    def recreate_capture_for_recovery(self):
        """重新创建capture进行恢复 - 线程安全版本"""
        # 使用双重锁保护，避免FFmpeg线程冲突
        with self.capture_lock:
            with self.video_lock:
                try:
                    print("🔄 重新创建capture...")
                    current_pos = 0
                    try:
                        current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                    except:
                        pass
                    if self.cap:
                        try:
                            self.cap.release()
                        except:
                            pass
                    time.sleep(0.5)
                    self.cap = self.create_safe_capture(self.video_path)
                    if self.cap and self.cap.isOpened():
                        try:
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos)
                        except:
                            pass
                        print(f"✅ capture重新创建成功，恢复到位置{current_pos}")
                        return True
                    else:
                        print("❌ capture重新创建失败")
                        return False
                except Exception as e:
                    print(f"❌ 重新创建capture异常: {e}")
                    return False

    def start_demo_subtitles(self):
        """启动演示字幕"""
        demo_subtitles = [
            "欢迎使用MP4播放器！",
            "这是一个功能完整的视频播放器",
            "支持实时字幕显示功能",
            "您可以调整字幕的大小和位置",
            "支持多种字幕颜色选择",
            "集成faster-whisper引擎可生成真实字幕",
            "当前显示的是演示字幕效果",
            "字幕会自动换行适应屏幕宽度",
            "背景半透明确保字幕清晰可见"
        ]

        def demo_loop():
            subtitle_index = 0
            total_subtitles = len(demo_subtitles)

            # 模拟字幕生成进度
            self.root.after(0, lambda: self.update_demo_progress(0, "开始生成演示字幕..."))
            time.sleep(0.5)

            while self.is_playing and self.subtitle_enabled.get():
                if subtitle_index < total_subtitles:
                    subtitle_text = demo_subtitles[subtitle_index]

                    # 更新进度
                    progress = ((subtitle_index + 1) / total_subtitles) * 100
                    status = f"显示字幕 ({subtitle_index + 1}/{total_subtitles})"

                    def update_ui(text=subtitle_text, p=progress, s=status):
                        self.update_subtitle(text)
                        self.update_demo_progress(p, s)

                    self.root.after(0, update_ui)
                    subtitle_index = (subtitle_index + 1) % total_subtitles

                time.sleep(3)  # 每3秒更换一次字幕

        if self.demo_subtitle_thread is None or not self.demo_subtitle_thread.is_alive():
            self.demo_subtitle_thread = threading.Thread(target=demo_loop, daemon=True)
            self.demo_subtitle_thread.start()

    def update_demo_progress(self, progress, status_text):
        """更新演示进度"""
        self.subtitle_progress_var.set(progress)
        self.progress_text_var.set(status_text)

    def update_subtitle(self, text):
        """更新字幕文本"""
        self.subtitle_text = text

    def update_subtitle_font_size(self, value):
        """更新字幕字体大小"""
        self.subtitle_font_size = int(float(value))

    def update_subtitle_position_y(self, value):
        """更新字幕垂直位置"""
        self.subtitle_position_y = float(value)

    def update_subtitle_position_x(self, value):
        """更新字幕水平位置"""
        self.subtitle_position_x = float(value)

    def update_subtitle_color(self, _event=None):
        """更新字幕颜色"""
        color_map = {
            "黑色": (0, 0, 0),
            "白色": (255, 255, 255),
            "黄色": (255, 255, 0),
            "红色": (255, 0, 0),
            "绿色": (0, 255, 0),
            "蓝色": (0, 0, 255)
        }
        self.subtitle_color = color_map.get(self.color_var.get(), (0, 0, 0))

    def update_playback_speed(self, value):
        """更新播放速度"""
        self.playback_speed = float(value)
        self.speed_label.configure(text=f"{self.playback_speed:.2f}x")

        # 更新帧延迟
        if self.fps > 0:
            self.speed_adjusted_frame_delay = (1.0 / self.fps) / self.playback_speed

        print(f"🎬 播放速度调整为: {self.playback_speed:.2f}x")

        # 如果正在播放，需要调整音频播放速度
        if self.is_playing and self.audio_enabled_var.get():
            self.adjust_audio_speed()

    def adjust_audio_speed(self):
        """调整音频播放速度以匹配视频"""
        # 注意：pygame.mixer不支持变速播放
        # 这里我们只提示用户，不进行实际操作
        if self.playback_speed != 1.0:
            print(f"⚠️ 音频播放器不支持变速播放，当前速度: {self.playback_speed:.2f}x")
            # pygame不支持变速播放，只能保持原速度



    def on_subtitle_drag_start(self, event):
        """开始拖动字幕"""
        if self.subtitle_draggable:
            self.subtitle_drag_start = (event.x, event.y)
            print(f"🎬 开始拖动字幕: ({event.x}, {event.y})")

    def on_subtitle_drag_motion(self, event):
        """拖动字幕过程中"""
        if self.subtitle_drag_start and self.subtitle_draggable:
            # 获取视频显示区域大小
            widget_width = self.video_label.winfo_width()
            widget_height = self.video_label.winfo_height()

            if widget_width > 0 and widget_height > 0:
                # 直接根据鼠标位置计算相对位置
                new_x = event.x / widget_width
                new_y = event.y / widget_height

                # 限制在合理范围内
                new_x = max(0.1, min(0.9, new_x))
                new_y = max(0.1, min(0.9, new_y))

                # 更新字幕位置
                self.subtitle_position_x = new_x
                self.subtitle_position_y = new_y

                # 更新界面控件
                self.position_x_var.set(new_x)
                self.position_y_var.set(new_y)

                print(f"🎬 拖动字幕到: ({new_x:.2f}, {new_y:.2f})")

    def on_subtitle_drag_end(self, event):
        """结束拖动字幕"""
        if self.subtitle_drag_start:
            # 最终位置确认
            widget_width = self.video_label.winfo_width()
            widget_height = self.video_label.winfo_height()

            if widget_width > 0 and widget_height > 0:
                final_x = max(0.1, min(0.9, event.x / widget_width))
                final_y = max(0.1, min(0.9, event.y / widget_height))

                self.subtitle_position_x = final_x
                self.subtitle_position_y = final_y
                self.position_x_var.set(final_x)
                self.position_y_var.set(final_y)

                print(f"🎬 字幕拖动完成: ({final_x:.2f}, {final_y:.2f})")

            self.subtitle_drag_start = None

    def on_window_configure(self, event):
        """窗口大小变化事件处理"""
        # 只处理主窗口的配置变化事件
        if event.widget == self.root:
            # 获取当前窗口状态
            window_state = self.root.state()
            window_width = self.root.winfo_width()
            window_height = self.root.winfo_height()

            # 检测窗口是否被最大化
            if window_state == 'zoomed' or (window_width > 1200 and window_height > 800):
                # 窗口最大化时，延迟刷新视频画面
                if hasattr(self, 'resize_timer'):
                    self.root.after_cancel(self.resize_timer)
                self.resize_timer = self.root.after(200, self.refresh_video_for_resize)

    def refresh_video_for_resize(self):
        """窗口大小变化后刷新视频显示"""
        if self.cap and hasattr(self, 'current_frame'):
            print(f"🖥️ 窗口大小变化，刷新视频显示")
            # 强制刷新当前帧显示
            self.show_frame()

    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def update_sync_status(self):
        """更新同步状态显示"""
        try:
            if not hasattr(self, 'sync_status_var'):
                return

            if not self.is_playing:
                self.sync_status_var.set("暂停中")
                return

            # 检查音频播放状态
            audio_playing = self.audio_player.is_audio_playing()

            if not self.audio_enabled_var.get():
                status = "音频已禁用"
                color = "gray"
            elif not self.audio_file_path:
                status = "无音频文件"
                color = "red"
            elif audio_playing:
                # 使用正确的同步计算方法
                current_time = time.time()

                # 计算当前视频位置
                if hasattr(self, 'video_start_real_time') and self.video_start_real_time:
                    video_elapsed = current_time - self.video_start_real_time
                    video_position = self.video_start_position + video_elapsed
                else:
                    video_position = self.current_frame / self.fps if self.fps > 0 else 0

                # 计算当前音频位置
                if hasattr(self, 'audio_start_real_time') and self.audio_start_real_time:
                    audio_elapsed = current_time - self.audio_start_real_time
                    audio_position = self.audio_start_position + audio_elapsed

                    # 应用用户设置的偏移
                    offset_seconds = self.sync_offset / 1000.0
                    adjusted_audio_position = audio_position + offset_seconds

                    # 计算同步差异
                    sync_diff = abs(video_position - adjusted_audio_position)

                    if sync_diff < 0.1:  # 100ms以内认为同步
                        status = f"✅ 同步良好 (差异:{sync_diff*1000:.0f}ms, 偏移:{self.sync_offset:.0f}ms)"
                        color = "green"
                    elif sync_diff < 0.5:  # 500ms以内认为可接受
                        status = f"⚠️ 轻微不同步 (差异:{sync_diff*1000:.0f}ms, 偏移:{self.sync_offset:.0f}ms)"
                        color = "orange"
                    else:
                        status = f"❌ 严重不同步 (差异:{sync_diff*1000:.0f}ms, 偏移:{self.sync_offset:.0f}ms)"
                        color = "red"
                else:
                    status = f"🔄 音频播放中 (偏移:{self.sync_offset:.0f}ms)"
                    color = "blue"
            else:
                status = "音频未播放"
                color = "red"

            # 只在内部变量中保存状态，不更新界面（因为界面已简化）
            self.sync_status_var.set(status)
            # 移除了sync_status_label的引用，因为界面已简化

        except Exception as e:
            # 静默处理错误，避免日志刷屏
            pass

    def extract_audio_for_playback(self, video_path):
        """提取音频文件用于播放（OGG格式，优化速度）"""
        def extract():
            try:
                # 禁用播放按钮
                self.root.after(0, lambda: self.play_button.configure(state="disabled", text="提取音频中..."))
                self.audio_extraction_complete = False

                print("🔊 开始快速提取音频用于播放...")

                # 创建临时音频文件（OGG格式，支持pygame位置播放）
                with tempfile.NamedTemporaryFile(suffix=".ogg", delete=False) as temp_audio:
                    self.audio_file_path = temp_audio.name

                print(f"🔊 临时音频文件路径: {self.audio_file_path}")

                # 使用moviepy提取音频
                video_clip = VideoFileClip(video_path)
                audio_clip = video_clip.audio

                if audio_clip is not None:
                    print("🔊 检测到音频轨道，开始快速提取...")

                    # 更新按钮状态
                    self.root.after(0, lambda: self.play_button.configure(text="正在提取音频..."))

                    # 提取音频，使用OGG格式（优化速度和质量）
                    audio_clip.write_audiofile(
                        self.audio_file_path,
                        verbose=False,
                        logger=None,
                        codec='libvorbis',  # OGG格式
                        ffmpeg_params=[
                            '-ar', '44100',     # 采样率44.1kHz
                            '-ac', '2',         # 立体声
                            '-q:a', '3',        # 音质等级3（较快，质量好）
                            '-threads', '0',    # 使用所有可用线程
                            '-compression_level', '4'  # 压缩等级4（平衡速度和大小）
                        ]
                    )

                    # 检查生成的文件
                    if os.path.exists(self.audio_file_path):
                        file_size = os.path.getsize(self.audio_file_path)
                        print(f"✅ 音频提取完成，文件大小: {file_size} 字节")

                        # 标记音频提取完成并启用播放按钮
                        self.audio_extraction_complete = True
                        self.root.after(0, lambda: self.play_button.configure(state="normal", text="播放"))
                        self.root.after(0, lambda: print("✅ 播放按钮已启用，可以开始播放"))

                        # 自动开始字幕生成
                        self.root.after(0, self.auto_generate_subtitles_after_load)

                        # 如果正在播放，尝试播放音频
                        if self.is_playing and self.audio_enabled_var.get():
                            self.root.after(0, self.play_audio)
                    else:
                        print("❌ 音频文件生成失败")
                        self.audio_file_path = None

                        # 即使音频提取失败也启用播放按钮（可以无声播放）
                        self.audio_extraction_complete = False
                        self.root.after(0, lambda: self.play_button.configure(state="normal", text="播放"))
                        self.root.after(0, lambda: print("⚠️ 音频提取失败，播放按钮已启用（无声播放）"))
                else:
                    print("⚠️ 视频文件没有音频轨道")
                    self.audio_file_path = None

                    # 即使没有音频也启用播放按钮（视频可以无声播放）
                    self.audio_extraction_complete = False
                    self.root.after(0, lambda: self.play_button.configure(state="normal", text="播放"))
                    self.root.after(0, lambda: print("✅ 无音频轨道，播放按钮已启用（无声播放）"))

                    # 在界面上显示提示
                    self.root.after(0, lambda: self.progress_text_var.set("视频没有音频轨道"))
                    self.root.after(0, lambda: messagebox.showinfo(
                        "提示",
                        "当前视频文件没有音频轨道，无法播放声音。\n\n"
                        "视频可以正常播放，但没有声音。\n"
                        "请选择包含音频的视频文件来测试音频播放功能。"
                    ))

                # 关闭文件
                video_clip.close()
                if audio_clip:
                    audio_clip.close()

            except Exception as e:
                print(f"❌ 音频提取失败: {e}")
                import traceback
                traceback.print_exc()
                self.audio_file_path = None

                # 提取失败也启用播放按钮（可以无声播放）
                self.audio_extraction_complete = False
                self.root.after(0, lambda: self.play_button.configure(state="normal", text="播放"))
                self.root.after(0, lambda: print("⚠️ 音频提取失败，播放按钮已启用（无声播放）"))

        # 在后台线程中提取音频
        threading.Thread(target=extract, daemon=True).start()

    def create_and_play_audio_segment_async(self, start_seconds):
        """异步创建并播放从指定位置开始的音频片段"""
        def create_segment():
            try:
                print(f"🔊 异步创建从{start_seconds:.2f}秒开始的音频片段...")

                if not MOVIEPY_AVAILABLE:
                    print("❌ moviepy不可用，无法创建音频片段")
                    return

                # 使用moviepy创建音频片段
                from moviepy.editor import AudioFileClip

                # 加载音频文件
                audio_clip = AudioFileClip(self.audio_file_path)

                # 检查跳转位置是否有效
                if start_seconds >= audio_clip.duration:
                    print(f"⚠️ 跳转位置超出音频长度: {start_seconds:.2f}s >= {audio_clip.duration:.2f}s")
                    audio_clip.close()
                    return

                # 创建从指定位置开始的片段（播放剩余部分）
                audio_segment = audio_clip.subclip(start_seconds)

                # 创建临时文件（WAV格式，更快）
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    temp_audio_path = temp_file.name

                # 导出音频片段（WAV格式，更快）
                audio_segment.write_audiofile(
                    temp_audio_path,
                    verbose=False,
                    logger=None,
                    codec='pcm_s16le',
                    ffmpeg_params=['-ar', '44100', '-ac', '2']
                )

                # 关闭clips
                audio_segment.close()
                audio_clip.close()

                # 在主线程中播放音频片段
                def play_segment():
                    if self.is_playing and PYGAME_AVAILABLE:
                        try:
                            pygame.mixer.music.stop()
                            pygame.mixer.music.load(temp_audio_path)
                            pygame.mixer.music.set_volume(1.0)
                            pygame.mixer.music.play()

                            # 保存临时文件路径，稍后清理
                            if hasattr(self, 'temp_audio_files'):
                                self.temp_audio_files.append(temp_audio_path)
                            else:
                                self.temp_audio_files = [temp_audio_path]

                            print(f"✅ 音频片段播放成功")

                        except Exception as e:
                            print(f"❌ 音频片段播放失败: {e}")
                            # 清理临时文件
                            try:
                                os.unlink(temp_audio_path)
                            except:
                                pass
                    else:
                        # 清理临时文件
                        try:
                            os.unlink(temp_audio_path)
                        except:
                            pass

                # 在主线程中执行播放
                self.root.after(0, play_segment)

            except Exception as e:
                print(f"❌ 创建音频片段失败: {e}")
                import traceback
                traceback.print_exc()

        # 在后台线程中执行
        threading.Thread(target=create_segment, daemon=True).start()

    def play_audio(self):
        """播放音频（兼容性函数）"""
        self.play_audio_sync()

    def pause_audio(self):
        """暂停音频并记录当前位置"""
        if not PYGAME_AVAILABLE:
            return

        try:
            # 计算当前音频播放位置
            if hasattr(self, 'audio_start_real_time') and self.audio_start_real_time:
                current_time = time.time()
                audio_elapsed = current_time - self.audio_start_real_time
                self.audio_paused_position = self.audio_start_position + audio_elapsed
                print(f"🔊 音频暂停，记录位置: {self.audio_paused_position:.2f}秒")
            else:
                # 如果没有时间基准，计算视频位置作为音频位置
                self.audio_paused_position = self.current_frame / self.fps if self.fps > 0 else 0
                print(f"🔊 音频暂停，使用视频位置: {self.audio_paused_position:.2f}秒")

            # 停止音频播放
            pygame.mixer.music.stop()
            print("🔊 音频已暂停")
        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.stop()
            self.audio_start_time = None
            self.audio_paused_position = 0
            print("🔊 音频已停止")
        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def resume_audio(self):
        """恢复音频播放（兼容性函数）"""
        self.resume_audio_sync()

    def seek_audio(self, position_seconds):
        """音频跳转（兼容性函数）"""
        self.seek_audio_sync(position_seconds)

    def play_audio_sync(self, start_position_seconds=0):
        """使用pygame位置播放的音频同步"""
        if not AUDIO_AVAILABLE or not self.audio_file_path:
            print("🔊 音频播放条件不满足")
            return

        try:
            print(f"🔊 pygame位置播放音频，位置: {start_position_seconds:.2f}秒")
            print(f"🔊 音频文件: {self.audio_file_path}")

            if not os.path.exists(self.audio_file_path):
                print("❌ 音频文件不存在")
                return

            # 加载音频文件
            if not self.audio_player.load_audio(self.audio_file_path):
                print("❌ 加载音频文件失败")
                return

            # 使用pygame位置播放
            if self.audio_player.play(start_position_seconds):
                print(f"✅ pygame位置播放成功")

                # 设置简化的同步基准
                current_time = time.time()
                with self.sync_lock:
                    # pygame.play(start=pos)真正从指定位置开始播放
                    self.audio_start_real_time = current_time
                    self.audio_start_position = start_position_seconds

                print(f"🔊 同步基准设置: 开始时间{self.audio_start_real_time:.3f}, 位置{self.audio_start_position:.2f}s")
            else:
                print("❌ pygame位置播放失败")

        except Exception as e:
            print(f"❌ 音频播放失败: {e}")
            import traceback
            traceback.print_exc()

    def resume_audio_sync(self):
        """恢复同步音频播放 - 使用pygame start参数从暂停位置继续"""
        if not PYGAME_AVAILABLE or not self.audio_file_path:
            return

        try:
            # 获取当前视频位置
            current_video_position = self.current_frame / self.fps if self.fps > 0 else 0

            # 使用暂停时记录的音频位置，如果没有则使用视频位置
            resume_position = getattr(self, 'audio_paused_position', current_video_position)

            print(f"🔊 恢复音频播放，从位置: {resume_position:.2f}秒继续")
            print(f"   视频位置: {current_video_position:.2f}秒")
            print(f"   音频恢复位置: {resume_position:.2f}秒")

            # 使用pygame start参数从暂停位置继续播放
            if not self.audio_player.load_audio(self.audio_file_path):
                print("❌ 加载音频文件失败")
                return

            if self.audio_player.play(resume_position):
                print("✅ 音频恢复播放成功")

                # 设置正确的同步基准
                current_time = time.time()
                with self.sync_lock:
                    # 视频的时间基准
                    self.video_start_real_time = current_time
                    self.video_start_position = current_video_position

                    # 音频的时间基准
                    self.audio_start_real_time = current_time
                    self.audio_start_position = resume_position

                print(f"🔊 同步基准重新设置完成")
                print(f"   视频基准: 时间{self.video_start_real_time:.3f}, 位置{self.video_start_position:.2f}s")
                print(f"   音频基准: 时间{self.audio_start_real_time:.3f}, 位置{self.audio_start_position:.2f}s")
            else:
                print("❌ 音频恢复播放失败")

            # 清除暂停位置记录
            self.audio_paused_position = 0

        except Exception as e:
            print(f"❌ 同步音频恢复失败: {e}")
            import traceback
            traceback.print_exc()

    def seek_audio_sync(self, position_seconds):
        """正确的音频同步跳转"""
        if not AUDIO_AVAILABLE or not self.audio_file_path:
            return

        try:
            # 获取用户选择的跳转模式
            seek_mode = getattr(self, 'seek_mode_var', None)
            mode = seek_mode.get() if seek_mode else "位置播放"

            print(f"🔊 正确音频跳转到: {position_seconds:.2f}秒 (模式: {mode})")

            # 如果视频暂停，只记录位置
            if not self.is_playing:
                self.audio_paused_position = position_seconds
                print(f"🔊 视频暂停中，记录音频位置: {position_seconds:.2f}秒")
                return

            # 根据模式选择跳转方式
            if mode == "禁用音频跳转":
                print("🔊 音频跳转已禁用")
                return
            else:
                print("🔊 使用正确的位置播放同步")

                # 先更新视频的时间基准（视频已经跳转完成）
                current_time = time.time()
                with self.sync_lock:
                    self.video_start_real_time = current_time
                    self.video_start_position = position_seconds

                # 然后播放音频并设置音频时间基准
                self.play_audio_sync(position_seconds)

        except Exception as e:
            print(f"❌ 音频跳转失败: {e}")
            import traceback
            traceback.print_exc()

    def simple_audio_restart_with_sync(self, video_position_seconds):
        """简单的音频重新播放，带有基本同步"""
        try:
            print(f"🔊 简单重播音频，视频位置: {video_position_seconds:.2f}秒")

            # 停止当前音频
            pygame.mixer.music.stop()

            # 重新加载并播放音频
            pygame.mixer.music.load(self.audio_file_path)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()

            # 设置同步基准 - 关键在这里！
            current_time = time.time()
            with self.sync_lock:
                # 音频从现在开始播放（从0秒开始）
                self.audio_start_time = current_time

                # 视频的虚拟开始时间 = 现在 - 视频当前位置
                # 这样计算出的视频时间就是video_position_seconds
                self.video_start_time = current_time - video_position_seconds

                # 应用用户设置的同步偏移
                audio_offset_seconds = self.sync_offset / 1000.0
                self.audio_start_time += audio_offset_seconds

            print(f"✅ 音频重播完成")
            print(f"   音频开始时间: {self.audio_start_time:.2f}")
            print(f"   视频虚拟开始时间: {self.video_start_time:.2f}")
            print(f"   同步偏移: {self.sync_offset:.0f}ms")

        except Exception as e:
            print(f"❌ 音频重播失败: {e}")
            import traceback
            traceback.print_exc()

    def smart_audio_sync(self, target_position_seconds):
        """智能音频同步 - 根据跳转距离选择策略"""
        try:
            print(f"🔊 智能音频同步到: {target_position_seconds:.2f}秒")

            # 策略1：小跳转（<10秒）- 重新播放音频，用户可以接受短暂的不同步
            if target_position_seconds < 10.0:
                print("🔊 小跳转：重新播放音频")
                pygame.mixer.music.stop()
                pygame.mixer.music.load(self.audio_file_path)
                pygame.mixer.music.set_volume(1.0)
                pygame.mixer.music.play()

                current_time = time.time()
                with self.sync_lock:
                    self.audio_start_time = current_time
                    self.video_start_time = current_time - target_position_seconds

                print(f"✅ 小跳转完成，音频从头播放")
                return

            # 策略2：中等跳转（10-60秒）- 暂停音频，显示提示
            elif target_position_seconds < 60.0:
                print("🔊 中等跳转：暂停音频播放")
                pygame.mixer.music.stop()
                print(f"✅ 音频已暂停（跳转距离: {target_position_seconds:.1f}秒）")
                return

            # 策略3：大跳转（>60秒）- 暂停音频，显示提示
            else:
                print("🔊 大跳转：暂停音频播放")
                pygame.mixer.music.stop()
                print(f"✅ 音频已暂停（大跳转: {target_position_seconds:.1f}秒）")
                return

        except Exception as e:
            print(f"❌ 智能音频同步失败: {e}")
            import traceback
            traceback.print_exc()

    def adjust_sync_offset(self, value):
        """调整同步偏移"""
        self.sync_offset = float(value)
        self.sync_offset_label.configure(text=f"{self.sync_offset:.0f}ms")
        print(f"🔧 同步偏移调整为: {self.sync_offset:.0f}ms")

        if self.is_playing:
            # 实时调整音频同步
            with self.sync_lock:
                if self.video_start_time:
                    current_time = time.time()
                    video_elapsed = current_time - self.video_start_time
                    audio_offset_seconds = self.sync_offset / 1000.0
                    self.audio_start_time = current_time - video_elapsed + audio_offset_seconds

    def quick_sync_adjust(self, offset_ms):
        """快速调整同步偏移"""
        current_offset = self.sync_offset_var.get()
        new_offset = current_offset + offset_ms
        new_offset = max(-2000, min(2000, new_offset))  # 限制范围
        self.sync_offset_var.set(new_offset)
        self.adjust_sync_offset(new_offset)
        print(f"🔧 快速调整同步偏移: {offset_ms:+d}ms, 新值: {new_offset:.0f}ms")

    def reset_sync(self):
        """重置同步"""
        self.sync_offset_var.set(0)
        self.adjust_sync_offset(0)
        print("🔄 同步已重置")

    def restart_audio_from_current_position(self):
        """从当前视频位置重新开始音频播放"""
        if not PYGAME_AVAILABLE or not self.audio_file_path:
            messagebox.showwarning("警告", "音频播放功能不可用")
            return

        if not self.is_playing:
            messagebox.showinfo("提示", "请先播放视频")
            return

        try:
            current_video_time = self.current_frame / self.fps if self.fps > 0 else 0
            print(f"🔄 从当前位置重新开始音频: {current_video_time:.2f}秒")

            # 停止当前音频
            pygame.mixer.music.stop()

            # 重新播放音频
            pygame.mixer.music.load(self.audio_file_path)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()

            # 重新设置同步基准
            current_time = time.time()
            with self.sync_lock:
                self.audio_start_time = current_time
                self.video_start_time = current_time - current_video_time

                # 应用同步偏移
                audio_offset_seconds = self.sync_offset / 1000.0
                self.audio_start_time += audio_offset_seconds

            print(f"✅ 音频重新开始完成")
            messagebox.showinfo("音频重启", f"✅ 音频已从当前位置重新开始\n\n视频位置: {current_video_time:.1f}秒\n音频: 从头播放\n偏移: {self.sync_offset:.0f}ms\n\n注意：音频会从头播放，可能需要几秒钟才能同步")

        except Exception as e:
            print(f"❌ 重新开始音频失败: {e}")
            messagebox.showerror("错误", f"重新开始音频失败: {e}")

    def save_subtitles(self):
        """保存字幕到SRT文件"""
        if not self.subtitle_segments:
            messagebox.showwarning("警告", "没有可保存的字幕，请先生成字幕")
            return

        try:
            # 选择保存位置
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                title="保存字幕文件",
                defaultextension=".srt",
                filetypes=[
                    ("SRT字幕文件", "*.srt"),
                    ("所有文件", "*.*")
                ]
            )

            if not file_path:
                return

            # 生成SRT格式字幕
            srt_content = ""
            for i, segment in enumerate(self.subtitle_segments, 1):
                start_time = self.seconds_to_srt_time(segment['start'])
                end_time = self.seconds_to_srt_time(segment['end'])
                text = segment['text']

                srt_content += f"{i}\n"
                srt_content += f"{start_time} --> {end_time}\n"
                srt_content += f"{text}\n\n"

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)

            messagebox.showinfo("成功", f"✅ 字幕已保存到:\n{file_path}\n\n共保存 {len(self.subtitle_segments)} 个字幕片段")
            print(f"✅ 字幕已保存到: {file_path}")

        except Exception as e:
            print(f"❌ 保存字幕失败: {e}")
            messagebox.showerror("错误", f"保存字幕失败: {e}")

    def seconds_to_srt_time(self, seconds):
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    def test_subtitle_display(self):
        """测试字幕显示"""
        test_text = "这是测试字幕 - Test Subtitle Display"
        self.update_subtitle(test_text)
        print(f"🧪 测试字幕显示: {test_text}")

    def test_audio_playback(self):
        """测试音频播放功能"""
        if not AUDIO_AVAILABLE:
            messagebox.showwarning("警告", "音频播放功能不可用")
            return

        if not self.audio_file_path or not os.path.exists(self.audio_file_path):
            messagebox.showwarning("警告", "请先加载包含音频的视频文件")
            return

        try:
            print("🔊 测试音频播放...")
            print(f"🔊 音频文件: {self.audio_file_path}")
            print(f"🔊 文件大小: {os.path.getsize(self.audio_file_path)} 字节")

            if PYGAME_AVAILABLE:
                # 检查pygame状态
                print(f"🔊 pygame mixer状态: {pygame.mixer.get_init()}")
                print(f"🔊 pygame mixer忙碌状态: {pygame.mixer.music.get_busy()}")

                # 停止当前播放
                pygame.mixer.music.stop()

                # 加载并播放
                pygame.mixer.music.load(self.audio_file_path)
                pygame.mixer.music.set_volume(1.0)
                print("🔊 开始播放音频...")
                pygame.mixer.music.play()

                # 检查播放状态
                import time
                time.sleep(0.1)  # 等待一下
                is_playing = pygame.mixer.music.get_busy()
                print(f"🔊 播放状态: {is_playing}")

                if is_playing:
                    messagebox.showinfo("测试", "✅ 音频播放测试已开始\n如果听不到声音，请检查：\n1. 系统音量设置\n2. 音频设备连接\n3. 音频驱动程序")
                else:
                    messagebox.showwarning("警告", "❌ 音频播放失败\n音频文件可能有问题或格式不支持")

            elif WINSOUND_AVAILABLE:
                print("🔊 使用winsound播放...")
                winsound.PlaySound(self.audio_file_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
                messagebox.showinfo("测试", "✅ 音频播放测试已开始（winsound）\n如果听不到声音，请检查系统音量设置")

        except Exception as e:
            print(f"❌ 音频播放测试失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"音频播放测试失败: {e}\n\n请检查音频文件格式是否支持")

    def force_play_audio(self):
        """强制播放音频 - 使用最简单的方法"""
        if not PYGAME_AVAILABLE:
            messagebox.showwarning("警告", "pygame不可用")
            return

        if not self.audio_file_path or not os.path.exists(self.audio_file_path):
            messagebox.showwarning("警告", "没有可用的音频文件")
            return

        try:
            print("🔊 强制播放音频（最简单方法）")
            print(f"🔊 音频文件: {self.audio_file_path}")

            # 完全重新初始化pygame mixer
            pygame.mixer.quit()
            pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=1024)

            # 停止所有音频
            pygame.mixer.stop()
            pygame.mixer.music.stop()

            # 加载并播放
            pygame.mixer.music.load(self.audio_file_path)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()

            print("✅ 强制音频播放开始")
            messagebox.showinfo("强制播放", "✅ 强制音频播放已开始\n\n如果仍然听不到声音，可能是：\n1. 系统音量被静音\n2. 音频设备问题\n3. 音频文件损坏")

        except Exception as e:
            print(f"❌ 强制音频播放失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"强制音频播放失败: {e}")

    def test_audio_sync(self):
        """测试音频同步"""
        if self.is_playing:
            print("🔄 测试音频同步 - 重新同步")
            # 重新设置同步基准
            with self.sync_lock:
                current_time = time.time()
                self.video_start_time = current_time
                video_offset = self.current_frame / self.fps if self.fps > 0 else 0
                audio_offset_seconds = self.sync_offset / 1000.0
                self.audio_start_time = current_time - video_offset + audio_offset_seconds

            # 重新开始音频
            self.stop_audio()
            self.play_audio_sync()
            print("✅ 音频同步测试完成")
        else:
            print("⚠️ 视频未播放，无法测试同步")
            messagebox.showinfo("提示", "请先播放视频再测试同步")

    def test_system_audio(self):
        """测试系统音频功能"""
        try:
            print("🔔 测试系统音频...")

            if WINSOUND_AVAILABLE:
                # 播放Windows系统声音
                winsound.PlaySound("SystemAsterisk", winsound.SND_ALIAS)
                # 只在控制台输出测试结果，不显示弹框
                print("✅ 系统音频测试完成 - 如果听到了系统提示音，说明音频设备工作正常")
            else:
                print("⚠️ winsound不可用，无法测试系统音频")
                messagebox.showwarning("警告", "winsound不可用，无法测试系统音频")

        except Exception as e:
            print(f"❌ 系统音频测试失败: {e}")
            messagebox.showerror("错误", f"系统音频测试失败: {e}\n\n这可能表示音频系统有问题")

    def show_sync_info(self):
        """显示详细的同步信息"""
        if not self.cap:
            messagebox.showinfo("同步信息", "请先加载视频文件")
            return

        # 收集同步信息
        current_time = self.current_frame / self.fps if self.fps > 0 else 0
        total_time = self.total_frames / self.fps if self.fps > 0 else 0

        info = f"""🔍 详细同步信息:

📹 视频信息:
   • 当前帧: {self.current_frame} / {self.total_frames}
   • 当前时间: {current_time:.2f}s / {total_time:.2f}s
   • 帧率: {self.fps} fps
   • 播放状态: {'播放中' if self.is_playing else '暂停'}

🔊 音频信息:
   • 音频文件: {'存在' if self.audio_file_path and os.path.exists(self.audio_file_path) else '不存在'}
   • 音频启用: {'是' if self.audio_enabled_var.get() else '否'}
   • pygame状态: {'可用' if PYGAME_AVAILABLE else '不可用'}
   • 音频播放: {'播放中' if PYGAME_AVAILABLE and pygame.mixer.music.get_busy() else '未播放'}

⚙️ 同步设置:
   • 跳转模式: {getattr(self, 'seek_mode_var', tk.StringVar(value='未设置')).get()}
   • 同步偏移: {self.sync_offset:.0f}ms
   • moviepy可用: {'是' if MOVIEPY_AVAILABLE else '否'}

🎯 建议:
   • 如果音频不同步，尝试调整同步偏移
   • 使用"精确跳转"模式获得最佳同步效果
   • 检查音频文件是否正确提取"""

        messagebox.showinfo("同步信息", info)

    def load_whisper_model(self, model_size=None):
        """加载Whisper模型"""
        if not WHISPER_AVAILABLE:
            messagebox.showerror("错误", "faster-whisper不可用，无法加载模型")
            return False

        # 如果没有指定模型，让用户选择
        if model_size is None:
            model_choice = messagebox.askyesnocancel(
                "选择Whisper模型",
                "选择要使用的Whisper模型：\n\n"
                "【是】- large-v3 (最高质量，但较慢，需要更多内存)\n"
                "【否】- base (平衡速度和质量，推荐)\n"
                "【取消】- 取消操作\n\n"
                "注意：large-v3模型首次下载约3GB"
            )

            if model_choice is None:  # 取消
                return False
            elif model_choice:  # large-v3
                model_size = "large-v3"
            else:  # base
                model_size = "base"

        try:
            print(f"🤖 开始加载Whisper {model_size} 模型...")
            self.progress_text_var.set(f"正在加载Whisper {model_size} 模型...")

            # 在后台线程中加载模型
            def load_model():
                try:
                    from faster_whisper import WhisperModel

                    print(f"🤖 加载{model_size}模型...")
                    if model_size == "large-v3":
                        self.root.after(0, lambda: self.progress_text_var.set("下载large-v3模型中...（约3GB，请耐心等待）"))
                        print("📥 large-v3模型较大，首次下载需要时间...")

                    # 根据GPU可用性选择设备和计算类型
                    device = self.preferred_device if hasattr(self, 'preferred_device') else "cpu"

                    if device == "cuda" and self.gpu_available:
                        compute_type = "float16"  # GPU使用float16
                        print(f"🚀 使用GPU加速: {device}, 计算类型: {compute_type}")
                    else:
                        device = "cpu"
                        compute_type = "int8"  # CPU使用int8量化
                        print(f"🖥️ 使用CPU: {device}, 计算类型: {compute_type}")

                    # 加载模型
                    self.whisper_model = WhisperModel(
                        model_size,
                        device=device,
                        compute_type=compute_type
                    )

                    self.current_model_size = model_size
                    self.root.after(0, lambda: self.progress_text_var.set(f"Whisper {model_size} 模型加载完成"))

                    # 只在控制台输出成功信息，不显示弹框
                    if model_size == "large-v3":
                        print(f"✅ Whisper large-v3 模型加载完成 - 最高质量模型，可生成高质量字幕")
                    else:
                        print(f"✅ Whisper {model_size} 模型加载完成 - 可生成字幕")

                except Exception as e:
                    error_msg = f"{model_size} 模型加载失败: {e}"
                    print(f"❌ {error_msg}")
                    self.root.after(0, lambda: self.progress_text_var.set(error_msg))

                    if "large-v3" in str(e).lower() or "memory" in str(e).lower():
                        self.root.after(0, lambda: messagebox.showerror("错误", f"❌ {error_msg}\n\n建议：\n1. 检查网络连接\n2. 确保有足够的内存空间\n3. 或选择较小的模型（如base）"))
                    else:
                        self.root.after(0, lambda: messagebox.showerror("错误", f"❌ {error_msg}\n\n请检查网络连接或稍后重试。"))

            # 在后台线程中执行
            threading.Thread(target=load_model, daemon=True).start()
            return True

        except Exception as e:
            print(f"❌ 加载Whisper模型失败: {e}")
            messagebox.showerror("错误", f"加载Whisper模型失败: {e}")
            return False

    def generate_subtitles(self):
        """生成实时字幕"""
        if not WHISPER_AVAILABLE:
            messagebox.showerror("错误", "faster-whisper不可用，无法生成字幕")
            return

        if not self.whisper_model:
            # 如果模型未加载，先加载模型
            result = messagebox.askyesno("模型未加载", "需要先加载Whisper模型才能生成字幕。\n\n是否现在加载模型？")
            if result:
                self.load_whisper_model()
            return

        if not self.audio_file_path or not os.path.exists(self.audio_file_path):
            messagebox.showwarning("警告", "请先加载包含音频的视频文件")
            return

        # 询问字幕生成模式
        mode = messagebox.askyesnocancel(
            "字幕生成模式",
            "选择字幕生成模式：\n\n"
            "【是】- 实时流式生成（边播放边生成）\n"
            "【否】- 完整生成（一次性生成全部字幕）\n"
            "【取消】- 取消操作"
        )

        if mode is None:  # 取消
            return
        elif mode:  # 实时流式生成
            self.start_streaming_subtitle_generation()
        else:  # 完整生成
            self.start_complete_subtitle_generation()

    def start_streaming_subtitle_generation(self):
        """开始真正的流式字幕生成"""
        try:
            print("🎬 开始真正的流式字幕生成...")
            self.subtitle_streaming_mode = True
            self.subtitle_generation_complete = False
            self.subtitle_segments = []
            self.current_subtitle_index = 0
            self.streaming_segments_buffer = []  # 流式缓冲区

            # 更新按钮状态
            self.generate_subtitle_btn.configure(state="disabled")
            self.stop_generation_btn.configure(state="normal")
            self.progress_text_var.set("流式字幕生成中...")

            def streaming_generate():
                try:
                    print("🎤 开始真正的流式语音识别...")

                    # 根据模型类型优化参数
                    model_size = getattr(self, 'current_model_size', 'base')

                    # 流式处理参数
                    if model_size == "large-v3":
                        print("🎤 使用large-v3模型流式参数...")
                        transcribe_params = {
                            "language": "zh",
                            "beam_size": 3,  # 流式处理使用较小的beam size
                            "best_of": 3,
                            "temperature": 0.0,
                            "vad_filter": True,
                            "vad_parameters": dict(
                                min_silence_duration_ms=200,  # 更短的静音分割，更实时
                                speech_pad_ms=200
                            ),
                            "word_timestamps": True,
                            "condition_on_previous_text": True,
                            "compression_ratio_threshold": 2.4,
                            "log_prob_threshold": -1.0,
                            "no_speech_threshold": 0.6,
                            "initial_prompt": "以下是中文语音的准确转录："
                        }
                    else:
                        print(f"🎤 使用{model_size}模型流式参数...")
                        transcribe_params = {
                            "language": "zh",
                            "beam_size": 1,
                            "best_of": 1,
                            "temperature": 0.0,
                            "vad_filter": True,
                            "vad_parameters": dict(min_silence_duration_ms=300),
                            "word_timestamps": True
                        }

                    # 开始流式转录
                    segments, info = self.whisper_model.transcribe(
                        self.audio_file_path,
                        **transcribe_params
                    )

                    print(f"🎤 检测到语言: {info.language} (置信度: {info.language_probability:.2f})")

                    segment_count = 0
                    for segment in segments:
                        if not self.subtitle_streaming_mode:  # 检查是否被停止
                            break

                        segment_count += 1
                        start_time = segment.start
                        end_time = segment.end
                        text = segment.text.strip()

                        if text:
                            # 转换为简体中文
                            if self.opencc_converter:
                                try:
                                    text = self.opencc_converter.convert(text)
                                except:
                                    pass

                            # 创建字幕项
                            subtitle_item = {
                                'start': start_time,
                                'end': end_time,
                                'text': text,
                                'generated_at': time.time()  # 记录生成时间
                            }

                            # 立即添加到字幕列表
                            self.subtitle_segments.append(subtitle_item)

                            print(f"🎬 流式字幕 {segment_count}: {start_time:.2f}s-{end_time:.2f}s: {text}")

                            # 立即更新界面和进度
                            def update_ui(count=segment_count, item=subtitle_item):
                                self.progress_text_var.set(f"流式字幕: 已生成 {count} 个片段")
                                self.streaming_status_var.set(f"🔴 实时生成中... ({count})")

                                # 如果当前播放时间在这个字幕范围内，立即显示
                                if self.is_playing:
                                    current_time = self.current_frame / self.fps if self.fps > 0 else 0
                                    if item['start'] <= current_time <= item['end']:
                                        self.update_subtitle(item['text'])
                                        print(f"🎬 实时显示: {item['text']}")

                            self.root.after(0, update_ui)

                            # 短暂延迟，模拟真实的流式处理
                            time.sleep(0.1)

                    # 完成
                    if self.subtitle_streaming_mode:
                        self.subtitle_generation_complete = True
                        self.root.after(0, lambda: self.progress_text_var.set(f"流式字幕完成: 共 {segment_count} 个片段"))
                        self.root.after(0, lambda: self.streaming_status_var.set(f"✅ 完成 ({segment_count}个片段)"))
                        self.root.after(0, lambda: self.generate_subtitle_btn.configure(state="normal"))
                        self.root.after(0, lambda: self.stop_generation_btn.configure(state="disabled"))
                        self.root.after(0, lambda: self.save_subtitle_btn.configure(state="normal"))
                        # 只在控制台输出完成信息，不显示弹框
                        print(f"✅ 流式字幕生成完成，共 {segment_count} 个片段，字幕已与视频实时同步显示")

                except Exception as e:
                    error_msg = f"流式字幕生成失败: {e}"
                    print(f"❌ {error_msg}")
                    self.root.after(0, lambda: self.progress_text_var.set(error_msg))
                    self.root.after(0, lambda: messagebox.showerror("错误", f"❌ {error_msg}"))
                finally:
                    self.subtitle_streaming_mode = False

            # 在后台线程中执行
            threading.Thread(target=streaming_generate, daemon=True).start()

        except Exception as e:
            print(f"❌ 启动实时字幕生成失败: {e}")
            messagebox.showerror("错误", f"启动实时字幕生成失败: {e}")
            self.subtitle_streaming_mode = False

    def start_complete_subtitle_generation(self):
        """开始完整字幕生成"""
        try:
            print("🎬 开始完整字幕生成...")
            self.subtitle_generation_complete = False
            self.subtitle_streaming_mode = False
            self.subtitle_segments = []
            self.current_subtitle_index = 0

            # 更新按钮状态
            self.generate_subtitle_btn.configure(state="disabled")
            self.stop_generation_btn.configure(state="normal")
            self.progress_text_var.set("正在生成完整字幕...")

            def complete_generate():
                try:
                    print("🎤 开始完整语音识别...")

                    # 根据模型类型使用最优参数
                    model_size = getattr(self, 'current_model_size', 'base')

                    if model_size == "large-v3":
                        # large-v3模型最高质量参数
                        print("🎤 使用large-v3模型最高质量参数...")
                        segments, info = self.whisper_model.transcribe(
                            self.audio_file_path,
                            language="zh",  # 中文
                            beam_size=10,   # 最大的beam size，最准确
                            best_of=10,     # 最多候选，最准确
                            temperature=[0.0, 0.2, 0.4, 0.6, 0.8],  # 多温度采样
                            vad_filter=True,  # 语音活动检测
                            vad_parameters=dict(
                                min_silence_duration_ms=200,  # 200ms静音分割，最精细
                                speech_pad_ms=400  # 语音填充
                            ),
                            word_timestamps=True,  # 词级时间戳
                            condition_on_previous_text=True,  # 基于前文上下文
                            compression_ratio_threshold=2.4,  # 压缩比阈值
                            log_prob_threshold=-1.0,  # 对数概率阈值
                            no_speech_threshold=0.6,  # 无语音阈值
                            initial_prompt="以下是中文语音的准确转录："  # 中文提示
                        )
                    else:
                        # 其他模型使用高质量参数
                        print(f"🎤 使用{model_size}模型高质量参数...")
                        segments, info = self.whisper_model.transcribe(
                            self.audio_file_path,
                            language="zh",  # 中文
                            beam_size=5,    # 更大的beam size，更准确
                            best_of=5,      # 多个候选，更准确
                            temperature=0.0,  # 确定性输出
                            vad_filter=True,  # 语音活动检测
                            vad_parameters=dict(min_silence_duration_ms=1000),  # 1秒静音分割
                            word_timestamps=True  # 词级时间戳
                        )

                    print(f"🎤 检测到语言: {info.language} (置信度: {info.language_probability:.2f})")

                    segment_count = 0
                    total_segments = []

                    # 先收集所有片段
                    for segment in segments:
                        total_segments.append(segment)

                    total_count = len(total_segments)
                    print(f"🎬 共检测到 {total_count} 个语音片段")

                    # 处理每个片段
                    for i, segment in enumerate(total_segments):
                        segment_count += 1
                        start_time = segment.start
                        end_time = segment.end
                        text = segment.text.strip()

                        if text:
                            # 转换为简体中文
                            if self.opencc_converter:
                                try:
                                    text = self.opencc_converter.convert(text)
                                except:
                                    pass  # 转换失败就使用原文

                            # 添加到字幕列表
                            subtitle_item = {
                                'start': start_time,
                                'end': end_time,
                                'text': text
                            }
                            self.subtitle_segments.append(subtitle_item)

                            print(f"🎬 字幕片段 {segment_count}/{total_count}: {start_time:.2f}s-{end_time:.2f}s: {text}")

                            # 更新进度
                            progress = (segment_count / total_count) * 100
                            self.root.after(0, lambda p=progress, c=segment_count, t=total_count:
                                          self.progress_text_var.set(f"生成字幕: {c}/{t} ({p:.1f}%)"))

                    # 完成
                    self.subtitle_generation_complete = True
                    self.root.after(0, lambda: self.progress_text_var.set(f"字幕生成完成: 共 {segment_count} 个片段"))
                    self.root.after(0, lambda: self.generate_subtitle_btn.configure(state="normal"))
                    self.root.after(0, lambda: self.stop_generation_btn.configure(state="disabled"))
                    self.root.after(0, lambda: self.save_subtitle_btn.configure(state="normal"))
                    # 只在控制台输出完成信息，不显示弹框
                    print(f"✅ 完整字幕生成完成，共 {segment_count} 个片段，字幕将在播放时自动显示")

                except Exception as e:
                    error_msg = f"字幕生成失败: {e}"
                    print(f"❌ {error_msg}")
                    self.root.after(0, lambda: self.progress_text_var.set(error_msg))
                    self.root.after(0, lambda: messagebox.showerror("错误", f"❌ {error_msg}"))

            # 在后台线程中执行
            threading.Thread(target=complete_generate, daemon=True).start()

        except Exception as e:
            print(f"❌ 启动字幕生成失败: {e}")
            messagebox.showerror("错误", f"启动字幕生成失败: {e}")

    def update_current_subtitle(self, current_time):
        """更新当前显示的字幕 - 优化的流式版本"""
        if not self.subtitle_segments:
            return

        # 查找当前时间对应的字幕
        current_subtitle = None
        current_index = -1

        # 优化搜索：从当前索引附近开始搜索
        start_index = max(0, self.current_subtitle_index - 2)
        end_index = min(len(self.subtitle_segments), self.current_subtitle_index + 10)

        # 首先在附近范围搜索
        for i in range(start_index, end_index):
            if i < len(self.subtitle_segments):
                segment = self.subtitle_segments[i]
                if segment['start'] <= current_time <= segment['end']:
                    current_subtitle = segment['text']
                    current_index = i
                    break

        # 如果附近没找到，全局搜索
        if current_subtitle is None:
            for i, segment in enumerate(self.subtitle_segments):
                if segment['start'] <= current_time <= segment['end']:
                    current_subtitle = segment['text']
                    current_index = i
                    break

        # 更新字幕显示
        if current_subtitle:
            if current_index != self.current_subtitle_index:
                self.current_subtitle_index = current_index
                self.update_subtitle(current_subtitle)

                # 流式生成时的实时反馈
                if self.subtitle_streaming_mode:
                    print(f"🎬 显示字幕 {current_index + 1}: {current_time:.2f}s - {current_subtitle}")
        else:
            # 只在有字幕变化时清空
            if self.subtitle_text:
                self.update_subtitle("")

    def stop_subtitle_generation(self):
        """停止字幕生成"""
        if self.subtitle_streaming_mode:
            self.subtitle_streaming_mode = False
            self.progress_text_var.set("字幕生成已停止")
            print("⏹️ 实时字幕生成已停止")

    def check_gpu_availability(self):
        """检查GPU可用性"""
        try:
            print("🔍 检查GPU可用性...")

            # 检查CUDA
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_count = torch.cuda.device_count()
                    gpu_name = torch.cuda.get_device_name(0)
                    print(f"✅ 检测到CUDA GPU: {gpu_name} (共{gpu_count}个)")
                    self.gpu_available = True
                    self.preferred_device = "cuda"
                    return
            except ImportError:
                print("⚠️ PyTorch未安装，无法检测CUDA")
            except Exception as e:
                print(f"⚠️ CUDA检测失败: {e}")

            # 检查其他GPU加速
            try:
                import subprocess
                result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print("✅ 检测到NVIDIA GPU（通过nvidia-smi）")
                    self.gpu_available = True
                    self.preferred_device = "cuda"
                    return
            except:
                pass

            print("ℹ️ 未检测到可用GPU，将使用CPU")
            self.gpu_available = False
            self.preferred_device = "cpu"

        except Exception as e:
            print(f"❌ GPU检测失败: {e}")
            self.gpu_available = False
            self.preferred_device = "cpu"

    def auto_generate_subtitles_after_load(self):
        """视频加载完成后自动生成字幕"""
        if not self.auto_generate_subtitles:
            return

        if not WHISPER_AVAILABLE or not self.whisper_model:
            print("⚠️ Whisper模型未加载，跳过自动字幕生成")
            return

        if not self.audio_file_path or not os.path.exists(self.audio_file_path):
            print("⚠️ 音频文件不存在，跳过自动字幕生成")
            return

        print("🎬 开始自动流式生成字幕...")
        self.progress_text_var.set("流式生成字幕中...")

        # 使用流式字幕生成模式
        self.start_streaming_subtitle_generation()

if __name__ == "__main__":
    import tkinter as tk
    root = tk.Tk()
    player = SimpleMP4Player(root)
    root.mainloop()