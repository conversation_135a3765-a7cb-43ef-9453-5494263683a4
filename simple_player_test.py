#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版播放器测试
用于测试基本功能而不会自动关闭
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import os
import sys
from PIL import Image, ImageTk
import traceback

class SimpleMP4Player:
    def __init__(self, root):
        self.root = root
        self.root.title("简化MP4播放器")
        self.root.geometry("800x600")
        
        # 视频相关变量
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        
        self.setup_ui()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        print("✅ 简化播放器初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 文件选择按钮
        ttk.Button(control_frame, text="选择MP4文件", 
                  command=self.select_video_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放控制按钮
        self.play_button = ttk.Button(control_frame, text="播放", 
                                     command=self.toggle_play, state="disabled")
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="停止", 
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(control_frame, from_=0, to=100, 
                                     orient=tk.HORIZONTAL, variable=self.progress_var)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(control_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(self.root, relief=tk.SUNKEN, borderwidth=2)
        self.video_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.video_label = ttk.Label(self.video_frame, text="请选择MP4文件开始播放")
        self.video_label.pack(expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, padx=10, pady=(0, 5))
    
    def select_video_file(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP4文件",
            filetypes=[("MP4文件", "*.mp4"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            print(f"加载视频: {video_path}")
            
            # 停止当前播放
            self.stop_video()
            
            # 检查文件
            if not os.path.exists(video_path):
                raise Exception(f"文件不存在: {video_path}")
            
            # 设置路径
            self.video_path = video_path
            
            # 创建VideoCapture
            self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            if self.total_frames <= 0:
                self.total_frames = 1000
            if self.fps <= 0:
                self.fps = 30
            
            print(f"视频信息: {self.total_frames}帧, {self.fps}fps")
            
            # 重置进度条
            self.progress_bar.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0
            
            # 显示第一帧
            self.show_frame()
            
            # 更新状态
            duration = self.total_frames / self.fps
            self.status_var.set(f"视频已加载: {os.path.basename(video_path)} "
                              f"({self.total_frames}帧, {duration:.1f}秒)")
            
            # 启用播放按钮
            self.play_button.configure(state="normal")
            
            print("✅ 视频加载成功")
            
        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            
            # 清理资源
            if self.cap:
                self.cap.release()
                self.cap = None
    
    def show_frame(self):
        """显示当前帧"""
        if self.cap is None:
            return
        
        try:
            # 读取帧
            ret, frame = self.cap.read()
            if not ret:
                print("无法读取帧")
                return
            
            # 转换为PIL图像
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # 调整大小
            display_width = self.video_frame.winfo_width()
            display_height = self.video_frame.winfo_height()
            
            if display_width > 1 and display_height > 1:
                # 保持宽高比
                img_ratio = pil_image.width / pil_image.height
                display_ratio = display_width / display_height
                
                if img_ratio > display_ratio:
                    new_width = display_width
                    new_height = int(display_width / img_ratio)
                else:
                    new_height = display_height
                    new_width = int(display_height * img_ratio)
                
                new_width = max(1, new_width)
                new_height = max(1, new_height)
                
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter图像
            photo = ImageTk.PhotoImage(pil_image)
            self.video_label.configure(image=photo, text="")
            self.video_label.image = photo  # 保持引用
            
            # 更新进度
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            total_time = self.total_frames / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            
        except Exception as e:
            print(f"显示帧时出错: {e}")
    
    def toggle_play(self):
        """切换播放/暂停"""
        if not self.cap:
            return
        
        self.is_playing = not self.is_playing
        
        if self.is_playing:
            self.play_button.configure(text="暂停")
            self.play_video()
        else:
            self.play_button.configure(text="播放")
    
    def play_video(self):
        """播放视频"""
        if not self.is_playing or not self.cap:
            return
        
        try:
            # 读取下一帧
            ret, frame = self.cap.read()
            if ret:
                self.current_frame += 1
                self.show_frame()
                
                # 计算延迟
                delay = int(1000 / self.fps)
                self.root.after(delay, self.play_video)
            else:
                # 播放完毕
                self.is_playing = False
                self.play_button.configure(text="播放")
                print("播放完毕")
        except Exception as e:
            print(f"播放时出错: {e}")
            self.is_playing = False
            self.play_button.configure(text="播放")
    
    def stop_video(self):
        """停止播放"""
        self.is_playing = False
        if hasattr(self, 'play_button'):
            self.play_button.configure(text="播放")
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        self.current_frame = 0
        self.video_path = None
        
        # 重置界面
        self.video_label.configure(image="", text="请选择MP4文件开始播放")
        self.video_label.image = None
        self.status_var.set("就绪")
        
        if hasattr(self, 'play_button'):
            self.play_button.configure(state="disabled")
    
    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def on_closing(self):
        """程序关闭时的清理工作"""
        print("📝 程序正在关闭...")
        self.stop_video()
        self.root.destroy()

def main():
    """主函数"""
    try:
        print("🚀 启动简化MP4播放器...")
        
        root = tk.Tk()
        app = SimpleMP4Player(root)
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            video_file = sys.argv[1]
            print(f"📁 从命令行加载视频: {video_file}")
            
            if os.path.exists(video_file):
                app.load_video(video_file)
            else:
                print(f"❌ 文件不存在: {video_file}")
        
        print("🎬 启动主界面...")
        root.mainloop()
        
        print("🔚 程序正常结束")
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
