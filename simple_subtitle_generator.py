#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单字幕生成器 - 专门用于测试字幕生成功能
不涉及复杂的视频播放，专注于字幕生成
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
import tempfile

# 检查依赖
print("🔍 检查字幕生成依赖...")

try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    print("✅ faster-whisper 可用")
except ImportError as e:
    WHISPER_AVAILABLE = False
    print(f"❌ faster-whisper 不可用: {e}")

try:
    import moviepy
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")
    print("尝试安装: pip install moviepy")
except Exception as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 导入异常: {e}")

class SimpleSubtitleGenerator:
    def __init__(self):
        print("🚀 启动简单字幕生成器...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("简单字幕生成器 - 测试版")
        self.root.geometry("800x600")
        
        # 变量
        self.video_path = None
        self.whisper_model = None
        self.subtitle_segments = []
        
        # 创建界面
        self.create_ui()
        
        # 加载Whisper模型
        if WHISPER_AVAILABLE:
            self.load_whisper_model()
        
        print("✅ 简单字幕生成器初始化完成")
    
    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightcyan")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="简单字幕生成器", 
                              font=("Arial", 18, "bold"), bg="lightcyan")
        title_label.pack(pady=10)
        
        # 功能说明
        info_frame = tk.LabelFrame(main_frame, text="功能说明", font=("Arial", 12))
        info_frame.pack(fill=tk.X, pady=10)
        
        info_text = """🎯 专门用于测试字幕生成功能
🔧 使用faster-whisper进行语音识别
🎬 使用moviepy提取音频
📝 生成SRT格式字幕文件"""
        
        tk.Label(info_frame, text=info_text, font=("Arial", 11), 
                justify=tk.LEFT, bg="lightyellow").pack(padx=15, pady=15, fill=tk.X)
        
        # 依赖状态
        status_frame = tk.LabelFrame(main_frame, text="依赖状态", font=("Arial", 12))
        status_frame.pack(fill=tk.X, pady=10)
        
        whisper_status = "✅ 可用" if WHISPER_AVAILABLE else "❌ 不可用"
        moviepy_status = "✅ 可用" if MOVIEPY_AVAILABLE else "❌ 不可用"
        
        tk.Label(status_frame, text=f"faster-whisper: {whisper_status}", 
                font=("Arial", 11)).pack(anchor=tk.W, padx=15, pady=5)
        tk.Label(status_frame, text=f"moviepy: {moviepy_status}", 
                font=("Arial", 11)).pack(anchor=tk.W, padx=15, pady=5)
        
        # 控制面板
        control_frame = tk.Frame(main_frame, bg="lightcyan")
        control_frame.pack(fill=tk.X, pady=15)
        
        tk.Button(control_frame, text="📁 选择视频", font=("Arial", 12),
                 command=self.select_video, bg="lightblue").pack(side=tk.LEFT, padx=5)
        
        self.generate_btn = tk.Button(control_frame, text="🎤 生成字幕", font=("Arial", 12),
                                     command=self.generate_subtitles, state="disabled", bg="orange")
        self.generate_btn.pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="💾 保存字幕", font=("Arial", 12),
                 command=self.save_subtitles, bg="lightgreen").pack(side=tk.LEFT, padx=5)
        
        # 视频信息
        self.video_info_var = tk.StringVar(value="未选择视频文件")
        tk.Label(control_frame, textvariable=self.video_info_var, 
                font=("Arial", 10)).pack(side=tk.LEFT, padx=15)
        
        # 进度显示
        progress_frame = tk.LabelFrame(main_frame, text="生成进度", font=("Arial", 12))
        progress_frame.pack(fill=tk.X, pady=10)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=15, pady=10)
        
        self.progress_text_var = tk.StringVar(value="就绪")
        tk.Label(progress_frame, textvariable=self.progress_text_var, 
                font=("Arial", 11)).pack(pady=5)
        
        # 字幕预览
        preview_frame = tk.LabelFrame(main_frame, text="字幕预览", font=("Arial", 12))
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建文本框和滚动条
        text_frame = tk.Frame(preview_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.subtitle_text = tk.Text(text_frame, font=("Consolas", 10), wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.subtitle_text.yview)
        self.subtitle_text.configure(yscrollcommand=scrollbar.set)
        
        self.subtitle_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = tk.Label(main_frame, textvariable=self.status_var, 
                             relief=tk.SUNKEN, font=("Arial", 10))
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
        # 添加初始信息
        self.subtitle_text.insert(tk.END, "🎬 简单字幕生成器\n\n")
        self.subtitle_text.insert(tk.END, "使用说明:\n")
        self.subtitle_text.insert(tk.END, "1. 点击'选择视频'选择要处理的视频文件\n")
        self.subtitle_text.insert(tk.END, "2. 点击'生成字幕'开始AI语音识别\n")
        self.subtitle_text.insert(tk.END, "3. 生成完成后可以预览和保存字幕\n\n")
        
        if not WHISPER_AVAILABLE:
            self.subtitle_text.insert(tk.END, "⚠️ 警告: faster-whisper不可用\n")
            self.subtitle_text.insert(tk.END, "请安装: pip install faster-whisper\n\n")
        
        if not MOVIEPY_AVAILABLE:
            self.subtitle_text.insert(tk.END, "⚠️ 警告: moviepy不可用\n")
            self.subtitle_text.insert(tk.END, "请安装: pip install moviepy\n\n")
    
    def load_whisper_model(self):
        """加载Whisper模型"""
        def load_model():
            try:
                self.root.after(0, lambda: self.status_var.set("正在加载Whisper模型..."))
                self.root.after(0, lambda: self.progress_text_var.set("加载模型中..."))
                
                # 尝试加载base模型
                self.whisper_model = WhisperModel("base", device="cpu", compute_type="int8")
                
                self.root.after(0, lambda: self.status_var.set("Whisper模型加载成功"))
                self.root.after(0, lambda: self.progress_text_var.set("模型就绪"))
                
                print("✅ Whisper模型加载成功")
                
            except Exception as e:
                error_msg = f"Whisper模型加载失败: {e}"
                self.root.after(0, lambda: self.status_var.set(error_msg))
                self.root.after(0, lambda: self.progress_text_var.set("模型加载失败"))
                print(f"❌ {error_msg}")
        
        # 在后台线程中加载
        threading.Thread(target=load_model, daemon=True).start()
    
    def select_video(self):
        """选择视频"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[("视频文件", "*.mp4 *.avi *.mov *.mkv"), ("所有文件", "*.*")]
            )
            
            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_info_var.set(f"已选择: {filename}")
                
                # 检查文件大小
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                
                self.subtitle_text.insert(tk.END, f"\n📁 已选择视频: {filename}\n")
                self.subtitle_text.insert(tk.END, f"📊 文件大小: {file_size:.1f} MB\n")
                self.subtitle_text.see(tk.END)
                
                # 启用生成按钮
                if WHISPER_AVAILABLE and MOVIEPY_AVAILABLE and self.whisper_model:
                    self.generate_btn.configure(state="normal")
                    self.status_var.set("可以开始生成字幕")
                else:
                    missing = []
                    if not WHISPER_AVAILABLE:
                        missing.append("faster-whisper")
                    if not MOVIEPY_AVAILABLE:
                        missing.append("moviepy")
                    if not self.whisper_model:
                        missing.append("Whisper模型")
                    
                    self.status_var.set(f"缺少依赖: {', '.join(missing)}")
                
        except Exception as e:
            error_msg = f"选择视频失败: {e}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def generate_subtitles(self):
        """生成字幕"""
        if not self.video_path:
            messagebox.showwarning("警告", "请先选择视频文件")
            return
        
        if not WHISPER_AVAILABLE or not self.whisper_model:
            messagebox.showerror("错误", "Whisper模型不可用")
            return
        
        if not MOVIEPY_AVAILABLE:
            messagebox.showerror("错误", "moviepy不可用，无法提取音频")
            return
        
        def generate():
            try:
                self.root.after(0, lambda: self.generate_btn.configure(state="disabled"))
                self.root.after(0, lambda: self.status_var.set("正在生成字幕..."))
                
                # 步骤1: 提取音频
                self.root.after(0, lambda: self.progress_text_var.set("正在提取音频..."))
                self.root.after(0, lambda: self.progress_var.set(10))
                
                self.subtitle_text.insert(tk.END, "\n🔊 开始提取音频...\n")
                self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
                video_clip = VideoFileClip(self.video_path)
                
                # 创建临时音频文件
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                    audio_path = temp_audio.name
                
                # 提取音频
                audio_clip = video_clip.audio
                audio_clip.write_audiofile(audio_path, verbose=False, logger=None)
                
                # 关闭文件
                video_clip.close()
                audio_clip.close()
                
                self.subtitle_text.insert(tk.END, "✅ 音频提取完成\n")
                self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
                # 步骤2: 语音识别
                self.root.after(0, lambda: self.progress_text_var.set("正在进行语音识别..."))
                self.root.after(0, lambda: self.progress_var.set(40))
                
                self.subtitle_text.insert(tk.END, "\n🤖 开始AI语音识别...\n")
                self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
                segments, info = self.whisper_model.transcribe(
                    audio_path,
                    language="zh",  # 中文
                    beam_size=5,
                    word_timestamps=True
                )
                
                self.subtitle_text.insert(tk.END, f"🔍 检测到语言: {info.language} (置信度: {info.language_probability:.2f})\n")
                self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
                # 步骤3: 处理结果
                self.root.after(0, lambda: self.progress_text_var.set("正在处理识别结果..."))
                self.root.after(0, lambda: self.progress_var.set(70))
                
                self.subtitle_segments = []
                
                self.subtitle_text.insert(tk.END, "\n📝 识别结果:\n")
                self.subtitle_text.insert(tk.END, "=" * 50 + "\n")
                self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
                for i, segment in enumerate(segments):
                    subtitle_info = {
                        'start': segment.start,
                        'end': segment.end,
                        'text': segment.text.strip()
                    }
                    self.subtitle_segments.append(subtitle_info)
                    
                    # 显示到界面
                    time_info = f"[{segment.start:.1f}s - {segment.end:.1f}s]"
                    self.subtitle_text.insert(tk.END, f"{i+1:3d}. {time_info} {segment.text.strip()}\n")
                    
                    if (i + 1) % 5 == 0:  # 每5条更新一次显示
                        self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
                # 清理临时文件
                try:
                    os.unlink(audio_path)
                except:
                    pass
                
                # 完成
                self.root.after(0, lambda: self.progress_text_var.set(f"完成 - 共{len(self.subtitle_segments)}条字幕"))
                self.root.after(0, lambda: self.progress_var.set(100))
                self.root.after(0, lambda: self.status_var.set("字幕生成完成"))
                
                self.subtitle_text.insert(tk.END, "=" * 50 + "\n")
                self.subtitle_text.insert(tk.END, f"✅ 字幕生成完成！共生成 {len(self.subtitle_segments)} 条字幕\n")
                self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
            except Exception as e:
                error_msg = f"字幕生成失败: {e}"
                self.root.after(0, lambda: self.status_var.set(error_msg))
                self.root.after(0, lambda: self.progress_text_var.set("生成失败"))
                self.root.after(0, lambda: self.progress_var.set(0))
                
                self.subtitle_text.insert(tk.END, f"\n❌ 错误: {error_msg}\n")
                self.root.after(0, lambda: self.subtitle_text.see(tk.END))
                
            finally:
                self.root.after(0, lambda: self.generate_btn.configure(state="normal"))
        
        # 在后台线程中生成字幕
        threading.Thread(target=generate, daemon=True).start()
    
    def save_subtitles(self):
        """保存字幕文件"""
        if not self.subtitle_segments:
            messagebox.showwarning("警告", "没有字幕可保存")
            return
        
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存字幕文件",
                defaultextension=".srt",
                filetypes=[("SRT字幕", "*.srt"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if file_path:
                with open(file_path, "w", encoding="utf-8") as f:
                    for i, segment in enumerate(self.subtitle_segments, 1):
                        start_time = self.format_time(segment['start'])
                        end_time = self.format_time(segment['end'])
                        
                        f.write(f"{i}\n")
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{segment['text']}\n\n")
                
                self.subtitle_text.insert(tk.END, f"\n💾 字幕已保存: {os.path.basename(file_path)}\n")
                self.subtitle_text.see(tk.END)
                
                self.status_var.set(f"字幕已保存到: {file_path}")
                messagebox.showinfo("成功", f"字幕已保存到:\n{file_path}")
                
        except Exception as e:
            error_msg = f"保存字幕失败: {e}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def format_time(self, seconds):
        """格式化时间为SRT格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"
    
    def run(self):
        """运行生成器"""
        try:
            print("🎬 启动简单字幕生成器界面...")
            
            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    filename = os.path.basename(video_file)
                    self.video_info_var.set(f"命令行: {filename}")
                    
                    self.subtitle_text.insert(tk.END, f"\n📁 命令行参数: {filename}\n")
                    self.subtitle_text.see(tk.END)
                    
                    if WHISPER_AVAILABLE and MOVIEPY_AVAILABLE:
                        self.generate_btn.configure(state="normal")
                
            # 启动主循环
            self.root.mainloop()
            
            print("🔚 简单字幕生成器正常结束")
            
        except Exception as e:
            print(f"❌ 生成器异常: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 简单字幕生成器")
    print("=" * 60)
    print("功能: 专门用于测试字幕生成功能")
    print("引擎: faster-whisper + moviepy")
    print("输出: SRT格式字幕文件")
    print("=" * 60)
    
    try:
        generator = SimpleSubtitleGenerator()
        generator.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
