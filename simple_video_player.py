#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单视频播放器 - 基于超简单播放器，添加视频功能
保持简单性，绝对不会自动关闭
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import os
import time

class SimpleVideoPlayer:
    def __init__(self):
        print("🚀 启动简单视频播放器...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("简单视频播放器 - 绝不自动关闭")
        self.root.geometry("800x600")
        
        # 设置变量
        self.running = True
        self.cap = None
        self.video_path = None
        
        # 创建界面
        self.create_ui()
        
        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.ask_before_close)
        
        # 启动定时器
        self.start_timer()
        
        print("✅ 简单视频播放器初始化完成")
    
    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightgray")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 标题
        title_label = tk.Label(main_frame, text="简单视频播放器", 
                              font=("Arial", 18, "bold"), bg="lightgray")
        title_label.pack(pady=10)
        
        # 文件选择区域
        file_frame = tk.Frame(main_frame, bg="lightgray")
        file_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(file_frame, text="📁 选择视频文件", font=("Arial", 12),
                 command=self.select_video, bg="lightblue").pack(side=tk.LEFT, padx=5)
        
        self.file_label = tk.Label(file_frame, text="未选择文件", 
                                  font=("Arial", 10), bg="lightgray")
        self.file_label.pack(side=tk.LEFT, padx=10)
        
        # 视频信息显示
        info_frame = tk.LabelFrame(main_frame, text="视频信息", font=("Arial", 12))
        info_frame.pack(fill=tk.X, pady=10)
        
        self.info_text = tk.Text(info_frame, height=6, font=("Consolas", 10))
        self.info_text.pack(fill=tk.X, padx=10, pady=10)
        
        # 状态显示
        status_frame = tk.LabelFrame(main_frame, text="运行状态", font=("Arial", 12))
        status_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.status_text = tk.Text(status_frame, height=8, font=("Consolas", 9))
        scrollbar = tk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # 控制按钮
        control_frame = tk.Frame(main_frame, bg="lightgray")
        control_frame.pack(fill=tk.X, pady=10)
        
        tk.Button(control_frame, text="🔒 保持运行", font=("Arial", 12),
                 command=self.force_keep_running, bg="orange").pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="🧪 测试OpenCV", font=("Arial", 12),
                 command=self.test_opencv, bg="lightgreen").pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="❌ 手动关闭", font=("Arial", 12),
                 command=self.manual_close, bg="red", fg="white").pack(side=tk.RIGHT, padx=5)
        
        # 添加初始信息
        self.add_status("程序启动成功！")
        self.add_status("这个播放器绝对不会自动关闭")
        self.add_status("请选择视频文件开始测试")
        
        self.add_info("等待选择视频文件...")
    
    def add_status(self, message):
        """添加状态信息"""
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_text.see(tk.END)
        print(f"[{timestamp}] {message}")
    
    def add_info(self, message):
        """添加信息"""
        timestamp = time.strftime("%H:%M:%S")
        self.info_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.info_text.see(tk.END)
    
    def select_video(self):
        """选择视频文件"""
        try:
            self.add_status("打开文件选择对话框...")
            
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[
                    ("视频文件", "*.mp4 *.avi *.mov *.mkv"),
                    ("MP4文件", "*.mp4"),
                    ("所有文件", "*.*")
                ]
            )
            
            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.file_label.configure(text=filename)
                
                self.add_status(f"选择了视频文件: {filename}")
                self.test_video_load()
            else:
                self.add_status("用户取消了文件选择")
                
        except Exception as e:
            self.add_status(f"文件选择出错: {e}")
            self.add_info(f"错误: {e}")
    
    def test_video_load(self):
        """测试视频加载"""
        if not self.video_path:
            return
        
        try:
            self.add_status("开始测试视频加载...")
            self.add_info("正在分析视频文件...")
            
            # 检查文件
            if not os.path.exists(self.video_path):
                raise Exception("文件不存在")
            
            file_size = os.path.getsize(self.video_path)
            self.add_info(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
            
            # 尝试导入OpenCV
            try:
                import cv2
                self.add_info(f"OpenCV版本: {cv2.__version__}")
            except ImportError as e:
                raise Exception(f"OpenCV导入失败: {e}")
            
            # 尝试打开视频
            self.add_status("尝试打开视频文件...")
            
            methods = [
                ("默认方式", lambda: cv2.VideoCapture(self.video_path)),
                ("MSMF后端", lambda: cv2.VideoCapture(self.video_path, cv2.CAP_MSMF)),
                ("FFMPEG后端", lambda: cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)),
            ]
            
            success = False
            for method_name, method_func in methods:
                try:
                    self.add_status(f"尝试 {method_name}...")
                    
                    cap = method_func()
                    if cap and cap.isOpened():
                        # 获取视频信息
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        
                        self.add_info(f"✅ {method_name} 成功")
                        self.add_info(f"分辨率: {width}x{height}")
                        self.add_info(f"帧率: {fps:.2f} fps")
                        self.add_info(f"总帧数: {frame_count}")
                        
                        if frame_count > 0 and fps > 0:
                            duration = frame_count / fps
                            self.add_info(f"时长: {duration:.2f} 秒")
                        
                        # 测试读取第一帧
                        ret, frame = cap.read()
                        if ret and frame is not None:
                            self.add_info(f"✅ 成功读取帧，形状: {frame.shape}")
                            success = True
                        else:
                            self.add_info("❌ 无法读取帧")
                        
                        cap.release()
                        
                        if success:
                            self.cap = None  # 不保持打开状态
                            break
                    else:
                        self.add_status(f"❌ {method_name} 无法打开")
                        if cap:
                            cap.release()
                            
                except Exception as e:
                    self.add_status(f"❌ {method_name} 失败: {e}")
                    continue
            
            if success:
                self.add_status("✅ 视频文件测试成功！")
                self.add_info("视频文件可以正常播放")
            else:
                self.add_status("❌ 所有方法都无法打开视频文件")
                self.add_info("视频文件可能损坏或格式不支持")
                
        except Exception as e:
            self.add_status(f"❌ 视频加载测试失败: {e}")
            self.add_info(f"错误详情: {e}")
    
    def test_opencv(self):
        """测试OpenCV功能"""
        try:
            self.add_status("测试OpenCV基本功能...")
            
            import cv2
            self.add_info(f"OpenCV版本: {cv2.__version__}")
            
            # 测试创建VideoCapture
            cap = cv2.VideoCapture()
            self.add_info("✅ VideoCapture对象创建成功")
            
            cap.release()
            self.add_info("✅ VideoCapture释放成功")
            
            self.add_status("✅ OpenCV测试通过")
            
        except Exception as e:
            self.add_status(f"❌ OpenCV测试失败: {e}")
            self.add_info(f"OpenCV错误: {e}")
    
    def force_keep_running(self):
        """强制保持运行"""
        self.add_status("🔒 强制保持运行模式激活！")
        messagebox.showinfo("保持运行", "程序已设置为强制保持运行模式！\n绝对不会自动关闭。")
    
    def manual_close(self):
        """手动关闭"""
        self.add_status("用户请求手动关闭程序")
        
        result = messagebox.askyesno("手动关闭", 
                                   "确定要关闭程序吗？\n\n这是唯一的关闭方式。")
        if result:
            self.add_status("用户确认关闭程序")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_status("用户取消关闭，继续运行")
    
    def ask_before_close(self):
        """关闭前询问"""
        self.add_status("用户尝试关闭窗口")
        
        result = messagebox.askyesno("确认关闭", 
                                   "真的要关闭程序吗？\n\n选择'否'继续运行")
        if result:
            self.add_status("用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_status("用户取消关闭，继续运行")
    
    def cleanup(self):
        """清理资源"""
        self.running = False
        if self.cap:
            try:
                self.cap.release()
            except:
                pass
            self.cap = None
    
    def start_timer(self):
        """启动定时器"""
        if not hasattr(self, 'start_time'):
            self.start_time = time.time()
        
        def update_timer():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)
                    
                    # 每30秒报告一次
                    if runtime > 0 and runtime % 30 == 0:
                        self.add_status(f"💓 程序运行正常，已运行 {runtime} 秒")
                    
                    # 继续定时器
                    self.root.after(1000, update_timer)
                    
                except Exception as e:
                    print(f"定时器错误: {e}")
        
        update_timer()
    
    def run(self):
        """运行播放器"""
        try:
            print("🎬 启动简单视频播放器界面...")
            print("💡 这个版本绝对不会自动关闭")
            
            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                print(f"命令行参数: {video_file}")
                self.add_status(f"命令行参数: {video_file}")
                
                if os.path.exists(video_file):
                    self.video_path = video_file
                    self.file_label.configure(text=os.path.basename(video_file))
                    # 延迟测试，确保界面完全加载
                    self.root.after(1000, self.test_video_load)
                else:
                    self.add_status(f"命令行文件不存在: {video_file}")
            
            # 启动主循环
            self.root.mainloop()
            
            print("🔚 程序正常结束")
            
        except Exception as e:
            print(f"❌ 程序异常: {e}")
            import traceback
            traceback.print_exc()
            
            try:
                input("\n程序异常，按回车键退出...")
            except:
                time.sleep(10)

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 简单视频播放器")
    print("=" * 60)
    print("特点:")
    print("- 绝对不会自动关闭")
    print("- 可以测试视频文件加载")
    print("- 显示详细的视频信息")
    print("- 需要用户手动确认才能关闭")
    print("=" * 60)
    
    try:
        player = SimpleVideoPlayer()
        player.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()
