#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定版MP4播放器
专门解决自动关闭问题
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import os
import sys
from PIL import Image, ImageTk
import threading
import time
import traceback

class StableMP4Player:
    def __init__(self, root):
        self.root = root
        self.root.title("稳定版MP4播放器")
        self.root.geometry("1000x700")
        
        # 视频相关变量
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.play_thread = None
        self.should_stop = False
        
        # 创建界面
        self.create_ui()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        print("✅ 稳定版播放器初始化完成")
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择
        ttk.Button(control_frame, text="选择视频文件", 
                  command=self.select_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放控制
        self.play_btn = ttk.Button(control_frame, text="播放", 
                                  command=self.toggle_play, state="disabled")
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="停止", 
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(control_frame, from_=0, to=100, 
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.seek_video)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(control_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=2)
        self.video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = ttk.Label(self.video_frame, text="请选择视频文件")
        self.video_label.pack(expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X)
        
        # 保持运行按钮（防止意外关闭）
        keep_alive_frame = ttk.Frame(main_frame)
        keep_alive_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(keep_alive_frame, text="程序状态:").pack(side=tk.LEFT)
        self.keep_alive_var = tk.StringVar(value="运行中")
        ttk.Label(keep_alive_frame, textvariable=self.keep_alive_var, 
                 foreground="green").pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(keep_alive_frame, text="保持运行", 
                  command=self.keep_alive).pack(side=tk.RIGHT)
    
    def keep_alive(self):
        """保持程序运行"""
        self.keep_alive_var.set("强制保持运行中...")
        self.root.after(2000, lambda: self.keep_alive_var.set("运行中"))
        print("🔄 程序保持运行")
    
    def select_file(self):
        """选择视频文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[
                    ("视频文件", "*.mp4 *.avi *.mov *.mkv"),
                    ("MP4文件", "*.mp4"),
                    ("所有文件", "*.*")
                ]
            )
            
            if file_path:
                self.load_video(file_path)
        except Exception as e:
            print(f"选择文件时出错: {e}")
            self.status_var.set(f"选择文件失败: {e}")
    
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            print(f"📁 加载视频: {video_path}")
            self.status_var.set("正在加载视频...")
            
            # 停止当前播放
            self.stop_video()
            
            # 检查文件
            if not os.path.exists(video_path):
                raise Exception(f"文件不存在: {video_path}")
            
            file_size = os.path.getsize(video_path)
            print(f"文件大小: {file_size:,} 字节")
            
            # 设置路径
            self.video_path = video_path
            
            # 尝试多种方式打开视频
            success = False
            methods = [
                ("默认方式", lambda: cv2.VideoCapture(video_path)),
                ("MSMF后端", lambda: cv2.VideoCapture(video_path, cv2.CAP_MSMF)),
                ("FFMPEG后端", lambda: cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)),
            ]
            
            for method_name, method_func in methods:
                try:
                    print(f"尝试 {method_name}...")
                    self.cap = method_func()
                    
                    if self.cap and self.cap.isOpened():
                        # 测试读取
                        ret, test_frame = self.cap.read()
                        if ret and test_frame is not None:
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            print(f"✅ {method_name} 成功")
                            success = True
                            break
                        else:
                            self.cap.release()
                            print(f"❌ {method_name} 无法读取帧")
                    else:
                        if self.cap:
                            self.cap.release()
                        print(f"❌ {method_name} 无法打开")
                except Exception as e:
                    print(f"❌ {method_name} 失败: {e}")
                    continue
            
            if not success:
                raise Exception("所有方法都无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            if self.total_frames <= 0:
                self.total_frames = 1000
            if self.fps <= 0:
                self.fps = 30
            
            print(f"视频信息: {self.total_frames}帧, {self.fps}fps")
            
            # 重置界面
            self.progress_bar.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0
            
            # 显示第一帧
            self.show_frame()
            
            # 更新状态
            duration = self.total_frames / self.fps
            self.status_var.set(f"视频已加载: {os.path.basename(video_path)} "
                              f"({self.total_frames}帧, {duration:.1f}秒)")
            
            # 启用播放按钮
            self.play_btn.configure(state="normal")
            
            print("✅ 视频加载成功")
            
        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(f"❌ {error_msg}")
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
            
            # 清理
            if hasattr(self, 'cap') and self.cap:
                try:
                    self.cap.release()
                except:
                    pass
                self.cap = None
    
    def show_frame(self):
        """显示当前帧"""
        if not self.cap:
            return
        
        try:
            # 读取帧
            ret, frame = self.cap.read()
            if not ret:
                return
            
            # 转换为RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # 调整大小适应显示区域
            display_width = self.video_frame.winfo_width()
            display_height = self.video_frame.winfo_height()
            
            if display_width > 1 and display_height > 1:
                # 保持宽高比
                img_ratio = pil_image.width / pil_image.height
                display_ratio = display_width / display_height
                
                if img_ratio > display_ratio:
                    new_width = display_width - 20
                    new_height = int(new_width / img_ratio)
                else:
                    new_height = display_height - 20
                    new_width = int(new_height * img_ratio)
                
                new_width = max(1, new_width)
                new_height = max(1, new_height)
                
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter图像
            photo = ImageTk.PhotoImage(pil_image)
            self.video_label.configure(image=photo, text="")
            self.video_label.image = photo  # 保持引用
            
            # 更新进度
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps
            total_time = self.total_frames / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            
        except Exception as e:
            print(f"显示帧时出错: {e}")
    
    def toggle_play(self):
        """切换播放/暂停"""
        if not self.cap:
            return
        
        self.is_playing = not self.is_playing
        
        if self.is_playing:
            self.play_btn.configure(text="暂停")
            self.start_playback()
        else:
            self.play_btn.configure(text="播放")
            self.should_stop = True
    
    def start_playback(self):
        """开始播放"""
        if self.play_thread and self.play_thread.is_alive():
            self.should_stop = True
            self.play_thread.join(timeout=1.0)
        
        self.should_stop = False
        self.play_thread = threading.Thread(target=self.playback_loop, daemon=True)
        self.play_thread.start()
    
    def playback_loop(self):
        """播放循环"""
        try:
            while self.is_playing and not self.should_stop and self.cap:
                start_time = time.time()
                
                # 读取下一帧
                ret, frame = self.cap.read()
                if ret:
                    self.current_frame += 1
                    
                    # 在主线程中更新显示
                    self.root.after(0, self.show_frame)
                    
                    # 控制播放速度
                    elapsed = time.time() - start_time
                    delay = (1.0 / self.fps) - elapsed
                    if delay > 0:
                        time.sleep(delay)
                else:
                    # 播放完毕
                    self.root.after(0, self.playback_finished)
                    break
                    
        except Exception as e:
            print(f"播放循环出错: {e}")
            self.root.after(0, self.playback_finished)
    
    def playback_finished(self):
        """播放完毕"""
        self.is_playing = False
        self.play_btn.configure(text="播放")
        print("播放完毕")
    
    def seek_video(self, value):
        """跳转到指定位置"""
        if not self.cap:
            return
        
        try:
            frame_number = int(float(value))
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            self.current_frame = frame_number
            self.show_frame()
        except Exception as e:
            print(f"跳转时出错: {e}")
    
    def stop_video(self):
        """停止播放"""
        self.is_playing = False
        self.should_stop = True
        
        if hasattr(self, 'play_btn'):
            self.play_btn.configure(text="播放", state="disabled")
        
        # 等待播放线程结束
        if self.play_thread and self.play_thread.is_alive():
            self.play_thread.join(timeout=1.0)
        
        # 释放资源
        if self.cap:
            try:
                self.cap.release()
            except:
                pass
            self.cap = None
        
        # 重置界面
        self.current_frame = 0
        self.video_path = None
        self.video_label.configure(image="", text="请选择视频文件")
        self.video_label.image = None
        self.status_var.set("就绪")
    
    def format_time(self, seconds):
        """格式化时间"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def on_closing(self):
        """程序关闭"""
        try:
            print("📝 程序正在关闭...")
            self.should_stop = True
            self.stop_video()
            
            # 等待线程结束
            if self.play_thread and self.play_thread.is_alive():
                self.play_thread.join(timeout=2.0)
            
            print("✅ 清理完成")
        except Exception as e:
            print(f"关闭时出错: {e}")
        finally:
            self.root.destroy()

def main():
    """主函数"""
    try:
        print("🚀 启动稳定版MP4播放器...")
        
        # 创建主窗口
        root = tk.Tk()
        
        # 创建播放器
        player = StableMP4Player(root)
        
        # 处理命令行参数
        if len(sys.argv) > 1:
            video_file = sys.argv[1]
            print(f"📁 从命令行加载: {video_file}")
            if os.path.exists(video_file):
                # 延迟加载，确保界面完全初始化
                root.after(1000, lambda: player.load_video(video_file))
            else:
                print(f"❌ 文件不存在: {video_file}")
        
        print("🎬 启动界面...")
        print("💡 提示: 点击'保持运行'按钮可防止意外关闭")
        
        # 启动主循环
        root.mainloop()
        
        print("🔚 程序正常结束")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        traceback.print_exc()
        
        # 保持控制台打开
        try:
            input("\n按回车键退出...")
        except:
            time.sleep(5)

if __name__ == "__main__":
    main()
