#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频同步诊断工具
实时监控音视频同步状态
"""

import tkinter as tk
from tkinter import ttk, filedialog
import time
import threading
import os

try:
    import pygame
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
except:
    PYGAME_AVAILABLE = False

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
except:
    MOVIEPY_AVAILABLE = False

class SyncDiagnosis:
    def __init__(self, root):
        self.root = root
        self.root.title("音频同步诊断工具")
        self.root.geometry("800x600")
        
        self.video_path = None
        self.audio_path = None
        self.video_duration = 0
        self.audio_start_time = None
        self.video_start_time = None
        self.current_video_position = 0
        
        self.setup_ui()
        self.start_monitoring()
        
    def setup_ui(self):
        """设置界面"""
        # 文件选择
        file_frame = ttk.Frame(self.root)
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(file_frame, text="选择视频文件", 
                  command=self.select_video).pack(side=tk.LEFT, padx=(0, 10))
        
        self.file_label = ttk.Label(file_frame, text="未选择文件")
        self.file_label.pack(side=tk.LEFT)
        
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(control_frame, text="开始播放", 
                  command=self.start_playback).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="停止播放", 
                  command=self.stop_playback).pack(side=tk.LEFT, padx=(0, 5))
        
        # 跳转控制
        jump_frame = ttk.Frame(control_frame)
        jump_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        ttk.Label(jump_frame, text="跳转到(秒):").pack(side=tk.LEFT)
        self.jump_var = tk.DoubleVar(value=10.0)
        ttk.Entry(jump_frame, textvariable=self.jump_var, width=8).pack(side=tk.LEFT, padx=(5, 5))
        ttk.Button(jump_frame, text="跳转", command=self.jump_to_position).pack(side=tk.LEFT)
        
        # 实时状态显示
        status_frame = ttk.LabelFrame(self.root, text="实时同步状态")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.status_text = tk.Text(status_frame, height=20, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def log(self, message):
        """记录日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_message = f"[{timestamp}] {message}\n"
        self.status_text.insert(tk.END, log_message)
        self.status_text.see(tk.END)
        self.root.update()
        print(message)
        
    def select_video(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[("视频文件", "*.mp4 *.avi *.mov"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.video_path = file_path
            self.file_label.configure(text=os.path.basename(file_path))
            self.log(f"✅ 选择视频: {file_path}")
            
            # 获取视频信息
            if MOVIEPY_AVAILABLE:
                try:
                    video_clip = VideoFileClip(file_path)
                    self.video_duration = video_clip.duration
                    self.log(f"📹 视频时长: {self.video_duration:.2f} 秒")
                    video_clip.close()
                except Exception as e:
                    self.log(f"❌ 获取视频信息失败: {e}")
            
            # 提取音频
            self.extract_audio()
            
    def extract_audio(self):
        """提取音频"""
        if not self.video_path or not MOVIEPY_AVAILABLE:
            return
            
        def extract():
            try:
                self.log("🔊 开始提取音频...")
                
                import tempfile
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    self.audio_path = temp_file.name
                
                video_clip = VideoFileClip(self.video_path)
                if video_clip.audio:
                    audio_clip = video_clip.audio
                    audio_clip.write_audiofile(
                        self.audio_path,
                        verbose=False,
                        logger=None,
                        codec='pcm_s16le',
                        ffmpeg_params=['-ar', '44100', '-ac', '2']
                    )
                    
                    file_size = os.path.getsize(self.audio_path)
                    self.log(f"✅ 音频提取完成: {file_size} 字节")
                    
                    audio_clip.close()
                else:
                    self.log("❌ 视频没有音频轨道")
                    
                video_clip.close()
                
            except Exception as e:
                self.log(f"❌ 音频提取失败: {e}")
                
        threading.Thread(target=extract, daemon=True).start()
        
    def start_playback(self):
        """开始播放"""
        if not self.audio_path or not PYGAME_AVAILABLE:
            self.log("❌ 音频文件或pygame不可用")
            return
            
        try:
            self.log("🎬 开始播放")
            
            # 设置时间基准
            current_time = time.time()
            self.audio_start_time = current_time
            self.video_start_time = current_time
            self.current_video_position = 0
            
            # 播放音频
            pygame.mixer.music.load(self.audio_path)
            pygame.mixer.music.play()
            
            self.log(f"✅ 播放开始，基准时间: {current_time:.3f}")
            
        except Exception as e:
            self.log(f"❌ 播放失败: {e}")
            
    def stop_playback(self):
        """停止播放"""
        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.music.stop()
            self.log("⏹️ 播放已停止")
        except Exception as e:
            self.log(f"❌ 停止失败: {e}")
            
    def jump_to_position(self):
        """跳转到指定位置"""
        position = self.jump_var.get()
        
        if position >= self.video_duration:
            self.log(f"❌ 跳转位置超出视频长度: {position:.2f}s >= {self.video_duration:.2f}s")
            return
            
        try:
            self.log(f"🎯 跳转到: {position:.2f} 秒")
            
            # 停止当前播放
            if PYGAME_AVAILABLE:
                pygame.mixer.music.stop()
            
            # 重新设置时间基准
            current_time = time.time()
            self.audio_start_time = current_time  # 音频从现在开始播放（从头开始）
            self.video_start_time = current_time - position  # 视频的虚拟开始时间
            self.current_video_position = position
            
            # 重新播放音频
            pygame.mixer.music.load(self.audio_path)
            pygame.mixer.music.play()
            
            self.log(f"✅ 跳转完成")
            self.log(f"   音频开始时间: {self.audio_start_time:.3f}")
            self.log(f"   视频虚拟开始时间: {self.video_start_time:.3f}")
            self.log(f"   视频位置: {position:.2f}s")
            
        except Exception as e:
            self.log(f"❌ 跳转失败: {e}")
            
    def start_monitoring(self):
        """开始监控"""
        def monitor():
            while True:
                try:
                    if self.audio_start_time and self.video_start_time:
                        current_time = time.time()
                        
                        # 计算理论位置
                        audio_position = current_time - self.audio_start_time
                        video_position = current_time - self.video_start_time
                        
                        # 计算同步差异
                        sync_diff = abs(audio_position - video_position)
                        
                        # 检查音频播放状态
                        audio_playing = PYGAME_AVAILABLE and pygame.mixer.music.get_busy()
                        
                        # 更新状态（每秒一次）
                        status = f"🔄 音频位置: {audio_position:.2f}s | 视频位置: {video_position:.2f}s | 差异: {sync_diff:.2f}s | 音频播放: {audio_playing}"
                        
                        # 只在状态变化时记录
                        if hasattr(self, 'last_status') and self.last_status != status:
                            self.log(status)
                        self.last_status = status
                        
                    time.sleep(1)
                    
                except Exception as e:
                    self.log(f"❌ 监控错误: {e}")
                    time.sleep(1)
                    
        threading.Thread(target=monitor, daemon=True).start()

if __name__ == "__main__":
    root = tk.Tk()
    app = SyncDiagnosis(root)
    root.mainloop()
