#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频提取功能
验证不同的音频提取方法是否可用
"""

import os
import sys

def test_moviepy():
    """测试moviepy音频提取"""
    print("测试moviepy...")
    try:
        from moviepy.editor import VideoFileClip
        print("✓ moviepy 导入成功")
        
        # 如果有测试视频文件，可以尝试提取
        test_files = ["demo_video.mp4", "test.mp4", "sample.mp4"]
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"找到测试文件: {test_file}")
                try:
                    video = VideoFileClip(test_file)
                    if video.audio is not None:
                        print("✓ 视频包含音频轨道")
                        print(f"  视频时长: {video.duration:.2f}秒")
                        print(f"  音频采样率: {video.audio.fps}Hz")
                    else:
                        print("⚠️  视频不包含音频轨道")
                    video.close()
                    return True
                except Exception as e:
                    print(f"❌ 处理视频文件失败: {e}")
                    return False
        
        print("ℹ️  没有找到测试视频文件，但moviepy可用")
        return True
        
    except ImportError:
        print("❌ moviepy 未安装")
        print("   安装命令: pip install moviepy")
        return False
    except Exception as e:
        print(f"❌ moviepy 测试失败: {e}")
        return False

def test_ffmpeg():
    """测试ffmpeg"""
    print("\n测试ffmpeg...")
    try:
        import subprocess
        
        # 检查ffmpeg版本
        result = subprocess.run(
            ["ffmpeg", "-version"], 
            capture_output=True, 
            text=True, 
            timeout=5
        )
        
        if result.returncode == 0:
            print("✓ ffmpeg 可用")
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"  {version_line}")
            return True
        else:
            print("❌ ffmpeg 不可用")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ ffmpeg 响应超时")
        return False
    except FileNotFoundError:
        print("❌ ffmpeg 未找到")
        print("   请从 https://ffmpeg.org 下载并添加到PATH")
        return False
    except Exception as e:
        print(f"❌ ffmpeg 测试失败: {e}")
        return False

def test_basic_dependencies():
    """测试基础依赖"""
    print("\n测试基础依赖...")
    
    dependencies = [
        ("cv2", "opencv-python"),
        ("numpy", "numpy"),
        ("PIL", "pillow"),
        ("tkinter", "tkinter (内置)")
    ]
    
    all_ok = True
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package} 未安装")
            all_ok = False
    
    return all_ok

def create_test_video():
    """创建一个简单的测试视频"""
    print("\n创建测试视频...")
    try:
        import cv2
        import numpy as np
        
        # 创建一个简单的测试视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter('test_video.mp4', fourcc, 10.0, (320, 240))
        
        for i in range(30):  # 3秒，10fps
            # 创建彩色帧
            frame = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
            out.write(frame)
        
        out.release()
        print("✓ 测试视频创建成功: test_video.mp4")
        print("  注意: 此视频不包含音频轨道")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试视频失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("音频提取功能测试")
    print("=" * 50)
    
    # 测试基础依赖
    basic_ok = test_basic_dependencies()
    
    # 测试音频提取方法
    moviepy_ok = test_moviepy()
    ffmpeg_ok = test_ffmpeg()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    print(f"基础依赖: {'✓ 正常' if basic_ok else '❌ 有问题'}")
    print(f"moviepy: {'✓ 可用' if moviepy_ok else '❌ 不可用'}")
    print(f"ffmpeg: {'✓ 可用' if ffmpeg_ok else '❌ 不可用'}")
    
    if moviepy_ok or ffmpeg_ok:
        print("\n🎉 音频提取功能可用！")
        print("可以使用完整的字幕生成功能。")
    else:
        print("\n⚠️  音频提取功能不可用")
        print("\n解决方案:")
        print("1. 安装moviepy: pip install moviepy")
        print("2. 或安装ffmpeg: https://ffmpeg.org/download.html")
        print("3. 运行安装脚本: python install_audio_support.py")
    
    # 询问是否创建测试视频
    if not moviepy_ok and not ffmpeg_ok:
        print("\n是否创建测试视频文件？(y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes', '是']:
                create_test_video()
        except:
            pass
    
    print("\n测试完成。")

if __name__ == "__main__":
    main()
