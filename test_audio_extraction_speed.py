#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频提取速度优化
"""

import time
import os
import tempfile
from moviepy.editor import VideoFileClip

def test_audio_extraction_speed(video_path):
    """测试不同参数下的音频提取速度"""
    
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return
    
    print("🧪 测试音频提取速度优化")
    print("=" * 60)
    print(f"📹 视频文件: {video_path}")
    
    # 获取视频信息
    try:
        video_clip = VideoFileClip(video_path)
        duration = video_clip.duration
        print(f"📹 视频时长: {duration:.2f} 秒")
        video_clip.close()
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return
    
    # 测试配置
    test_configs = [
        {
            "name": "原始配置（WAV格式）",
            "suffix": ".wav",
            "codec": "pcm_s16le",
            "params": ['-ar', '44100', '-ac', '2']
        },
        {
            "name": "基础OGG配置",
            "suffix": ".ogg",
            "codec": "libvorbis",
            "params": ['-ar', '44100', '-ac', '2']
        },
        {
            "name": "优化OGG配置（质量4）",
            "suffix": ".ogg",
            "codec": "libvorbis",
            "params": ['-ar', '44100', '-ac', '2', '-q:a', '4', '-threads', '4']
        },
        {
            "name": "最优OGG配置（质量3，多线程）",
            "suffix": ".ogg",
            "codec": "libvorbis",
            "params": ['-ar', '44100', '-ac', '2', '-q:a', '3', '-threads', '0', '-compression_level', '4']
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n📋 测试: {config['name']}")
        print("-" * 40)
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=config['suffix'], delete=False) as temp_file:
                temp_path = temp_file.name
            
            # 开始计时
            start_time = time.time()
            
            # 提取音频
            video_clip = VideoFileClip(video_path)
            audio_clip = video_clip.audio
            
            if audio_clip:
                audio_clip.write_audiofile(
                    temp_path,
                    verbose=False,
                    logger=None,
                    codec=config['codec'],
                    ffmpeg_params=config['params']
                )
                
                # 结束计时
                end_time = time.time()
                extraction_time = end_time - start_time
                
                # 获取文件信息
                file_size = os.path.getsize(temp_path)
                file_size_mb = file_size / (1024 * 1024)
                
                # 计算速度
                speed_ratio = duration / extraction_time
                
                result = {
                    'name': config['name'],
                    'time': extraction_time,
                    'size': file_size,
                    'size_mb': file_size_mb,
                    'speed_ratio': speed_ratio
                }
                results.append(result)
                
                print(f"✅ 提取完成")
                print(f"   耗时: {extraction_time:.2f} 秒")
                print(f"   文件大小: {file_size_mb:.2f} MB")
                print(f"   速度比: {speed_ratio:.2f}x (>1.0表示比实时更快)")
                
                # 清理临时文件
                audio_clip.close()
                video_clip.close()
                os.unlink(temp_path)
                
            else:
                print("❌ 视频没有音频轨道")
                video_clip.close()
                os.unlink(temp_path)
                
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            try:
                os.unlink(temp_path)
            except:
                pass
    
    # 显示结果对比
    print("\n" + "=" * 60)
    print("📊 结果对比:")
    print("=" * 60)
    
    if results:
        print(f"{'配置':<25} {'耗时(秒)':<10} {'大小(MB)':<10} {'速度比':<8}")
        print("-" * 60)
        
        for result in results:
            print(f"{result['name']:<25} {result['time']:<10.2f} {result['size_mb']:<10.2f} {result['speed_ratio']:<8.2f}")
        
        # 找出最快的配置
        fastest = min(results, key=lambda x: x['time'])
        smallest = min(results, key=lambda x: x['size'])
        
        print("\n🏆 最佳配置:")
        print(f"   最快提取: {fastest['name']} ({fastest['time']:.2f}秒)")
        print(f"   最小文件: {smallest['name']} ({smallest['size_mb']:.2f}MB)")
        
        # 推荐配置
        print("\n💡 推荐配置:")
        print("   对于快速提取: 最优OGG配置（质量3，多线程）")
        print("   - 使用所有CPU线程加速")
        print("   - 适中的音质和文件大小")
        print("   - 支持pygame位置播放")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    # 使用默认测试视频
    test_video = "C:/Users/<USER>/Desktop/afb2568cff4e11edaa9f00163e046553.mp4"
    test_audio_extraction_speed(test_video)
