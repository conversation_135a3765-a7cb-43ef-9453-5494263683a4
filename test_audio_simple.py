#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单音频播放测试脚本
用于诊断音频播放问题
"""

import os
import sys
import time
import tempfile

def test_system_audio():
    """测试系统音频"""
    print("=== 系统音频测试 ===")
    
    # 测试winsound
    try:
        import winsound
        print("✅ winsound可用")
        
        # 播放系统提示音
        print("🔊 播放系统提示音...")
        winsound.MessageBeep(winsound.MB_OK)
        time.sleep(1)
        
        # 播放系统声音文件
        system_sound = r"C:\Windows\Media\chimes.wav"
        if os.path.exists(system_sound):
            print(f"🔊 播放系统声音文件: {system_sound}")
            winsound.PlaySound(system_sound, winsound.SND_FILENAME)
            print("✅ 系统声音播放完成")
        else:
            print("⚠️ 系统声音文件不存在")
            
    except Exception as e:
        print(f"❌ winsound测试失败: {e}")
    
    print()

def test_pygame_audio():
    """测试pygame音频"""
    print("=== pygame音频测试 ===")
    
    try:
        import pygame
        
        # 初始化pygame音频
        pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=1024)
        print("✅ pygame音频初始化成功")
        
        # 获取音频设备信息
        freq, size, channels = pygame.mixer.get_init()
        print(f"音频设备: {freq}Hz, {size}bit, {channels}声道")
        
        # 测试播放系统声音文件
        system_sound = r"C:\Windows\Media\chimes.wav"
        if os.path.exists(system_sound):
            print(f"🔊 使用pygame播放: {system_sound}")
            pygame.mixer.music.load(system_sound)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()
            
            # 等待播放完成
            while pygame.mixer.music.get_busy():
                time.sleep(0.1)
            
            print("✅ pygame音频播放完成")
        else:
            print("⚠️ 系统声音文件不存在")
        
        pygame.mixer.quit()
        
    except Exception as e:
        print(f"❌ pygame测试失败: {e}")
    
    print()

def test_video_audio():
    """测试视频音频提取和播放"""
    print("=== 视频音频测试 ===")
    
    # 检查测试视频文件
    test_video = "test_video.mp4"
    if not os.path.exists(test_video):
        print(f"❌ 测试视频文件不存在: {test_video}")
        return
    
    try:
        from moviepy.editor import VideoFileClip
        import pygame
        
        print(f"🔊 提取音频从: {test_video}")
        
        # 提取音频
        video_clip = VideoFileClip(test_video)
        audio_clip = video_clip.audio
        
        if audio_clip is None:
            print("❌ 视频文件没有音频轨道")
            return
        
        # 创建临时音频文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
            audio_path = temp_audio.name
        
        print(f"🔊 保存音频到: {audio_path}")
        
        # 提取音频（只提取前10秒）
        audio_clip = audio_clip.subclip(0, min(10, audio_clip.duration))
        audio_clip.write_audiofile(
            audio_path, 
            verbose=False, 
            logger=None,
            codec='pcm_s16le',
            ffmpeg_params=['-ar', '44100']
        )
        
        # 关闭文件
        video_clip.close()
        audio_clip.close()
        
        # 检查生成的文件
        if os.path.exists(audio_path):
            file_size = os.path.getsize(audio_path)
            print(f"✅ 音频提取完成，文件大小: {file_size} 字节")
            
            # 使用pygame播放
            pygame.mixer.init()
            pygame.mixer.music.load(audio_path)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()
            
            print("🔊 播放提取的音频...")
            print("⏰ 播放10秒钟...")
            
            # 等待播放完成或10秒
            start_time = time.time()
            while pygame.mixer.music.get_busy() and (time.time() - start_time) < 10:
                time.sleep(0.1)
            
            pygame.mixer.music.stop()
            pygame.mixer.quit()
            
            print("✅ 音频播放完成")
            
            # 清理临时文件
            try:
                os.unlink(audio_path)
                print("🧹 临时文件已清理")
            except:
                pass
        else:
            print("❌ 音频文件生成失败")
    
    except Exception as e:
        print(f"❌ 视频音频测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔊 音频播放诊断工具")
    print("=" * 50)
    
    # 检查系统音量提示
    print("📢 请确保：")
    print("1. 系统音量已开启且不是静音")
    print("2. 音频设备正常工作")
    print("3. 耳机或扬声器已连接")
    print()
    
    # 运行测试
    test_system_audio()
    test_pygame_audio()
    test_video_audio()
    
    print("=" * 50)
    print("🔊 音频诊断完成")
    print()
    print("如果听不到声音，请检查：")
    print("1. Windows音量混合器中Python的音量设置")
    print("2. 音频设备是否被其他程序占用")
    print("3. 音频驱动程序是否正常")

if __name__ == "__main__":
    main()
