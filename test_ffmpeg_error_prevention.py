#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FFmpeg错误预防机制
验证新的安全后端选择和错误处理
"""

import os
import sys
import tempfile
import cv2
import numpy as np
import time

def test_backend_priority():
    """测试后端优先级"""
    print("=" * 60)
    print("1. 测试后端优先级")
    print("=" * 60)
    
    # 创建测试视频
    temp_video = create_test_video()
    if not temp_video:
        return False
    
    try:
        # 模拟安全后端选择逻辑
        safe_backends = [
            ("MSMF", cv2.CAP_MSMF),
            ("DSHOW", cv2.CAP_DSHOW),
            ("默认", None),
        ]
        
        working_backends = []
        
        for backend_name, backend_flag in safe_backends:
            try:
                print(f"测试 {backend_name} 后端...")
                
                if backend_flag is not None:
                    cap = cv2.VideoCapture(temp_video, backend_flag)
                else:
                    # 设置环境变量避免FFMPEG
                    original_env = os.environ.get('OPENCV_VIDEOIO_PRIORITY_LIST', '')
                    os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'MSMF,DSHOW,V4L2'
                    
                    try:
                        cap = cv2.VideoCapture(temp_video)
                        if cap.isOpened():
                            backend_used = cap.getBackendName()
                            print(f"  默认后端实际使用: {backend_used}")
                            if 'FFMPEG' in backend_used.upper():
                                print("  ⚠️  仍使用FFMPEG，跳过")
                                cap.release()
                                continue
                    finally:
                        if original_env:
                            os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = original_env
                        else:
                            os.environ.pop('OPENCV_VIDEOIO_PRIORITY_LIST', None)
                
                if cap.isOpened():
                    # 测试读取
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        print(f"  ✓ {backend_name} 后端工作正常")
                        working_backends.append(backend_name)
                    else:
                        print(f"  ❌ {backend_name} 后端无法读取帧")
                    cap.release()
                else:
                    print(f"  ❌ {backend_name} 后端无法打开")
                    
            except Exception as e:
                print(f"  ❌ {backend_name} 后端异常: {e}")
        
        print(f"\n可用的安全后端: {len(working_backends)}")
        for backend in working_backends:
            print(f"  ✓ {backend}")
        
        return len(working_backends) > 0
        
    finally:
        if os.path.exists(temp_video):
            os.unlink(temp_video)

def test_ffmpeg_avoidance():
    """测试FFMPEG避免机制"""
    print("\n" + "=" * 60)
    print("2. 测试FFMPEG避免机制")
    print("=" * 60)
    
    temp_video = create_test_video()
    if not temp_video:
        return False
    
    try:
        # 测试环境变量设置
        print("测试环境变量设置...")
        
        original_env = os.environ.get('OPENCV_VIDEOIO_PRIORITY_LIST', '')
        os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'MSMF,DSHOW,V4L2'
        
        try:
            cap = cv2.VideoCapture(temp_video)
            if cap.isOpened():
                backend = cap.getBackendName()
                print(f"使用后端: {backend}")
                
                if 'FFMPEG' not in backend.upper():
                    print("✓ 成功避免使用FFMPEG")
                    cap.release()
                    return True
                else:
                    print("❌ 仍然使用FFMPEG")
                    cap.release()
                    return False
            else:
                print("❌ 无法打开视频")
                return False
                
        finally:
            if original_env:
                os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = original_env
            else:
                os.environ.pop('OPENCV_VIDEOIO_PRIORITY_LIST', None)
        
    finally:
        if os.path.exists(temp_video):
            os.unlink(temp_video)

def test_single_thread_ffmpeg():
    """测试单线程FFMPEG"""
    print("\n" + "=" * 60)
    print("3. 测试单线程FFMPEG")
    print("=" * 60)
    
    temp_video = create_test_video()
    if not temp_video:
        return False
    
    try:
        print("测试单线程FFMPEG设置...")
        
        # 保存原始环境变量
        original_threads = os.environ.get('OPENCV_FFMPEG_CAPTURE_OPTIONS', '')
        
        # 设置单线程选项
        os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1'
        
        try:
            cap = cv2.VideoCapture(temp_video, cv2.CAP_FFMPEG)
            
            if cap.isOpened():
                # 设置缓冲区
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                # 测试读取多帧
                frames_read = 0
                for i in range(10):
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        frames_read += 1
                    else:
                        break
                
                cap.release()
                
                if frames_read > 0:
                    print(f"✓ 单线程FFMPEG成功读取 {frames_read} 帧")
                    return True
                else:
                    print("❌ 单线程FFMPEG无法读取帧")
                    return False
            else:
                print("❌ 单线程FFMPEG无法打开")
                return False
                
        finally:
            # 恢复环境变量
            if original_threads:
                os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = original_threads
            else:
                os.environ.pop('OPENCV_FFMPEG_CAPTURE_OPTIONS', None)
        
    finally:
        if os.path.exists(temp_video):
            os.unlink(temp_video)

def test_error_detection():
    """测试FFmpeg错误检测"""
    print("\n" + "=" * 60)
    print("4. 测试FFmpeg错误检测")
    print("=" * 60)
    
    # 模拟FFmpeg错误消息
    test_errors = [
        "Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173",
        "libavcodec error: cannot decode frame",
        "pthread_frame.c: assertion failed",
        "ffmpeg: error while decoding",
        "avcodec_receive_frame failed",
        "Unknown C++ exception from OpenCV",  # 非FFmpeg错误
    ]
    
    def detect_ffmpeg_error(error_msg):
        """检测是否是FFmpeg错误"""
        ffmpeg_indicators = ["libavcodec", "pthread_frame", "async_lock", "ffmpeg", "avcodec"]
        return any(indicator in error_msg.lower() for indicator in ffmpeg_indicators)
    
    passed = 0
    for error_msg in test_errors:
        is_ffmpeg = detect_ffmpeg_error(error_msg)
        
        # 前5个应该被检测为FFmpeg错误，最后一个不是
        expected = test_errors.index(error_msg) < 5
        
        if is_ffmpeg == expected:
            status = "FFmpeg错误" if is_ffmpeg else "非FFmpeg错误"
            print(f"✓ '{error_msg[:40]}...' → {status}")
            passed += 1
        else:
            status = "FFmpeg错误" if is_ffmpeg else "非FFmpeg错误"
            expected_status = "FFmpeg错误" if expected else "非FFmpeg错误"
            print(f"❌ '{error_msg[:40]}...' → {status} (期望: {expected_status})")
    
    print(f"\nFFmpeg错误检测测试: {passed}/{len(test_errors)} 通过")
    return passed == len(test_errors)

def test_resource_cleanup():
    """测试资源清理"""
    print("\n" + "=" * 60)
    print("5. 测试资源清理")
    print("=" * 60)
    
    temp_video = create_test_video()
    if not temp_video:
        return False
    
    try:
        print("测试VideoCapture资源清理...")
        
        # 创建多个VideoCapture
        caps = []
        for i in range(3):
            try:
                cap = cv2.VideoCapture(temp_video, cv2.CAP_MSMF)
                if cap.isOpened():
                    caps.append(cap)
                    print(f"  创建VideoCapture {i+1}")
            except Exception as e:
                print(f"  创建VideoCapture {i+1} 失败: {e}")
        
        print(f"成功创建 {len(caps)} 个VideoCapture")
        
        # 强制释放资源
        for i, cap in enumerate(caps):
            try:
                # 多次释放测试
                for _ in range(3):
                    cap.release()
                    time.sleep(0.01)
                print(f"  ✓ VideoCapture {i+1} 释放成功")
            except Exception as e:
                print(f"  ❌ VideoCapture {i+1} 释放失败: {e}")
        
        # 垃圾回收
        import gc
        gc.collect()
        
        print("✓ 资源清理测试完成")
        return True
        
    finally:
        if os.path.exists(temp_video):
            os.unlink(temp_video)

def create_test_video():
    """创建测试视频"""
    try:
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video_path = temp_video.name
        temp_video.close()
        
        # 创建简单视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_path, fourcc, 30, (320, 240))
        
        if not out.isOpened():
            return None
        
        # 生成20帧
        for i in range(20):
            frame = np.zeros((240, 320, 3), dtype=np.uint8)
            frame[:, :] = [i * 12 % 256, (i * 8) % 256, (i * 4) % 256]
            cv2.putText(frame, f'Frame {i}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            out.write(frame)
        
        out.release()
        return temp_video_path
        
    except Exception as e:
        print(f"创建测试视频失败: {e}")
        return None

def main():
    """主函数"""
    print("FFmpeg错误预防机制测试")
    print("验证新的安全后端选择和错误处理")
    
    tests = [
        ("后端优先级", test_backend_priority),
        ("FFMPEG避免机制", test_ffmpeg_avoidance),
        ("单线程FFMPEG", test_single_thread_ffmpeg),
        ("FFmpeg错误检测", test_error_detection),
        ("资源清理", test_resource_cleanup)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！FFmpeg错误预防机制正常。")
        print("\n预防特性:")
        print("✅ 优先使用安全后端（MSMF、DSHOW）")
        print("✅ 避免使用FFMPEG后端")
        print("✅ 单线程FFMPEG作为最后选择")
        print("✅ 智能错误检测和分类")
        print("✅ 强化资源清理机制")
    else:
        print("⚠️  部分测试失败，可能仍存在风险。")
        print("\n建议:")
        print("• 确保使用Windows系统（MSMF支持）")
        print("• 检查OpenCV版本和编译选项")
        print("• 考虑更新视频驱动程序")

if __name__ == "__main__":
    main()
