#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FFmpeg错误修复
验证针对 "Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173" 的修复
"""

import os
import sys
import tempfile
import cv2
import numpy as np
import time

def test_opencv_backends():
    """测试不同的OpenCV后端"""
    print("=" * 60)
    print("1. 测试OpenCV后端")
    print("=" * 60)
    
    # 创建简单的测试视频
    temp_video = create_simple_test_video()
    if not temp_video:
        print("❌ 无法创建测试视频")
        return False
    
    try:
        backends = [
            ("默认后端", cv2.VideoCapture, [temp_video]),
            ("MSMF后端", cv2.VideoCapture, [temp_video, cv2.CAP_MSMF]),
            ("DSHOW后端", cv2.VideoCapture, [temp_video, cv2.CAP_DSHOW]),
            ("FFMPEG后端", cv2.VideoCapture, [temp_video, cv2.CAP_FFMPEG]),
        ]
        
        working_backends = []
        
        for backend_name, backend_func, args in backends:
            try:
                print(f"测试 {backend_name}...")
                cap = backend_func(*args)
                
                if cap.isOpened():
                    # 尝试读取几帧
                    frames_read = 0
                    for i in range(5):
                        ret, frame = cap.read()
                        if ret and frame is not None:
                            frames_read += 1
                        else:
                            break
                    
                    cap.release()
                    
                    if frames_read > 0:
                        print(f"  ✓ {backend_name} 成功 (读取 {frames_read} 帧)")
                        working_backends.append(backend_name)
                    else:
                        print(f"  ❌ {backend_name} 无法读取帧")
                else:
                    print(f"  ❌ {backend_name} 无法打开视频")
                    
            except Exception as e:
                print(f"  ❌ {backend_name} 异常: {e}")
        
        print(f"\n可用后端: {len(working_backends)}/{len(backends)}")
        for backend in working_backends:
            print(f"  ✓ {backend}")
        
        return len(working_backends) > 0
        
    finally:
        # 清理测试文件
        if os.path.exists(temp_video):
            os.unlink(temp_video)

def test_single_thread_capture():
    """测试单线程VideoCapture"""
    print("\n" + "=" * 60)
    print("2. 测试单线程VideoCapture")
    print("=" * 60)
    
    temp_video = create_simple_test_video()
    if not temp_video:
        return False
    
    try:
        # 测试设置单线程环境变量
        original_env = os.environ.get('OPENCV_FFMPEG_CAPTURE_OPTIONS', '')
        
        print("设置单线程环境变量...")
        os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1'
        
        try:
            cap = cv2.VideoCapture(temp_video, cv2.CAP_FFMPEG)
            
            if cap.isOpened():
                # 设置缓冲区大小
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                # 测试读取
                frames_read = 0
                for i in range(10):
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        frames_read += 1
                    else:
                        break
                
                cap.release()
                
                if frames_read > 0:
                    print(f"✓ 单线程模式成功 (读取 {frames_read} 帧)")
                    return True
                else:
                    print("❌ 单线程模式无法读取帧")
                    return False
            else:
                print("❌ 单线程模式无法打开视频")
                return False
                
        finally:
            # 恢复环境变量
            if original_env:
                os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = original_env
            else:
                os.environ.pop('OPENCV_FFMPEG_CAPTURE_OPTIONS', None)
        
    finally:
        if os.path.exists(temp_video):
            os.unlink(temp_video)

def test_error_detection():
    """测试错误检测机制"""
    print("\n" + "=" * 60)
    print("3. 测试错误检测机制")
    print("=" * 60)
    
    # 模拟各种FFmpeg错误消息
    test_errors = [
        ("Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173", "FFmpeg多线程错误"),
        ("libavcodec error: cannot decode frame", "FFmpeg编解码器错误"),
        ("Unknown C++ exception from OpenCV code", "通用OpenCV错误"),
        ("cv::Mat assertion failed", "OpenCV Mat错误"),
        ("Bad argument in VideoCapture", "参数错误"),
    ]
    
    # 错误检测逻辑（从播放器中提取）
    def detect_error_type(error_msg):
        opencv_indicators = [
            "OpenCV", "C++", "cv::", "Mat", "VideoCapture",
            "Unknown C++ exception", "assertion failed",
            "Bad argument", "Unsupported format",
            "libavcodec", "pthread_frame", "async_lock",
            "ffmpeg", "avcodec", "avformat"
        ]
        
        ffmpeg_indicators = ["libavcodec", "pthread_frame", "async_lock", "ffmpeg", "avcodec"]
        
        is_opencv_error = any(indicator in error_msg for indicator in opencv_indicators)
        is_ffmpeg_error = any(indicator in error_msg.lower() for indicator in ffmpeg_indicators)
        
        if is_opencv_error and is_ffmpeg_error:
            return "FFmpeg相关错误"
        elif is_opencv_error:
            return "OpenCV错误"
        else:
            return "其他错误"
    
    passed = 0
    for error_msg, expected_type in test_errors:
        detected_type = detect_error_type(error_msg)
        
        if "FFmpeg" in expected_type and "FFmpeg" in detected_type:
            print(f"✓ '{error_msg[:50]}...' → {detected_type}")
            passed += 1
        elif "OpenCV" in expected_type and "OpenCV" in detected_type:
            print(f"✓ '{error_msg[:50]}...' → {detected_type}")
            passed += 1
        else:
            print(f"❌ '{error_msg[:50]}...' → {detected_type} (期望: {expected_type})")
    
    print(f"\n错误检测测试: {passed}/{len(test_errors)} 通过")
    return passed == len(test_errors)

def test_resource_cleanup():
    """测试资源清理"""
    print("\n" + "=" * 60)
    print("4. 测试资源清理")
    print("=" * 60)
    
    temp_video = create_simple_test_video()
    if not temp_video:
        return False
    
    try:
        # 创建多个VideoCapture对象并释放
        caps = []
        
        print("创建多个VideoCapture对象...")
        for i in range(5):
            try:
                cap = cv2.VideoCapture(temp_video)
                if cap.isOpened():
                    caps.append(cap)
                    print(f"  ✓ VideoCapture {i+1} 创建成功")
                else:
                    print(f"  ❌ VideoCapture {i+1} 创建失败")
            except Exception as e:
                print(f"  ❌ VideoCapture {i+1} 异常: {e}")
        
        print(f"\n成功创建 {len(caps)} 个VideoCapture对象")
        
        # 释放所有对象
        print("释放所有VideoCapture对象...")
        for i, cap in enumerate(caps):
            try:
                cap.release()
                print(f"  ✓ VideoCapture {i+1} 释放成功")
            except Exception as e:
                print(f"  ❌ VideoCapture {i+1} 释放失败: {e}")
        
        # 强制垃圾回收
        import gc
        gc.collect()
        
        print("✓ 资源清理测试完成")
        return True
        
    finally:
        if os.path.exists(temp_video):
            os.unlink(temp_video)

def create_simple_test_video():
    """创建简单的测试视频"""
    try:
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video_path = temp_video.name
        temp_video.close()
        
        # 创建简单的测试视频
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_path, fourcc, 30, (320, 240))
        
        if not out.isOpened():
            return None
        
        # 生成30帧简单内容
        for i in range(30):
            frame = np.zeros((240, 320, 3), dtype=np.uint8)
            frame[:, :] = [i * 8 % 256, (i * 4) % 256, (i * 2) % 256]
            cv2.putText(frame, f'Frame {i}', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            out.write(frame)
        
        out.release()
        return temp_video_path
        
    except Exception as e:
        print(f"创建测试视频失败: {e}")
        return None

def main():
    """主函数"""
    print("FFmpeg错误修复测试")
    print("针对: Assertion fctx->async_lock failed at libavcodec/pthread_frame.c:173")
    
    tests = [
        ("OpenCV后端测试", test_opencv_backends),
        ("单线程VideoCapture", test_single_thread_capture),
        ("错误检测机制", test_error_detection),
        ("资源清理", test_resource_cleanup)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！FFmpeg错误修复应该有效。")
        print("\n修复特性:")
        print("✅ 多种OpenCV后端支持")
        print("✅ 单线程VideoCapture模式")
        print("✅ FFmpeg错误智能检测")
        print("✅ 资源清理和恢复机制")
    else:
        print("⚠️  部分测试失败，可能仍存在问题。")

if __name__ == "__main__":
    main()
