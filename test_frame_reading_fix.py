#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试帧读取修复
验证 "无法读取帧，可能到达视频末尾" 问题的修复效果
"""

import os
import sys
import tempfile
import cv2
import numpy as np
import time

def create_test_video_with_issues():
    """创建一个可能有问题的测试视频"""
    print("创建测试视频（模拟可能的问题）...")
    
    try:
        # 创建临时视频文件
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video_path = temp_video.name
        temp_video.close()
        
        # 视频参数
        width, height = 320, 240  # 较小的分辨率
        fps = 15  # 较低的帧率
        duration = 3  # 3秒，较短的视频
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频写入器")
            return None
        
        # 生成测试帧，包含一些特殊情况
        total_frames = fps * duration
        for i in range(total_frames):
            # 创建不同类型的帧
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            if i < 5:
                # 前几帧：纯色
                frame[:, :] = [100, 150, 200]
            elif i < 10:
                # 中间几帧：渐变
                gradient = int((i - 5) * 255 / 5)
                frame[:, :] = [gradient, 255 - gradient, 128]
            else:
                # 后面的帧：随机噪声
                frame = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
            
            # 添加帧编号
            cv2.putText(frame, f'{i+1}/{total_frames}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✓ 测试视频创建成功: {temp_video_path}")
        print(f"  分辨率: {width}x{height}, 帧率: {fps}, 总帧数: {total_frames}")
        return temp_video_path
        
    except Exception as e:
        print(f"❌ 创建测试视频失败: {e}")
        return None

def test_frame_reading_robustness(video_path):
    """测试帧读取的鲁棒性"""
    print(f"\n测试帧读取鲁棒性: {video_path}")
    
    if not video_path or not os.path.exists(video_path):
        print("❌ 测试视频文件不存在")
        return False
    
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return False
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"  视频信息: {total_frames}帧, {fps}fps")
        
        # 测试1: 顺序读取所有帧
        print("  测试1: 顺序读取所有帧")
        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        frames_read = 0
        failed_reads = 0
        
        for i in range(total_frames + 5):  # 多读几帧测试边界
            ret, frame = cap.read()
            current_pos = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
            
            if ret and frame is not None:
                frames_read += 1
                if i % 10 == 0:
                    print(f"    读取帧 {i}: 成功 (位置: {current_pos})")
            else:
                failed_reads += 1
                print(f"    读取帧 {i}: 失败 (位置: {current_pos})")
                
                # 模拟我们的恢复逻辑
                if i < total_frames - 1:  # 如果不是真的到了末尾
                    print(f"    尝试恢复: 跳转到帧 {max(0, i-2)}")
                    cap.set(cv2.CAP_PROP_POS_FRAMES, max(0, i-2))
                    ret2, frame2 = cap.read()
                    if ret2:
                        print(f"    恢复成功")
                        frames_read += 1
                    else:
                        print(f"    恢复失败")
        
        print(f"  结果: 成功读取 {frames_read} 帧, 失败 {failed_reads} 次")
        
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ 帧读取测试失败: {e}")
        return False

def test_seeking_edge_cases(video_path):
    """测试跳转的边界情况"""
    print(f"\n测试跳转边界情况: {video_path}")
    
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return False
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 测试各种跳转位置
        test_positions = [
            -5,  # 负数
            0,   # 开始
            total_frames // 4,  # 1/4位置
            total_frames // 2,  # 中间
            total_frames * 3 // 4,  # 3/4位置
            total_frames - 1,  # 最后一帧
            total_frames,      # 超出范围
            total_frames + 10  # 远超出范围
        ]
        
        for pos in test_positions:
            print(f"  测试跳转到位置: {pos}")
            
            # 边界检查（模拟我们的修复逻辑）
            safe_pos = pos
            if safe_pos < 0:
                safe_pos = 0
            elif safe_pos >= total_frames:
                safe_pos = max(0, total_frames - 1)
            
            success = cap.set(cv2.CAP_PROP_POS_FRAMES, safe_pos)
            actual_pos = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
            
            print(f"    原始位置: {pos}, 安全位置: {safe_pos}, 实际位置: {actual_pos}")
            
            # 尝试读取一帧
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"    ✓ 成功读取帧")
            else:
                print(f"    ❌ 读取帧失败")
        
        cap.release()
        print("✓ 跳转边界测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 跳转边界测试失败: {e}")
        return False

def test_continuous_playback_simulation(video_path):
    """模拟连续播放测试"""
    print(f"\n模拟连续播放测试: {video_path}")
    
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return False
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_delay = 1.0 / fps if fps > 0 else 1/30
        
        print(f"  模拟播放 {total_frames} 帧，帧率 {fps}")
        
        consecutive_errors = 0
        max_consecutive_errors = 5
        frames_played = 0
        
        start_time = time.time()
        
        while frames_played < total_frames and consecutive_errors < max_consecutive_errors:
            loop_start = time.time()
            
            ret, frame = cap.read()
            current_pos = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
            
            if ret and frame is not None:
                frames_played += 1
                consecutive_errors = 0
                
                if frames_played % 10 == 0:
                    elapsed = time.time() - start_time
                    print(f"    播放进度: {frames_played}/{total_frames} ({elapsed:.1f}s)")
            else:
                consecutive_errors += 1
                print(f"    读取失败 (位置: {current_pos}, 连续错误: {consecutive_errors})")
                
                # 模拟恢复逻辑
                if current_pos < total_frames - 1:
                    recovery_pos = max(0, current_pos - 2)
                    print(f"    尝试恢复到位置: {recovery_pos}")
                    cap.set(cv2.CAP_PROP_POS_FRAMES, recovery_pos)
            
            # 控制播放速度
            elapsed = time.time() - loop_start
            sleep_time = max(0, frame_delay - elapsed)
            if sleep_time > 0:
                time.sleep(min(sleep_time, 0.1))  # 最多等待0.1秒
        
        total_time = time.time() - start_time
        print(f"  播放完成: {frames_played}/{total_frames} 帧, 用时 {total_time:.1f}s")
        
        cap.release()
        
        if consecutive_errors >= max_consecutive_errors:
            print("  ⚠️  由于连续错误过多而停止")
            return False
        else:
            print("  ✓ 播放测试成功")
            return True
        
    except Exception as e:
        print(f"❌ 连续播放测试失败: {e}")
        return False

def main():
    """主函数"""
    print("帧读取修复测试")
    print("=" * 50)
    
    # 创建测试视频
    test_video_path = create_test_video_with_issues()
    if not test_video_path:
        print("无法创建测试视频，退出测试")
        return
    
    try:
        # 运行各项测试
        tests = [
            ("帧读取鲁棒性", lambda: test_frame_reading_robustness(test_video_path)),
            ("跳转边界情况", lambda: test_seeking_edge_cases(test_video_path)),
            ("连续播放模拟", lambda: test_continuous_playback_simulation(test_video_path))
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                if test_func():
                    passed_tests += 1
                    print(f"✓ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
        
        # 显示测试结果
        print("\n" + "=" * 50)
        print(f"测试结果: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！帧读取修复应该有效。")
        else:
            print("⚠️  部分测试失败，可能仍存在问题。")
        
    finally:
        # 清理测试文件
        if test_video_path and os.path.exists(test_video_path):
            try:
                os.unlink(test_video_path)
                print(f"\n清理测试文件: {test_video_path}")
            except:
                print(f"\n无法清理测试文件: {test_video_path}")

if __name__ == "__main__":
    main()
