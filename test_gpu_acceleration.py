#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPU加速字幕生成功能
"""

import time
import os

def check_gpu_availability():
    """检查GPU可用性"""
    print("🔍 检查GPU可用性...")
    print("=" * 50)
    
    gpu_available = False
    gpu_info = ""
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ CUDA可用")
            print(f"   GPU数量: {gpu_count}")
            print(f"   GPU名称: {gpu_name}")
            print(f"   GPU内存: {gpu_memory:.1f}GB")
            gpu_available = True
            gpu_info = f"{gpu_name} ({gpu_memory:.1f}GB)"
        else:
            print("❌ CUDA不可用")
    except ImportError:
        print("❌ PyTorch未安装，无法检测CUDA")
    except Exception as e:
        print(f"❌ CUDA检测失败: {e}")
    
    # 检查nvidia-smi
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ nvidia-smi可用")
            print("GPU信息:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GeForce' in line or 'RTX' in line or 'GTX' in line or 'Quadro' in line:
                    print(f"   {line.strip()}")
            if not gpu_available:
                gpu_available = True
                gpu_info = "NVIDIA GPU (通过nvidia-smi检测)"
        else:
            print("❌ nvidia-smi不可用")
    except FileNotFoundError:
        print("❌ nvidia-smi未找到")
    except Exception as e:
        print(f"❌ nvidia-smi检测失败: {e}")
    
    if not gpu_available:
        print("ℹ️ 未检测到可用GPU，将使用CPU")
        gpu_info = "CPU only"
    
    return gpu_available, gpu_info

def test_whisper_gpu_performance():
    """测试Whisper GPU性能"""
    print("\n🧪 测试Whisper GPU性能")
    print("=" * 50)
    
    try:
        from faster_whisper import WhisperModel
        print("✅ faster-whisper可用")
    except ImportError as e:
        print(f"❌ faster-whisper不可用: {e}")
        return
    
    gpu_available, gpu_info = check_gpu_availability()
    
    # 测试配置
    test_configs = []
    
    # CPU配置
    test_configs.append({
        "name": "CPU (int8)",
        "device": "cpu",
        "compute_type": "int8"
    })
    
    # GPU配置（如果可用）
    if gpu_available:
        test_configs.append({
            "name": "GPU (float16)",
            "device": "cuda",
            "compute_type": "float16"
        })
        test_configs.append({
            "name": "GPU (int8)",
            "device": "cuda", 
            "compute_type": "int8"
        })
    
    # 测试音频文件
    test_audio_files = [
        "C:/Windows/Media/Windows Logon.wav",
        "C:/Windows/Media/chimes.wav"
    ]
    
    test_audio = None
    for audio_file in test_audio_files:
        if os.path.exists(audio_file):
            test_audio = audio_file
            break
    
    if not test_audio:
        print("⚠️ 未找到测试音频文件，跳过性能测试")
        return
    
    print(f"🎤 使用测试音频: {os.path.basename(test_audio)}")
    
    results = []
    
    for config in test_configs:
        print(f"\n📋 测试配置: {config['name']}")
        print("-" * 30)
        
        try:
            # 加载模型
            print(f"🤖 加载large-v3模型 ({config['device']}, {config['compute_type']})...")
            start_time = time.time()
            
            model = WhisperModel(
                "large-v3",
                device=config['device'],
                compute_type=config['compute_type']
            )
            
            load_time = time.time() - start_time
            print(f"✅ 模型加载完成，耗时: {load_time:.2f}秒")
            
            # 测试转录
            print("🎤 开始转录测试...")
            start_time = time.time()
            
            segments, info = model.transcribe(
                test_audio,
                language="zh",
                beam_size=5,
                best_of=5,
                temperature=0.0,
                vad_filter=True,
                word_timestamps=True
            )
            
            # 处理结果
            segment_count = 0
            for segment in segments:
                segment_count += 1
                if segment_count >= 3:  # 只处理前3个片段
                    break
            
            transcribe_time = time.time() - start_time
            
            result = {
                'config': config['name'],
                'device': config['device'],
                'compute_type': config['compute_type'],
                'load_time': load_time,
                'transcribe_time': transcribe_time,
                'segments': segment_count,
                'language': info.language,
                'confidence': info.language_probability
            }
            results.append(result)
            
            print(f"✅ 转录完成")
            print(f"   转录时间: {transcribe_time:.2f}秒")
            print(f"   检测语言: {info.language} (置信度: {info.language_probability:.2f})")
            print(f"   片段数量: {segment_count}")
            
            # 清理模型
            del model
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            if "CUDA" in str(e) or "GPU" in str(e):
                print("   可能是GPU内存不足或CUDA版本不兼容")
            continue
    
    # 显示性能对比
    if len(results) > 1:
        print("\n📊 性能对比:")
        print("=" * 60)
        print(f"{'配置':<15} {'设备':<8} {'加载时间':<10} {'转录时间':<10} {'总时间':<10}")
        print("-" * 60)
        
        for result in results:
            total_time = result['load_time'] + result['transcribe_time']
            print(f"{result['config']:<15} {result['device']:<8} {result['load_time']:<10.2f} {result['transcribe_time']:<10.2f} {total_time:<10.2f}")
        
        # 计算加速比
        if len(results) >= 2:
            cpu_result = next((r for r in results if r['device'] == 'cpu'), None)
            gpu_results = [r for r in results if r['device'] == 'cuda']
            
            if cpu_result and gpu_results:
                print(f"\n🚀 GPU加速效果:")
                for gpu_result in gpu_results:
                    cpu_total = cpu_result['load_time'] + cpu_result['transcribe_time']
                    gpu_total = gpu_result['load_time'] + gpu_result['transcribe_time']
                    speedup = cpu_total / gpu_total if gpu_total > 0 else 0
                    print(f"   {gpu_result['config']}: {speedup:.2f}x 加速")
    
    print(f"\n💡 推荐配置:")
    print("• 如果有GPU: 使用GPU + float16获得最佳性能")
    print("• 如果只有CPU: 使用CPU + int8获得最佳兼容性")
    print("• large-v3模型提供最高质量的字幕识别")

def show_gpu_requirements():
    """显示GPU要求"""
    print("\n📋 GPU加速要求:")
    print("=" * 50)
    print("1. 硬件要求:")
    print("   • NVIDIA GPU (支持CUDA)")
    print("   • 至少4GB显存（推荐8GB+）")
    print("   • 计算能力3.5+")
    
    print("\n2. 软件要求:")
    print("   • CUDA Toolkit 11.2+")
    print("   • PyTorch with CUDA support")
    print("   • faster-whisper")
    
    print("\n3. 安装命令:")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    print("   pip install faster-whisper")
    
    print("\n4. 性能优势:")
    print("   • 模型加载速度提升2-5x")
    print("   • 转录速度提升3-10x")
    print("   • 支持更大的模型和批处理")

if __name__ == "__main__":
    # 检查GPU可用性
    gpu_available, gpu_info = check_gpu_availability()
    
    # 测试性能
    test_whisper_gpu_performance()
    
    # 显示要求
    show_gpu_requirements()
    
    print(f"\n✅ GPU测试完成")
    if gpu_available:
        print(f"🚀 GPU加速可用: {gpu_info}")
        print("现在可以在主程序中使用GPU加速生成字幕了！")
    else:
        print("💻 将使用CPU模式，仍然可以生成高质量字幕")
