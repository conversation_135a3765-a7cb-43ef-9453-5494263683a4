#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Whisper large-v3模型性能
"""

import time
import os

def test_large_v3_model():
    """测试large-v3模型加载和性能"""
    print("🧪 测试Whisper large-v3模型")
    print("=" * 60)
    
    try:
        from faster_whisper import WhisperModel
        print("✅ faster-whisper 可用")
    except ImportError as e:
        print(f"❌ faster-whisper 不可用: {e}")
        return False
    
    # 测试模型加载
    print("\n🤖 测试large-v3模型加载...")
    print("注意：首次下载约3GB，请耐心等待...")
    
    try:
        start_time = time.time()
        
        # 加载large-v3模型
        model = WhisperModel(
            "large-v3", 
            device="cpu",
            compute_type="int8"  # 使用int8量化节省内存
        )
        
        load_time = time.time() - start_time
        print(f"✅ large-v3模型加载成功，耗时: {load_time:.2f}秒")
        
        # 测试模型信息
        print(f"📊 模型信息:")
        print(f"   模型名称: large-v3")
        print(f"   设备: CPU")
        print(f"   计算类型: int8")
        print(f"   加载时间: {load_time:.2f}秒")
        
        # 测试音频识别（如果有测试音频）
        test_audio_files = [
            "C:/Windows/Media/Windows Logon.wav",
            "C:/Windows/Media/chimes.wav",
            "C:/Windows/Media/ding.wav"
        ]
        
        for audio_file in test_audio_files:
            if os.path.exists(audio_file):
                print(f"\n🎤 测试音频识别: {os.path.basename(audio_file)}")
                try:
                    start_time = time.time()
                    
                    # 使用最高质量参数
                    segments, info = model.transcribe(
                        audio_file,
                        language="zh",  # 中文
                        beam_size=10,   # 最大beam size
                        best_of=10,     # 最多候选
                        temperature=[0.0, 0.2, 0.4, 0.6, 0.8],  # 多温度采样
                        vad_filter=True,
                        vad_parameters=dict(
                            min_silence_duration_ms=200,
                            speech_pad_ms=400
                        ),
                        word_timestamps=True,
                        condition_on_previous_text=True,
                        compression_ratio_threshold=2.4,
                        log_prob_threshold=-1.0,
                        no_speech_threshold=0.6,
                        initial_prompt="以下是中文语音的准确转录："
                    )
                    
                    process_time = time.time() - start_time
                    
                    print(f"   处理时间: {process_time:.2f}秒")
                    print(f"   检测语言: {info.language} (置信度: {info.language_probability:.2f})")
                    print(f"   音频时长: {info.duration:.2f}秒")
                    
                    segment_count = 0
                    for segment in segments:
                        segment_count += 1
                        print(f"   片段 {segment_count}: {segment.start:.2f}s-{segment.end:.2f}s")
                        print(f"     文本: {segment.text}")
                        print(f"     平均概率: {segment.avg_logprob:.3f}")
                        print(f"     无语音概率: {segment.no_speech_prob:.3f}")
                        
                        if segment_count >= 3:  # 只显示前3个片段
                            break
                    
                    if segment_count == 0:
                        print("   ⚠️ 未检测到语音内容")
                    else:
                        print(f"   ✅ 成功识别 {segment_count} 个语音片段")
                        
                        # 计算处理速度
                        if info.duration > 0:
                            speed_ratio = info.duration / process_time
                            print(f"   🚀 处理速度: {speed_ratio:.2f}x 实时速度")
                    
                except Exception as e:
                    print(f"   ❌ 识别失败: {e}")
                break
        else:
            print("\n⚠️ 未找到测试音频文件，跳过识别测试")
        
        # 显示large-v3模型的优势
        print("\n💡 large-v3模型优势:")
        print("=" * 40)
        print("1. 最高准确性:")
        print("   • 最新的Whisper架构")
        print("   • 在多种语言上训练")
        print("   • 对中文支持最佳")
        
        print("\n2. 高级功能:")
        print("   • 多温度采样提高准确性")
        print("   • 上下文感知转录")
        print("   • 更好的标点符号处理")
        print("   • 改进的时间戳精度")
        
        print("\n3. 适用场景:")
        print("   • 专业字幕制作")
        print("   • 高质量转录需求")
        print("   • 多语言混合内容")
        print("   • 复杂音频环境")
        
        print("\n4. 性能考虑:")
        print("   • 模型较大（约3GB）")
        print("   • 处理时间较长")
        print("   • 内存需求较高")
        print("   • 建议用于离线处理")
        
        return True
        
    except Exception as e:
        print(f"❌ large-v3模型测试失败: {e}")
        
        if "memory" in str(e).lower():
            print("\n💡 内存不足解决方案:")
            print("1. 关闭其他应用程序释放内存")
            print("2. 使用较小的模型（如base或small）")
            print("3. 考虑使用GPU版本（如果有CUDA）")
        elif "network" in str(e).lower() or "download" in str(e).lower():
            print("\n💡 网络问题解决方案:")
            print("1. 检查网络连接")
            print("2. 使用VPN或代理")
            print("3. 稍后重试")
        
        return False

def compare_model_sizes():
    """比较不同模型大小"""
    print("\n📊 Whisper模型对比:")
    print("=" * 60)
    
    models_info = [
        {"name": "tiny", "size": "39MB", "speed": "最快", "accuracy": "较低", "use_case": "实时处理"},
        {"name": "base", "size": "74MB", "speed": "快", "accuracy": "中等", "use_case": "平衡使用"},
        {"name": "small", "size": "244MB", "speed": "中等", "accuracy": "较好", "use_case": "质量优先"},
        {"name": "medium", "size": "769MB", "speed": "较慢", "accuracy": "好", "use_case": "高质量"},
        {"name": "large-v3", "size": "3GB", "speed": "最慢", "accuracy": "最高", "use_case": "专业级"}
    ]
    
    print(f"{'模型':<12} {'大小':<8} {'速度':<8} {'准确性':<8} {'适用场景':<12}")
    print("-" * 60)
    
    for model in models_info:
        print(f"{model['name']:<12} {model['size']:<8} {model['speed']:<8} {model['accuracy']:<8} {model['use_case']:<12}")
    
    print("\n🎯 推荐选择:")
    print("• 实时字幕: tiny 或 base")
    print("• 日常使用: base 或 small") 
    print("• 高质量字幕: large-v3")
    print("• 专业制作: large-v3")

if __name__ == "__main__":
    # 测试large-v3模型
    success = test_large_v3_model()
    
    # 显示模型对比
    compare_model_sizes()
    
    if success:
        print("\n✅ large-v3模型测试完成")
        print("现在可以在主程序中使用large-v3模型生成最高质量的字幕了！")
    else:
        print("\n❌ large-v3模型测试失败")
        print("建议使用较小的模型或检查系统资源")
