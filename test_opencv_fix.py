#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OpenCV C++异常修复
验证修复后的播放器是否能正常处理视频文件
"""

import os
import sys
import tempfile
import cv2
import numpy as np

def create_test_video():
    """创建测试视频文件"""
    print("创建测试视频文件...")
    
    try:
        # 创建临时视频文件
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video_path = temp_video.name
        temp_video.close()
        
        # 视频参数
        width, height = 640, 480
        fps = 30
        duration = 5  # 5秒
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建视频写入器")
            return None
        
        # 生成测试帧
        total_frames = fps * duration
        for i in range(total_frames):
            # 创建彩色渐变帧
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # 创建动态效果
            t = i / total_frames
            r = int(255 * abs(np.sin(t * np.pi * 2)))
            g = int(255 * abs(np.cos(t * np.pi * 2)))
            b = int(255 * abs(np.sin(t * np.pi * 4)))
            
            frame[:, :] = [b, g, r]  # BGR格式
            
            # 添加移动的圆形
            center_x = int(width * (0.5 + 0.3 * np.sin(t * np.pi * 4)))
            center_y = int(height * (0.5 + 0.3 * np.cos(t * np.pi * 4)))
            cv2.circle(frame, (center_x, center_y), 50, (255, 255, 255), -1)
            
            # 添加文字
            text = f'Frame {i+1}/{total_frames}'
            cv2.putText(frame, text, (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✓ 测试视频创建成功: {temp_video_path}")
        return temp_video_path
        
    except Exception as e:
        print(f"❌ 创建测试视频失败: {e}")
        return None

def test_video_reading_robustness(video_path):
    """测试视频读取的鲁棒性"""
    print(f"\n测试视频读取鲁棒性: {video_path}")
    
    if not video_path or not os.path.exists(video_path):
        print("❌ 测试视频文件不存在")
        return False
    
    try:
        # 测试多次打开和关闭
        for test_round in range(3):
            print(f"  测试轮次 {test_round + 1}/3")
            
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print(f"    ❌ 第{test_round + 1}轮: 无法打开视频")
                return False
            
            # 读取一些帧
            frames_read = 0
            for i in range(10):
                ret, frame = cap.read()
                if not ret:
                    break
                frames_read += 1
            
            print(f"    ✓ 第{test_round + 1}轮: 读取了 {frames_read} 帧")
            cap.release()
        
        print("✓ 视频读取鲁棒性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视频读取鲁棒性测试失败: {e}")
        return False

def test_frame_seeking(video_path):
    """测试帧跳转功能"""
    print(f"\n测试帧跳转功能: {video_path}")
    
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return False
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        print(f"  总帧数: {total_frames}")
        
        # 测试跳转到不同位置
        test_positions = [0, total_frames // 4, total_frames // 2, total_frames * 3 // 4]
        
        for pos in test_positions:
            if pos >= total_frames:
                continue
                
            cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
            ret, frame = cap.read()
            
            if ret:
                current_pos = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                print(f"  ✓ 跳转到帧 {pos}, 实际位置: {current_pos}")
            else:
                print(f"  ❌ 跳转到帧 {pos} 失败")
        
        cap.release()
        print("✓ 帧跳转测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 帧跳转测试失败: {e}")
        return False

def test_error_recovery():
    """测试错误恢复机制"""
    print("\n测试错误恢复机制...")
    
    try:
        # 测试打开不存在的文件
        cap = cv2.VideoCapture("nonexistent_file.mp4")
        is_opened = cap.isOpened()
        cap.release()
        
        if not is_opened:
            print("✓ 正确处理了不存在的文件")
        else:
            print("⚠️  意外地打开了不存在的文件")
        
        # 测试打开空文件
        temp_empty = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_empty_path = temp_empty.name
        temp_empty.close()
        
        cap = cv2.VideoCapture(temp_empty_path)
        is_opened = cap.isOpened()
        cap.release()
        
        # 清理
        os.unlink(temp_empty_path)
        
        if not is_opened:
            print("✓ 正确处理了空文件")
        else:
            print("⚠️  意外地打开了空文件")
        
        print("✓ 错误恢复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误恢复测试失败: {e}")
        return False

def main():
    """主函数"""
    print("OpenCV C++ 异常修复测试")
    print("=" * 50)
    
    # 创建测试视频
    test_video_path = create_test_video()
    if not test_video_path:
        print("无法创建测试视频，退出测试")
        return
    
    try:
        # 运行各项测试
        tests = [
            ("视频读取鲁棒性", lambda: test_video_reading_robustness(test_video_path)),
            ("帧跳转功能", lambda: test_frame_seeking(test_video_path)),
            ("错误恢复机制", test_error_recovery)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                if test_func():
                    passed_tests += 1
                    print(f"✓ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
        
        # 显示测试结果
        print("\n" + "=" * 50)
        print(f"测试结果: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！OpenCV修复应该有效。")
        else:
            print("⚠️  部分测试失败，可能仍存在问题。")
        
    finally:
        # 清理测试文件
        if test_video_path and os.path.exists(test_video_path):
            try:
                os.unlink(test_video_path)
                print(f"\n清理测试文件: {test_video_path}")
            except:
                print(f"\n无法清理测试文件: {test_video_path}")

if __name__ == "__main__":
    main()
