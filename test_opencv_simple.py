#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的OpenCV测试
诊断OpenCV视频加载问题
"""

import cv2
import os
import sys

def test_opencv_basic():
    """测试OpenCV基本功能"""
    print("OpenCV基本信息:")
    print(f"  版本: {cv2.__version__}")
    print(f"  构建信息: {cv2.getBuildInformation()[:200]}...")
    
    # 测试后端
    try:
        if hasattr(cv2, 'videoio_registry'):
            backends = cv2.videoio_registry.getBackends()
            print(f"  可用后端: {backends}")
        else:
            print("  无法获取后端信息")
    except Exception as e:
        print(f"  后端信息获取失败: {e}")

def test_video_file(video_path):
    """测试视频文件"""
    print(f"\n测试视频文件: {video_path}")
    
    if not os.path.exists(video_path):
        print("❌ 文件不存在")
        return False
    
    file_size = os.path.getsize(video_path)
    print(f"文件大小: {file_size:,} 字节")
    
    # 测试不同的方法
    methods = [
        ("默认", lambda: cv2.VideoCapture(video_path)),
        ("FFMPEG", lambda: cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)),
        ("MSMF", lambda: cv2.VideoCapture(video_path, cv2.CAP_MSMF)),
        ("DSHOW", lambda: cv2.VideoCapture(video_path, cv2.CAP_DSHOW)),
    ]
    
    for method_name, method_func in methods:
        print(f"\n尝试 {method_name}:")
        try:
            cap = method_func()
            
            if cap.isOpened():
                print(f"  ✓ 打开成功")
                
                # 获取属性
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                print(f"    分辨率: {width}x{height}")
                print(f"    帧率: {fps}")
                print(f"    总帧数: {frame_count}")
                
                # 尝试读取帧
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"    ✓ 成功读取帧: {frame.shape}")
                    cap.release()
                    return True
                else:
                    print(f"    ❌ 无法读取帧")
                
                cap.release()
            else:
                print(f"  ❌ 无法打开")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    return False

def create_simple_test_video():
    """创建最简单的测试视频"""
    print("\n创建最简单的测试视频...")
    
    import numpy as np
    
    output_path = "simple_test.avi"  # 使用AVI格式
    
    # 使用最基本的编码器
    fourcc = cv2.VideoWriter_fourcc(*'XVID')
    out = cv2.VideoWriter(output_path, fourcc, 20.0, (640, 480))
    
    if not out.isOpened():
        print("❌ 无法创建视频写入器")
        return None
    
    # 创建10帧简单内容
    for i in range(10):
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        frame[:, :] = [i * 25, 128, 255 - i * 25]
        
        # 添加简单文字
        cv2.putText(frame, f'Frame {i}', (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        out.write(frame)
    
    out.release()
    
    if os.path.exists(output_path):
        print(f"✓ 创建成功: {output_path}")
        return output_path
    else:
        print("❌ 创建失败")
        return None

def main():
    """主函数"""
    print("OpenCV视频加载诊断")
    print("=" * 50)
    
    # 1. 测试OpenCV基本功能
    test_opencv_basic()
    
    # 2. 创建简单测试视频
    simple_video = create_simple_test_video()
    
    # 3. 测试视频文件
    test_files = []
    
    if simple_video:
        test_files.append(simple_video)
    
    # 检查是否有其他测试文件
    if os.path.exists("test_video.mp4"):
        test_files.append("test_video.mp4")
    
    if len(sys.argv) > 1:
        test_files.append(sys.argv[1])
    
    # 测试所有文件
    for video_file in test_files:
        if test_video_file(video_file):
            print(f"\n🎉 {video_file} 测试成功！")
        else:
            print(f"\n❌ {video_file} 测试失败")
    
    print("\n诊断完成")

if __name__ == "__main__":
    main()
