#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播放器是否能正常运行
"""

try:
    print("正在导入模块...")
    import tkinter as tk
    print("✓ tkinter 导入成功")
    
    from tkinter import ttk, filedialog, messagebox
    print("✓ tkinter 子模块导入成功")
    
    import cv2
    print("✓ opencv-python 导入成功")
    
    import numpy as np
    print("✓ numpy 导入成功")
    
    from PIL import Image, ImageTk, ImageDraw, ImageFont
    print("✓ PIL 导入成功")
    
    import threading
    import time
    import os
    print("✓ 标准库导入成功")
    
    print("\n所有依赖模块导入成功！")
    
    # 测试创建简单窗口
    print("\n正在测试GUI创建...")
    root = tk.Tk()
    root.title("测试窗口")
    root.geometry("400x300")
    
    label = tk.Label(root, text="如果您看到这个窗口，说明播放器可以正常运行！\n\n点击关闭按钮退出测试。")
    label.pack(expand=True)
    
    def close_test():
        print("测试完成，关闭窗口")
        root.destroy()
    
    close_button = tk.Button(root, text="关闭测试", command=close_test)
    close_button.pack(pady=10)
    
    print("✓ GUI创建成功")
    print("\n测试窗口已打开，请检查是否正常显示")
    
    root.mainloop()
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有必要的依赖包")
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()
