#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播放器启动
检查程序是否能正常启动而不自动关闭
"""

import sys
import traceback
import time

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    
    try:
        import tkinter as tk
        print("✅ tkinter 导入成功")
    except Exception as e:
        print(f"❌ tkinter 导入失败: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV 导入成功 (版本: {cv2.__version__})")
    except Exception as e:
        print(f"❌ OpenCV 导入失败: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✅ PIL 导入成功")
    except Exception as e:
        print(f"❌ PIL 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy 导入成功 (版本: {np.__version__})")
    except Exception as e:
        print(f"❌ NumPy 导入失败: {e}")
        return False
    
    return True

def test_basic_tkinter():
    """测试基本的Tkinter功能"""
    print("\n🔍 测试基本Tkinter功能...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 创建基本窗口
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 添加一些基本组件
        label = tk.Label(root, text="测试标签")
        label.pack(pady=10)
        
        button = tk.Button(root, text="测试按钮", command=lambda: print("按钮被点击"))
        button.pack(pady=5)
        
        # 设置自动关闭
        def auto_close():
            print("✅ Tkinter基本功能测试通过")
            root.destroy()
        
        root.after(2000, auto_close)  # 2秒后自动关闭
        
        print("启动测试窗口（2秒后自动关闭）...")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Tkinter基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_player_class():
    """测试播放器类的创建"""
    print("\n🔍 测试播放器类创建...")
    
    try:
        # 导入播放器类
        sys.path.insert(0, '.')
        from mp4_player_with_subtitles import MP4PlayerWithSubtitles
        
        import tkinter as tk
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        print("创建播放器实例...")
        app = MP4PlayerWithSubtitles(root)
        
        print("✅ 播放器类创建成功")
        
        # 清理
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 播放器类创建失败: {e}")
        traceback.print_exc()
        return False

def test_player_startup():
    """测试播放器完整启动"""
    print("\n🔍 测试播放器完整启动...")
    
    try:
        # 导入播放器
        sys.path.insert(0, '.')
        from mp4_player_with_subtitles import MP4PlayerWithSubtitles
        
        import tkinter as tk
        
        # 创建根窗口
        root = tk.Tk()
        root.title("播放器启动测试")
        
        print("创建播放器实例...")
        app = MP4PlayerWithSubtitles(root)
        
        # 设置自动关闭
        def auto_close():
            print("✅ 播放器启动测试通过")
            try:
                app.on_closing()
            except:
                root.destroy()
        
        root.after(5000, auto_close)  # 5秒后自动关闭
        
        print("启动播放器（5秒后自动关闭）...")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 播放器启动测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("播放器启动测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("Tkinter基本功能", test_basic_tkinter),
        ("播放器类创建", test_player_class),
        ("播放器完整启动", test_player_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！播放器应该能正常启动")
    else:
        print("⚠️  部分测试失败，可能存在启动问题")
        
        # 提供建议
        print("\n建议:")
        if passed == 0:
            print("- 检查Python环境和依赖包安装")
            print("- 确保所有必要的包都已正确安装")
        else:
            print("- 检查失败的测试项目")
            print("- 查看详细的错误信息")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
