#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度条点击跳转功能
"""

import tkinter as tk
from tkinter import ttk
import time

class ProgressClickDemo:
    def __init__(self, root):
        self.root = root
        self.root.title("进度条点击跳转演示")
        self.root.geometry("800x400")
        
        # 模拟视频参数
        self.total_frames = 3000  # 模拟100秒的视频（30fps）
        self.fps = 30
        self.current_frame = 0
        self.is_seeking = False
        self.pause_progress_updates = False
        
        self.setup_ui()
        self.start_demo()
        
    def setup_ui(self):
        """设置界面"""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="进度条点击跳转功能演示", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_text = """
功能说明：
• 点击进度条任意位置可直接跳转到对应时间
• 拖动进度条可精确调整播放位置
• 支持实时预览拖动位置
• 松开鼠标时执行最终跳转
        """
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(pady=(0, 20))
        
        # 进度条框架
        progress_frame = ttk.LabelFrame(main_frame, text="视频进度控制")
        progress_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 时间显示
        self.time_var = tk.StringVar(value="00:00 / 01:40")
        time_label = ttk.Label(progress_frame, textvariable=self.time_var, font=("Arial", 12))
        time_label.pack(pady=10)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=self.total_frames-1,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_progress_drag)
        self.progress_bar.pack(fill=tk.X, padx=20, pady=10)
        
        # 绑定事件
        self.progress_bar.bind("<Button-1>", self.on_progress_click)
        self.progress_bar.bind("<B1-Motion>", self.on_progress_drag_motion)
        self.progress_bar.bind("<ButtonRelease-1>", self.on_progress_end_drag)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="操作状态")
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, font=("Arial", 10))
        status_label.pack(pady=10)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="播放/暂停", command=self.toggle_play).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="跳转到30秒", command=lambda: self.jump_to_time(30)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="跳转到60秒", command=lambda: self.jump_to_time(60)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="重置", command=self.reset_demo).pack(side=tk.LEFT)
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="操作日志")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        self.log_text = tk.Text(log_frame, height=8, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        print(message)
        
    def on_progress_click(self, event):
        """进度条点击事件"""
        widget = event.widget
        widget_width = widget.winfo_width()
        click_x = event.x
        
        if widget_width > 0:
            click_ratio = max(0, min(1, click_x / widget_width))
            target_frame = int(click_ratio * (self.total_frames - 1))
            target_time = target_frame / self.fps
            
            self.log(f"🔄 点击跳转: 位置{click_x}/{widget_width}, 目标{target_time:.1f}秒")
            
            # 立即跳转
            self.is_seeking = True
            self.pause_progress_updates = True
            self.progress_var.set(target_frame)
            self.perform_seek(target_frame)
            self.status_var.set(f"点击跳转到 {target_time:.1f}秒")
        
    def on_progress_drag_motion(self, event):
        """进度条拖动过程中"""
        if not self.is_seeking:
            self.is_seeking = True
            self.pause_progress_updates = True
            self.log("🔄 开始拖动进度条")
        
        widget = event.widget
        widget_width = widget.winfo_width()
        drag_x = max(0, min(widget_width, event.x))
        
        if widget_width > 0:
            drag_ratio = drag_x / widget_width
            target_frame = int(drag_ratio * (self.total_frames - 1))
            target_time = target_frame / self.fps
            
            self.progress_var.set(target_frame)
            self.update_time_display(target_frame)
            self.status_var.set(f"拖动中: {target_time:.1f}秒")

    def on_progress_end_drag(self, event):
        """进度条拖动结束"""
        widget = event.widget
        widget_width = widget.winfo_width()
        end_x = max(0, min(widget_width, event.x))
        
        if widget_width > 0:
            end_ratio = end_x / widget_width
            target_frame = int(end_ratio * (self.total_frames - 1))
            target_time = target_frame / self.fps
            
            self.log(f"🔄 拖动结束: 最终位置{target_time:.1f}秒")
            
            self.progress_var.set(target_frame)
            self.perform_seek(target_frame)
            self.status_var.set(f"拖动跳转到 {target_time:.1f}秒")
        
        self.is_seeking = False
        self.pause_progress_updates = False
        
    def on_progress_drag(self, value):
        """进度条值改变（兼容性）"""
        if not self.is_seeking:
            frame_number = int(float(value))
            self.perform_seek(frame_number)
            
    def perform_seek(self, target_frame):
        """执行跳转"""
        self.current_frame = target_frame
        self.update_time_display(target_frame)
        target_time = target_frame / self.fps
        self.log(f"✅ 跳转完成: 帧{target_frame}, 时间{target_time:.1f}秒")
        
    def update_time_display(self, frame):
        """更新时间显示"""
        current_time = frame / self.fps
        total_time = self.total_frames / self.fps
        self.time_var.set(f"{self.format_time(current_time)} / {self.format_time(total_time)}")
        
    def format_time(self, seconds):
        """格式化时间"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
        
    def toggle_play(self):
        """切换播放状态"""
        self.log("▶️ 播放/暂停切换")
        self.status_var.set("播放状态切换")
        
    def jump_to_time(self, seconds):
        """跳转到指定时间"""
        target_frame = int(seconds * self.fps)
        self.progress_var.set(target_frame)
        self.perform_seek(target_frame)
        self.log(f"🎯 快速跳转到 {seconds}秒")
        self.status_var.set(f"快速跳转到 {seconds}秒")
        
    def reset_demo(self):
        """重置演示"""
        self.current_frame = 0
        self.progress_var.set(0)
        self.update_time_display(0)
        self.log("🔄 演示重置")
        self.status_var.set("演示已重置")
        
    def start_demo(self):
        """开始演示"""
        self.log("🎬 进度条点击跳转演示开始")
        self.log("💡 提示: 点击进度条任意位置可直接跳转")
        self.log("💡 提示: 拖动进度条可精确调整位置")

if __name__ == "__main__":
    root = tk.Tk()
    demo = ProgressClickDemo(root)
    root.mainloop()
