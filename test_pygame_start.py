#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pygame.mixer.music.play(start=pos)功能
"""

import pygame
import time
import os

def test_pygame_start_parameter():
    """测试pygame的start参数"""
    print("🧪 测试pygame.mixer.music.play(start=pos)功能")
    print("=" * 50)
    
    # 初始化pygame
    try:
        pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=1024)
        print("✅ pygame初始化成功")
        print(f"   音频设置: {pygame.mixer.get_init()}")
    except Exception as e:
        print(f"❌ pygame初始化失败: {e}")
        return False
    
    # 查找测试音频文件
    test_files = [
        "C:/Windows/Media/Windows Logon.wav",
        "C:/Windows/Media/chimes.wav", 
        "C:/Windows/Media/ding.wav"
    ]
    
    test_file = None
    for file_path in test_files:
        if os.path.exists(file_path):
            test_file = file_path
            break
    
    if not test_file:
        print("❌ 没有找到测试音频文件")
        return False
    
    print(f"🔊 使用测试文件: {test_file}")
    
    # 测试1: 正常播放
    print("\n📋 测试1: 正常播放（从头开始）")
    try:
        pygame.mixer.music.load(test_file)
        pygame.mixer.music.play()
        print("✅ 正常播放开始")
        time.sleep(2)
        pygame.mixer.music.stop()
        print("✅ 正常播放停止")
    except Exception as e:
        print(f"❌ 正常播放失败: {e}")
    
    # 测试2: 使用start参数
    print("\n📋 测试2: 使用start参数（从1秒开始）")
    try:
        pygame.mixer.music.load(test_file)
        pygame.mixer.music.play(start=1.0)  # 从1秒开始播放
        print("✅ start参数播放开始")
        time.sleep(2)
        pygame.mixer.music.stop()
        print("✅ start参数播放停止")
    except Exception as e:
        print(f"❌ start参数播放失败: {e}")
        print(f"   错误详情: {type(e).__name__}: {e}")
    
    # 测试3: 检查pygame版本和功能
    print("\n📋 测试3: pygame版本信息")
    try:
        print(f"pygame版本: {pygame.version.ver}")
        print(f"SDL版本: {pygame.version.SDL}")
        
        # 检查play方法的参数
        import inspect
        play_signature = inspect.signature(pygame.mixer.music.play)
        print(f"play方法签名: {play_signature}")
        
    except Exception as e:
        print(f"❌ 获取版本信息失败: {e}")
    
    # 测试4: 尝试不同的start值
    print("\n📋 测试4: 测试不同的start值")
    start_positions = [0.5, 2.0, 0.1]
    
    for pos in start_positions:
        try:
            print(f"   测试start={pos}秒...")
            pygame.mixer.music.load(test_file)
            pygame.mixer.music.play(start=pos)
            time.sleep(1)
            pygame.mixer.music.stop()
            print(f"   ✅ start={pos}秒成功")
        except Exception as e:
            print(f"   ❌ start={pos}秒失败: {e}")
    
    print("\n" + "=" * 50)
    print("🔍 测试总结:")
    print("如果start参数测试都失败，说明当前pygame版本不支持start参数")
    print("需要使用其他方法实现位置播放")
    
    return True

if __name__ == "__main__":
    test_pygame_start_parameter()
    input("\n按回车键退出...")
