#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度条拖动pthread错误修复
"""

import os
import sys
import time

def setup_ultra_safe_seek_environment():
    """设置专门修复seek pthread错误的环境变量"""
    print("🔧 设置专门修复seek pthread错误的环境变量...")
    
    # 完全禁用多线程
    os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1|async;0|sync;1'
    os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
    os.environ['OPENCV_VIDEOIO_FFMPEG_THREAD_COUNT'] = '1'
    os.environ['FFMPEG_THREAD_SAFE'] = '0'  # 完全禁用线程安全
    
    # 禁用异步处理和seek缓冲
    os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
    os.environ['OPENCV_VIDEOIO_DISABLE_ASYNC'] = '1'
    os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'
    os.environ['OPENCV_FFMPEG_SEEK_BUFFER_SIZE'] = '1'
    os.environ['OPENCV_FFMPEG_DISABLE_SEEK_BUFFER'] = '1'
    
    # 禁用硬件加速
    os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'
    os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
    
    # 强制使用单线程解码
    os.environ['OPENCV_FFMPEG_THREAD_TYPE'] = '1'
    os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
    
    # 禁用MSMF
    os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
    os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
    
    # 设置更安全的缓冲区
    os.environ['OPENCV_FFMPEG_READ_ATTEMPTS'] = '1'
    os.environ['OPENCV_FFMPEG_CAPTURE_BUFFER_SIZE'] = '1'
    
    print("✅ 专门修复seek pthread错误的环境变量设置完成")

def test_seek_operations():
    """测试seek操作是否还有pthread错误"""
    setup_ultra_safe_seek_environment()
    
    import cv2
    
    video_path = "test_video_with_audio.mp4"
    if not os.path.exists(video_path):
        print(f"❌ 测试视频不存在: {video_path}")
        return
    
    print(f"🎬 测试seek操作: {video_path}")
    
    try:
        # 创建VideoCapture
        cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
        
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return
        
        print("✅ 视频文件打开成功")
        
        # 获取视频信息
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        print(f"📊 视频信息: {total_frames}帧, {fps}fps")
        
        # 测试多种seek操作
        seek_positions = [
            0,                    # 开始
            total_frames // 4,    # 25%
            total_frames // 2,    # 50%
            total_frames * 3 // 4, # 75%
            total_frames - 10,    # 接近结尾
            total_frames // 3,    # 33%
            total_frames * 2 // 3, # 66%
            10,                   # 接近开始
        ]
        
        print("🔄 开始测试seek操作...")
        
        for i, pos in enumerate(seek_positions):
            try:
                print(f"🔄 测试seek {i+1}/{len(seek_positions)}: 跳转到帧{pos}")
                
                # 执行seek操作
                cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                
                # 验证seek结果
                actual_pos = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                print(f"   目标位置: {pos}, 实际位置: {actual_pos}")
                
                # 尝试读取帧
                ret, frame = cap.read()
                if ret:
                    print(f"   ✅ 成功读取帧，大小: {frame.shape}")
                else:
                    print(f"   ⚠️ 读取帧失败")
                
                # 短暂延迟
                time.sleep(0.1)
                
            except Exception as e:
                print(f"   ❌ seek操作失败: {e}")
                if "async_lock" in str(e) or "pthread" in str(e):
                    print(f"   🚨 检测到pthread错误！")
                    return False
        
        print("✅ 所有seek操作测试完成，没有pthread错误")
        
        # 释放资源
        cap.release()
        print("✅ 资源释放完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        if "async_lock" in str(e) or "pthread" in str(e):
            print(f"🚨 检测到pthread错误！")
        return False

def test_rapid_seek():
    """测试快速连续seek操作"""
    setup_ultra_safe_seek_environment()
    
    import cv2
    
    video_path = "test_video_with_audio.mp4"
    if not os.path.exists(video_path):
        print(f"❌ 测试视频不存在: {video_path}")
        return
    
    print(f"🎬 测试快速连续seek操作: {video_path}")
    
    try:
        cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
        
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print("🔄 开始快速连续seek测试...")
        
        # 快速连续seek操作
        for i in range(20):
            try:
                # 随机位置
                import random
                pos = random.randint(0, total_frames - 1)
                
                print(f"🔄 快速seek {i+1}/20: 跳转到帧{pos}")
                cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                
                # 不等待，立即下一次seek
                
            except Exception as e:
                print(f"❌ 快速seek失败: {e}")
                if "async_lock" in str(e) or "pthread" in str(e):
                    print(f"🚨 检测到pthread错误！")
                    return False
        
        print("✅ 快速连续seek测试完成，没有pthread错误")
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ 快速seek测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 seek操作pthread错误修复测试")
    print("=" * 50)
    
    print("1. 测试基本seek操作")
    basic_result = test_seek_operations()
    
    print("\n2. 测试快速连续seek操作")
    rapid_result = test_rapid_seek()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果:")
    print(f"   基本seek操作: {'✅ 通过' if basic_result else '❌ 失败'}")
    print(f"   快速连续seek: {'✅ 通过' if rapid_result else '❌ 失败'}")
    
    if basic_result and rapid_result:
        print("\n🎉 所有测试通过！seek操作pthread错误已修复！")
        print("\n🚀 现在可以安全使用播放器的进度条拖动功能")
    else:
        print("\n⚠️ 部分测试失败，可能仍有pthread错误")
        print("\n🔧 建议:")
        print("1. 重启系统")
        print("2. 更新OpenCV到最新版本")
        print("3. 使用safe_player.py启动播放器")
    
    print("\n🔧 当前环境变量设置:")
    env_vars = [
        'OPENCV_FFMPEG_CAPTURE_OPTIONS',
        'OPENCV_VIDEOIO_FFMPEG_THREAD_COUNT',
        'OPENCV_FFMPEG_DISABLE_ASYNC',
        'OPENCV_FFMPEG_SEEK_BUFFER_SIZE'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"   {var} = {value}")

if __name__ == "__main__":
    main()
