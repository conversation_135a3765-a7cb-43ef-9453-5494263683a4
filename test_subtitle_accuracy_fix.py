#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字幕识别准确性修复
验证重复文本检测和Whisper参数优化
"""

import re

class SubtitleAccuracyTester:
    """字幕准确性测试器"""
    
    def __init__(self):
        self.subtitle_history = []
        self.max_history_size = 10
    
    def remove_repetitive_text(self, text):
        """检测和移除重复的文本内容"""
        if not text or len(text) < 4:
            return text
        
        try:
            # 检测重复的短语或句子
            words = text.split()
            if len(words) < 2:
                return text
            
            # 检测连续重复的词语
            cleaned_words = []
            prev_word = None
            repeat_count = 0
            
            for word in words:
                if word == prev_word:
                    repeat_count += 1
                    # 如果重复超过2次，跳过
                    if repeat_count <= 2:
                        cleaned_words.append(word)
                else:
                    cleaned_words.append(word)
                    repeat_count = 0
                prev_word = word
            
            # 检测重复的短语模式
            cleaned_text = ' '.join(cleaned_words)
            
            # 检测句子级别的重复
            sentences = self.split_into_sentences(cleaned_text)
            unique_sentences = []
            
            for sentence in sentences:
                sentence = sentence.strip()
                if sentence and sentence not in unique_sentences:
                    # 检查是否与已有句子过于相似
                    is_similar = False
                    for existing in unique_sentences:
                        if self.calculate_similarity(sentence, existing) > 0.8:
                            is_similar = True
                            break
                    
                    if not is_similar:
                        unique_sentences.append(sentence)
            
            result = '。'.join(unique_sentences)
            if result and not result.endswith(('。', '！', '？')):
                result += '。'
            
            return result if result else text
            
        except Exception as e:
            print(f"重复文本检测失败: {e}")
            return text

    def split_into_sentences(self, text):
        """将文本分割成句子"""
        # 按中文标点分割
        sentences = re.split(r'[。！？；]', text)
        return [s.strip() for s in sentences if s.strip()]

    def calculate_similarity(self, text1, text2):
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 简单的字符级相似度计算
        set1 = set(text1)
        set2 = set(text2)
        
        if not set1 and not set2:
            return 1.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0

    def is_duplicate_subtitle(self, text):
        """检查字幕是否与历史记录重复"""
        for history_text in self.subtitle_history:
            similarity = self.calculate_similarity(text, history_text)
            if similarity > 0.85:  # 85%以上相似度认为是重复
                return True
        return False

    def add_to_subtitle_history(self, text):
        """添加字幕到历史记录"""
        self.subtitle_history.append(text)
        if len(self.subtitle_history) > self.max_history_size:
            self.subtitle_history.pop(0)

def test_repetitive_text_removal():
    """测试重复文本移除"""
    print("=" * 60)
    print("1. 测试重复文本移除")
    print("=" * 60)
    
    tester = SubtitleAccuracyTester()
    
    test_cases = [
        # (输入, 期望输出描述)
        ("我认识你。我认识你。我认识你。我认识你。", "应该移除重复句子"),
        ("你好你好你好世界", "应该移除重复词语"),
        ("这是一个测试。这是一个测试。", "应该移除重复句子"),
        ("正常的字幕内容", "正常内容应该保持不变"),
        ("", "空文本应该返回空"),
        ("单个词", "单个词应该保持不变"),
        ("重复 重复 重复 的 词语", "应该限制重复次数"),
    ]
    
    passed = 0
    for input_text, description in test_cases:
        try:
            result = tester.remove_repetitive_text(input_text)
            
            # 检查结果是否比输入更简洁（对于重复文本）
            if "重复" in description:
                if len(result) < len(input_text):
                    print(f"✓ {description}")
                    print(f"  输入: '{input_text}'")
                    print(f"  输出: '{result}'")
                    passed += 1
                else:
                    print(f"❌ {description}")
                    print(f"  输入: '{input_text}'")
                    print(f"  输出: '{result}' (未能简化)")
            else:
                # 正常内容应该保持基本不变
                if result:
                    print(f"✓ {description}")
                    print(f"  输入: '{input_text}'")
                    print(f"  输出: '{result}'")
                    passed += 1
                else:
                    print(f"❌ {description}")
                    print(f"  输入: '{input_text}'")
                    print(f"  输出: '{result}' (意外为空)")
            
            print()
            
        except Exception as e:
            print(f"❌ {description}: 处理失败 - {e}")
    
    print(f"重复文本移除测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)

def test_duplicate_detection():
    """测试重复字幕检测"""
    print("\n" + "=" * 60)
    print("2. 测试重复字幕检测")
    print("=" * 60)
    
    tester = SubtitleAccuracyTester()
    
    # 添加一些历史字幕
    history_subtitles = [
        "你好世界",
        "这是一个测试",
        "我认识你",
        "今天天气很好"
    ]
    
    for subtitle in history_subtitles:
        tester.add_to_subtitle_history(subtitle)
    
    # 测试重复检测
    test_cases = [
        # (测试文本, 是否应该被检测为重复, 描述)
        ("你好世界", True, "完全相同的文本"),
        ("你好世界！", True, "基本相同但有标点差异"),
        ("我认识你。", True, "基本相同但有标点差异"),
        ("全新的字幕内容", False, "完全不同的内容"),
        ("你好", False, "部分相似但不足以判定为重复"),
        ("这是一个全新的测试", False, "有相似词但整体不同"),
    ]
    
    passed = 0
    for text, expected_duplicate, description in test_cases:
        try:
            is_duplicate = tester.is_duplicate_subtitle(text)
            
            if is_duplicate == expected_duplicate:
                print(f"✓ {description}: {is_duplicate}")
                passed += 1
            else:
                print(f"❌ {description}: {is_duplicate} (期望: {expected_duplicate})")
                
        except Exception as e:
            print(f"❌ {description}: 检测失败 - {e}")
    
    print(f"\n重复字幕检测测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)

def test_similarity_calculation():
    """测试相似度计算"""
    print("\n" + "=" * 60)
    print("3. 测试相似度计算")
    print("=" * 60)
    
    tester = SubtitleAccuracyTester()
    
    test_cases = [
        # (文本1, 文本2, 期望相似度范围, 描述)
        ("你好世界", "你好世界", (0.9, 1.0), "完全相同"),
        ("你好世界", "你好世界！", (0.8, 1.0), "基本相同"),
        ("你好", "世界", (0.0, 0.3), "完全不同"),
        ("我认识你", "我不认识你", (0.5, 0.8), "部分相似"),
        ("", "", (1.0, 1.0), "都为空"),
        ("测试", "", (0.0, 0.0), "一个为空"),
    ]
    
    passed = 0
    for text1, text2, (min_sim, max_sim), description in test_cases:
        try:
            similarity = tester.calculate_similarity(text1, text2)
            
            if min_sim <= similarity <= max_sim:
                print(f"✓ {description}: {similarity:.2f} (范围: {min_sim}-{max_sim})")
                passed += 1
            else:
                print(f"❌ {description}: {similarity:.2f} (期望范围: {min_sim}-{max_sim})")
                
        except Exception as e:
            print(f"❌ {description}: 计算失败 - {e}")
    
    print(f"\n相似度计算测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)

def test_whisper_parameters():
    """测试Whisper参数优化"""
    print("\n" + "=" * 60)
    print("4. 测试Whisper参数优化")
    print("=" * 60)
    
    # 模拟优化后的Whisper参数
    optimized_params = {
        'beam_size': 5,
        'language': 'zh',
        'word_timestamps': True,
        'initial_prompt': '以下是普通话的句子。',
        'condition_on_previous_text': False,  # 关键：禁用上下文依赖
        'temperature': 0.1,  # 关键：稍微增加随机性
        'compression_ratio_threshold': 2.4,
        'log_prob_threshold': -1.0,
        'no_speech_threshold': 0.6,
        'repetition_penalty': 1.1,  # 关键：添加重复惩罚
        'length_penalty': 1.0,
        'patience': 1,  # 关键：减少patience
        'suppress_blank': True,
        'vad_filter': True,  # 关键：启用语音活动检测
    }
    
    # 检查关键参数
    key_checks = [
        ('condition_on_previous_text', False, "禁用上下文依赖防止重复"),
        ('temperature', 0.1, "适当的随机性避免卡在重复模式"),
        ('repetition_penalty', 1.1, "添加重复惩罚"),
        ('patience', 1, "减少搜索深度"),
        ('vad_filter', True, "启用语音活动检测"),
        ('suppress_blank', True, "抑制空白输出"),
    ]
    
    passed = 0
    print("Whisper参数优化检查:")
    for param, expected, description in key_checks:
        if param in optimized_params and optimized_params[param] == expected:
            print(f"✓ {param}: {optimized_params[param]} - {description}")
            passed += 1
        else:
            actual = optimized_params.get(param, "未设置")
            print(f"❌ {param}: {actual} (期望: {expected}) - {description}")
    
    print(f"\nWhisper参数优化测试: {passed}/{len(key_checks)} 通过")
    return passed == len(key_checks)

def main():
    """主函数"""
    print("字幕识别准确性修复测试")
    print("验证重复文本检测和Whisper参数优化")
    
    tests = [
        ("重复文本移除", test_repetitive_text_removal),
        ("重复字幕检测", test_duplicate_detection),
        ("相似度计算", test_similarity_calculation),
        ("Whisper参数优化", test_whisper_parameters)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！字幕识别准确性修复功能正常。")
        print("\n修复特性:")
        print("✅ 重复文本检测和移除")
        print("✅ 字幕历史记录和去重")
        print("✅ 智能相似度计算")
        print("✅ Whisper参数优化")
        print("✅ 语音活动检测")
    else:
        print("⚠️  部分测试失败，需要进一步优化。")

if __name__ == "__main__":
    main()
