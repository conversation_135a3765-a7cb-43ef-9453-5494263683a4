#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字幕错误修复功能
验证各种字幕错误的处理和恢复机制
"""

import sys
import time
import threading
import queue

class MockSubtitleSystem:
    """模拟字幕系统用于测试"""
    
    def __init__(self):
        self.subtitle_buffer = queue.Queue()
        self.transcription_running = True
        self.current_subtitle_index = 0
    
    def process_subtitle_text(self, text):
        """模拟字幕文本处理"""
        if not text:
            return ""
        
        try:
            processed_text = text.strip()
            processed_text = ' '.join(processed_text.split())
            
            # 中文标点转换
            import re
            if re.search(r'[\u4e00-\u9fff]', processed_text):
                processed_text = processed_text.replace(',', '，')
                processed_text = processed_text.replace('.', '。')
                processed_text = processed_text.replace('?', '？')
                processed_text = processed_text.replace('!', '！')
            
            if len(processed_text) > 50:
                processed_text = processed_text[:47] + "..."
            
            return processed_text
            
        except Exception as e:
            print(f"文本处理错误: {e}")
            return text
    
    def validate_subtitle_data(self, subtitle_data):
        """验证字幕数据"""
        try:
            required_fields = ['text', 'start', 'end', 'index']
            for field in required_fields:
                if field not in subtitle_data:
                    return False
            
            if not subtitle_data['text'] or not subtitle_data['text'].strip():
                return False
            
            start_time = subtitle_data['start']
            end_time = subtitle_data['end']
            
            if start_time < 0 or end_time < 0:
                return False
            
            if start_time >= end_time:
                return False
            
            duration = end_time - start_time
            if duration > 30:
                return False
            
            return True
            
        except Exception:
            return False
    
    def safe_subtitle_processing(self, segment):
        """安全的字幕处理"""
        try:
            raw_text = getattr(segment, 'text', "")
            start_time = getattr(segment, 'start', 0.0)
            end_time = getattr(segment, 'end', start_time + 2.0)
            
            processed_text = self.process_subtitle_text(raw_text)
            
            subtitle_data = {
                'text': processed_text,
                'start': start_time,
                'end': end_time,
                'index': self.current_subtitle_index
            }
            
            if self.validate_subtitle_data(subtitle_data):
                return subtitle_data
            else:
                return None
                
        except Exception as e:
            print(f"字幕处理失败: {e}")
            return None

def test_subtitle_data_validation():
    """测试字幕数据验证"""
    print("=" * 60)
    print("1. 测试字幕数据验证")
    print("=" * 60)
    
    system = MockSubtitleSystem()
    
    # 测试用例
    test_cases = [
        # (数据, 是否应该通过, 描述)
        ({'text': '正常字幕', 'start': 0.0, 'end': 2.0, 'index': 0}, True, "正常数据"),
        ({'text': '', 'start': 0.0, 'end': 2.0, 'index': 0}, False, "空文本"),
        ({'text': '正常字幕', 'start': -1.0, 'end': 2.0, 'index': 0}, False, "负开始时间"),
        ({'text': '正常字幕', 'start': 2.0, 'end': 1.0, 'index': 0}, False, "时间顺序错误"),
        ({'text': '正常字幕', 'start': 0.0, 'end': 35.0, 'index': 0}, False, "持续时间过长"),
        ({'start': 0.0, 'end': 2.0, 'index': 0}, False, "缺少文本字段"),
        ({'text': '正常字幕', 'start': 0.0, 'end': 2.0, 'index': 0}, True, "正常数据2"),
    ]
    
    passed = 0
    for data, expected, description in test_cases:
        result = system.validate_subtitle_data(data)
        if result == expected:
            print(f"✓ {description}: {result}")
            passed += 1
        else:
            print(f"❌ {description}: {result} (期望: {expected})")
    
    print(f"\n数据验证测试: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)

def test_safe_subtitle_processing():
    """测试安全字幕处理"""
    print("\n" + "=" * 60)
    print("2. 测试安全字幕处理")
    print("=" * 60)
    
    system = MockSubtitleSystem()
    
    # 模拟字幕段对象
    class MockSegment:
        def __init__(self, text, start, end):
            self.text = text
            self.start = start
            self.end = end
    
    # 测试用例
    test_segments = [
        MockSegment("你好,世界!", 0.0, 2.0),
        MockSegment("", 2.0, 4.0),  # 空文本
        MockSegment("正常字幕内容", 4.0, 6.0),
        MockSegment("超长字幕内容" * 20, 6.0, 8.0),  # 超长文本
        MockSegment("测试字幕", -1.0, 1.0),  # 负时间
    ]
    
    processed = 0
    valid = 0
    
    for i, segment in enumerate(test_segments):
        try:
            result = system.safe_subtitle_processing(segment)
            processed += 1
            
            if result:
                valid += 1
                print(f"✓ 段落 {i+1}: '{segment.text[:20]}...' → 处理成功")
            else:
                print(f"⚠️  段落 {i+1}: '{segment.text[:20]}...' → 跳过（无效）")
                
        except Exception as e:
            print(f"❌ 段落 {i+1}: 处理异常 - {e}")
    
    print(f"\n安全处理测试: 处理 {processed}/{len(test_segments)}, 有效 {valid}")
    return processed == len(test_segments)

def test_error_recovery():
    """测试错误恢复机制"""
    print("\n" + "=" * 60)
    print("3. 测试错误恢复机制")
    print("=" * 60)
    
    # 模拟各种错误情况
    error_scenarios = [
        (FileNotFoundError("音频文件未找到"), "文件未找到错误"),
        (MemoryError("内存不足"), "内存错误"),
        (TimeoutError("转录超时"), "超时错误"),
        (ConnectionError("网络连接失败"), "网络错误"),
        (ValueError("无效参数"), "参数错误"),
    ]
    
    def handle_subtitle_generation_error(error):
        """模拟错误处理"""
        error_type = type(error).__name__
        error_msg = str(error)
        
        if "FileNotFoundError" in error_type:
            return "启动演示字幕"
        elif "MemoryError" in error_type or "memory" in error_msg.lower():
            return "建议使用更小模型"
        elif "TimeoutError" in error_type or "timeout" in error_msg.lower():
            return "启动演示字幕"
        elif "ConnectionError" in error_type or "network" in error_msg.lower():
            return "检查网络连接"
        else:
            return "启动演示字幕"
    
    handled = 0
    for error, description in error_scenarios:
        try:
            recovery_action = handle_subtitle_generation_error(error)
            print(f"✓ {description}: {recovery_action}")
            handled += 1
        except Exception as e:
            print(f"❌ {description}: 处理失败 - {e}")
    
    print(f"\n错误恢复测试: {handled}/{len(error_scenarios)} 处理成功")
    return handled == len(error_scenarios)

def test_subtitle_buffer_management():
    """测试字幕缓冲区管理"""
    print("\n" + "=" * 60)
    print("4. 测试字幕缓冲区管理")
    print("=" * 60)
    
    # 创建缓冲区
    buffer = queue.Queue(maxsize=10)
    
    # 测试数据
    test_data = [
        {'text': f'字幕{i}', 'start': i*2.0, 'end': (i+1)*2.0, 'index': i}
        for i in range(15)  # 超过缓冲区大小
    ]
    
    # 测试写入
    written = 0
    for data in test_data:
        try:
            buffer.put(data, timeout=0.1)
            written += 1
        except queue.Full:
            print(f"缓冲区满，已写入 {written} 项")
            break
    
    # 测试读取
    read = 0
    while not buffer.empty():
        try:
            item = buffer.get(timeout=0.1)
            read += 1
        except queue.Empty:
            break
    
    print(f"缓冲区测试: 写入 {written}, 读取 {read}")
    
    # 测试异常恢复
    try:
        # 清空队列
        while not buffer.empty():
            buffer.get_nowait()
        print("✓ 缓冲区清理成功")
        recovery_success = True
    except Exception as e:
        print(f"❌ 缓冲区清理失败: {e}")
        recovery_success = False
    
    return written > 0 and read > 0 and recovery_success

def test_thread_safety():
    """测试线程安全性"""
    print("\n" + "=" * 60)
    print("5. 测试线程安全性")
    print("=" * 60)
    
    shared_buffer = queue.Queue()
    results = {'produced': 0, 'consumed': 0, 'errors': 0}
    
    def producer():
        """生产者线程"""
        try:
            for i in range(20):
                data = {'text': f'字幕{i}', 'start': i, 'end': i+1, 'index': i}
                shared_buffer.put(data)
                results['produced'] += 1
                time.sleep(0.01)
        except Exception as e:
            print(f"生产者错误: {e}")
            results['errors'] += 1
    
    def consumer():
        """消费者线程"""
        try:
            while True:
                try:
                    data = shared_buffer.get(timeout=1.0)
                    if data is None:
                        break
                    results['consumed'] += 1
                    time.sleep(0.01)
                except queue.Empty:
                    break
        except Exception as e:
            print(f"消费者错误: {e}")
            results['errors'] += 1
    
    # 启动线程
    producer_thread = threading.Thread(target=producer, daemon=True)
    consumer_thread = threading.Thread(target=consumer, daemon=True)
    
    producer_thread.start()
    consumer_thread.start()
    
    # 等待完成
    producer_thread.join(timeout=5)
    shared_buffer.put(None)  # 结束信号
    consumer_thread.join(timeout=5)
    
    print(f"线程安全测试: 生产 {results['produced']}, 消费 {results['consumed']}, 错误 {results['errors']}")
    
    return results['errors'] == 0 and results['produced'] > 0 and results['consumed'] > 0

def main():
    """主函数"""
    print("字幕错误修复功能测试")
    print("验证各种字幕错误的处理和恢复机制")
    
    tests = [
        ("字幕数据验证", test_subtitle_data_validation),
        ("安全字幕处理", test_safe_subtitle_processing),
        ("错误恢复机制", test_error_recovery),
        ("缓冲区管理", test_subtitle_buffer_management),
        ("线程安全性", test_thread_safety)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！字幕错误修复功能正常。")
        print("\n修复特性:")
        print("✅ 字幕数据验证和过滤")
        print("✅ 安全的字幕处理机制")
        print("✅ 智能错误恢复策略")
        print("✅ 缓冲区异常处理")
        print("✅ 线程安全保障")
    else:
        print("⚠️  部分测试失败，需要进一步优化。")

if __name__ == "__main__":
    main()
