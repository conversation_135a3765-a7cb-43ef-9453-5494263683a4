#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字幕生成功能
"""

import os
import time

def test_whisper_availability():
    """测试Whisper可用性"""
    print("🧪 测试字幕生成功能")
    print("=" * 50)
    
    # 测试faster-whisper
    try:
        from faster_whisper import WhisperModel
        print("✅ faster-whisper 可用")
        
        # 测试模型加载
        print("🤖 测试模型加载...")
        start_time = time.time()
        
        model = WhisperModel("tiny", device="cpu", compute_type="int8")
        
        load_time = time.time() - start_time
        print(f"✅ tiny模型加载成功，耗时: {load_time:.2f}秒")
        
        # 测试音频文件识别（如果有的话）
        test_audio_files = [
            "C:/Windows/Media/Windows Logon.wav",
            "C:/Windows/Media/chimes.wav"
        ]
        
        for audio_file in test_audio_files:
            if os.path.exists(audio_file):
                print(f"🎤 测试音频识别: {audio_file}")
                try:
                    segments, info = model.transcribe(audio_file, language="en")
                    print(f"   检测语言: {info.language} (置信度: {info.language_probability:.2f})")
                    
                    segment_count = 0
                    for segment in segments:
                        segment_count += 1
                        print(f"   片段 {segment_count}: {segment.start:.2f}s-{segment.end:.2f}s: {segment.text}")
                        if segment_count >= 3:  # 只显示前3个片段
                            break
                    
                    if segment_count == 0:
                        print("   ⚠️ 未检测到语音内容")
                    else:
                        print(f"   ✅ 成功识别 {segment_count} 个语音片段")
                    
                except Exception as e:
                    print(f"   ❌ 识别失败: {e}")
                break
        else:
            print("⚠️ 未找到测试音频文件，跳过识别测试")
        
        return True
        
    except ImportError as e:
        print(f"❌ faster-whisper 不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_model_sizes():
    """测试不同模型大小的加载时间"""
    try:
        from faster_whisper import WhisperModel
        
        print("\n📊 测试不同模型大小:")
        print("-" * 30)
        
        models = ["tiny", "base", "small"]
        
        for model_size in models:
            try:
                print(f"🤖 测试 {model_size} 模型...")
                start_time = time.time()
                
                model = WhisperModel(model_size, device="cpu", compute_type="int8")
                
                load_time = time.time() - start_time
                print(f"   ✅ {model_size} 模型加载成功，耗时: {load_time:.2f}秒")
                
                # 清理内存
                del model
                
            except Exception as e:
                print(f"   ❌ {model_size} 模型加载失败: {e}")
        
    except ImportError:
        print("❌ faster-whisper 不可用，跳过模型测试")

def show_recommendations():
    """显示使用建议"""
    print("\n💡 使用建议:")
    print("=" * 50)
    print("1. 模型选择:")
    print("   • tiny: 最快，适合实时处理，准确性较低")
    print("   • base: 平衡速度和准确性，推荐日常使用")
    print("   • small: 更准确，但速度较慢")
    print("   • medium/large: 最准确，但需要更多资源")
    
    print("\n2. 使用场景:")
    print("   • 实时字幕: 使用tiny或base模型")
    print("   • 完整字幕: 使用base或small模型")
    print("   • 高质量字幕: 使用medium或large模型")
    
    print("\n3. 性能优化:")
    print("   • 使用CPU + int8量化获得最佳兼容性")
    print("   • 启用VAD（语音活动检测）减少处理时间")
    print("   • 设置合适的beam_size平衡速度和准确性")
    
    print("\n4. 支持的语言:")
    print("   • 中文: language='zh'")
    print("   • 英文: language='en'")
    print("   • 自动检测: language=None")

if __name__ == "__main__":
    # 测试基本功能
    whisper_available = test_whisper_availability()
    
    if whisper_available:
        # 测试不同模型
        test_model_sizes()
        
        # 显示建议
        show_recommendations()
        
        print("\n✅ 字幕生成功能测试完成")
        print("现在可以在主程序中使用实时字幕生成功能了！")
    else:
        print("\n❌ 字幕生成功能不可用")
        print("请安装 faster-whisper: pip install faster-whisper")
