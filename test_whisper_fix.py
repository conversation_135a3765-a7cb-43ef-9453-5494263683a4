#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Whisper 修复
验证 faster-whisper 在播放器中的工作状态
"""

import sys
import os

def test_whisper_import():
    """测试 Whisper 导入"""
    print("=" * 50)
    print("1. 测试 Whisper 导入")
    print("=" * 50)
    
    try:
        from faster_whisper import WhisperModel
        print("✓ faster-whisper 导入成功")
        print(f"Python 路径: {sys.executable}")
        return True
    except ImportError as e:
        print(f"❌ faster-whisper 导入失败: {e}")
        print(f"Python 路径: {sys.executable}")
        return False
    except Exception as e:
        print(f"❌ faster-whisper 导入错误: {e}")
        print(f"Python 路径: {sys.executable}")
        return False

def test_model_loading():
    """测试模型加载（模拟播放器的逻辑）"""
    print("\n" + "=" * 50)
    print("2. 测试模型加载（模拟播放器逻辑）")
    print("=" * 50)
    
    try:
        from faster_whisper import WhisperModel
        
        # 模拟播放器中的模型加载逻辑
        model_options = [
            ("base", "base模型 (较快)"),
            ("small", "small模型 (平衡)"),
            ("medium", "medium模型 (较好)"),
            ("large-v3", "large-v3模型 (最好)")
        ]

        model_loaded = False
        loaded_model = None
        
        for model_name, model_desc in model_options:
            try:
                print(f"尝试加载 {model_desc}...")
                
                # 使用CPU版本，确保兼容性
                model = WhisperModel(model_name, device="cpu", compute_type="int8")
                
                print(f"✓ {model_desc} 加载成功")
                model_loaded = True
                loaded_model = model
                break
                
            except Exception as model_error:
                print(f"❌ 加载 {model_desc} 失败: {model_error}")
                continue

        if model_loaded:
            print(f"\n✓ 成功加载模型")
            # 清理模型
            del loaded_model
            return True
        else:
            print(f"\n❌ 所有模型加载尝试都失败")
            return False
            
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")
        return False

def test_player_whisper_logic():
    """测试播放器中的 Whisper 逻辑"""
    print("\n" + "=" * 50)
    print("3. 测试播放器 Whisper 逻辑")
    print("=" * 50)
    
    # 模拟播放器中的导入逻辑
    try:
        from faster_whisper import WhisperModel
        WHISPER_AVAILABLE = True
        print("✓ faster-whisper 导入成功")
    except ImportError as e:
        WHISPER_AVAILABLE = False
        print(f"❌ faster-whisper未安装，字幕功能将被禁用")
        print(f"导入错误详情: {e}")
        print(f"Python 路径: {sys.executable}")
        return False
    except Exception as e:
        WHISPER_AVAILABLE = False
        print(f"❌ faster-whisper 导入时发生错误: {e}")
        print(f"Python 路径: {sys.executable}")
        return False
    
    # 模拟模型加载
    if WHISPER_AVAILABLE:
        try:
            print("开始加载Whisper模型...")
            
            # 尝试加载最小的模型
            whisper_model = WhisperModel("base", device="cpu", compute_type="int8")
            
            print("✓ Whisper模型加载成功")
            
            # 模拟检查逻辑
            whisper_ready = WHISPER_AVAILABLE and whisper_model is not None
            
            if whisper_ready:
                print("✓ Whisper功能就绪")
                status = "字幕功能可用"
            else:
                print("❌ Whisper功能不就绪")
                status = "字幕功能不可用"
            
            print(f"状态: {status}")
            
            # 清理
            del whisper_model
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    return False

def provide_troubleshooting():
    """提供故障排除建议"""
    print("\n" + "=" * 50)
    print("4. 故障排除建议")
    print("=" * 50)
    
    print("如果测试失败，请尝试以下解决方案：")
    print()
    print("1. 确保使用正确的虚拟环境：")
    print("   - 激活虚拟环境: source .venv/Scripts/activate")
    print("   - 检查Python路径: which python")
    print()
    print("2. 重新安装 faster-whisper：")
    print("   - pip uninstall faster-whisper")
    print("   - pip install faster-whisper")
    print()
    print("3. 检查网络连接：")
    print("   - 模型首次使用时需要从网络下载")
    print("   - 确保网络连接正常")
    print()
    print("4. 检查系统资源：")
    print("   - 确保有足够的内存")
    print("   - 关闭其他占用资源的程序")
    print()
    print("5. 使用更小的模型：")
    print("   - 如果内存不足，使用 'tiny' 或 'base' 模型")
    print("   - 避免使用 'large-v3' 模型")

def main():
    """主函数"""
    print("Whisper 修复测试")
    print("用于验证 faster-whisper 在播放器中的工作状态")
    
    # 运行测试
    tests = [
        ("Whisper 导入", test_whisper_import),
        ("模型加载", test_model_loading),
        ("播放器 Whisper 逻辑", test_player_whisper_logic)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    for test_name, result in results:
        status = "✓" if result else "❌"
        print(f"{status} {test_name}")
    
    if passed == total:
        print("\n🎉 所有测试通过！Whisper 功能应该可以正常使用。")
        print("现在可以运行播放器，Whisper 字幕功能应该可用。")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败")
        provide_troubleshooting()

if __name__ == "__main__":
    main()
