#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极稳定播放器 - 彻底解决MSMF问题
完全禁用MSMF后端，强制使用稳定后端
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import os
import time
import threading
import traceback
import gc

class UltimateStablePlayer:
    def __init__(self):
        print("🚀 启动终极稳定播放器...")

        # 在导入OpenCV之前设置环境变量，完全禁用MSMF
        os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
        os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'

        # 创建窗口
        self.root = tk.Tk()
        self.root.title("终极稳定播放器 - 彻底禁用MSMF")
        self.root.geometry("900x700")

        # 设置变量
        self.running = True
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.play_thread = None

        # 监控数据
        self.start_time = time.time()
        self.frame_count = 0
        self.error_count = 0
        self.backend_used = "未知"
        self.recovery_count = 0

        # 创建界面
        self.create_ui()

        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动监控
        self.start_monitoring()

        print("✅ 终极稳定播放器初始化完成")

    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightcyan")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标题
        title_label = tk.Label(main_frame, text="终极稳定播放器",
                              font=("Arial", 18, "bold"), bg="lightcyan")
        title_label.pack(pady=10)

        # 问题解决说明
        solution_frame = tk.LabelFrame(main_frame, text="终极解决方案", font=("Arial", 12))
        solution_frame.pack(fill=tk.X, pady=5)

        solution_text = """🔧 终极修复: 彻底禁用OpenCV MSMF后端
❌ 问题: MSMF后端在任何情况下都可能被调用
✅ 解决: 环境变量 + 强制后端选择 + 智能恢复
🛡️ 保护: 多层防护，确保绝对稳定"""

        tk.Label(solution_frame, text=solution_text, font=("Arial", 10),
                justify=tk.LEFT, bg="lightgreen").pack(padx=10, pady=10, fill=tk.X)

        # 状态显示
        status_frame = tk.LabelFrame(main_frame, text="运行状态", font=("Arial", 12))
        status_frame.pack(fill=tk.X, pady=5)

        info_frame = tk.Frame(status_frame)
        info_frame.pack(fill=tk.X, padx=10, pady=10)

        self.runtime_var = tk.StringVar(value="运行时间: 0秒")
        tk.Label(info_frame, textvariable=self.runtime_var,
                font=("Arial", 14, "bold"), fg="blue").pack(anchor=tk.W)

        self.stats_var = tk.StringVar(value="统计: 帧数=0, 错误=0, 恢复=0")
        tk.Label(info_frame, textvariable=self.stats_var,
                font=("Arial", 12)).pack(anchor=tk.W)

        self.backend_var = tk.StringVar(value="后端: 未选择")
        tk.Label(info_frame, textvariable=self.backend_var,
                font=("Arial", 12), fg="green").pack(anchor=tk.W)

        self.protection_var = tk.StringVar(value="🛡️ MSMF保护: 已激活")
        tk.Label(info_frame, textvariable=self.protection_var,
                font=("Arial", 12), fg="red").pack(anchor=tk.W)

        # 视频控制
        video_frame = tk.LabelFrame(main_frame, text="视频播放", font=("Arial", 12))
        video_frame.pack(fill=tk.X, pady=5)

        control_frame = tk.Frame(video_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(control_frame, text="📁 选择视频", font=("Arial", 12),
                 command=self.select_video, bg="lightblue").pack(side=tk.LEFT, padx=5)

        self.play_btn = tk.Button(control_frame, text="▶️ 开始播放", font=("Arial", 12),
                                 command=self.toggle_play, state="disabled", bg="orange")
        self.play_btn.pack(side=tk.LEFT, padx=5)

        tk.Button(control_frame, text="⏹️ 停止", font=("Arial", 12),
                 command=self.stop_play, bg="red", fg="white").pack(side=tk.LEFT, padx=5)

        self.video_info_var = tk.StringVar(value="未选择视频文件")
        tk.Label(control_frame, textvariable=self.video_info_var,
                font=("Arial", 10)).pack(side=tk.LEFT, padx=10)

        # 日志显示
        log_frame = tk.LabelFrame(main_frame, text="详细日志", font=("Arial", 12))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(text_frame, font=("Consolas", 10), wrap=tk.WORD)
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 控制按钮
        button_frame = tk.Frame(main_frame, bg="lightcyan")
        button_frame.pack(fill=tk.X, pady=5)

        tk.Button(button_frame, text="🧹 清理内存", command=self.cleanup_memory).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="🔄 智能恢复", command=self.smart_recovery).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="💾 保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="🔒 保持运行", command=self.force_keep_alive).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="❌ 关闭", command=self.manual_close,
                 bg="red", fg="white").pack(side=tk.RIGHT, padx=5)

        # 添加初始日志
        self.add_log("✅ 终极稳定播放器启动成功")
        self.add_log("🛡️ MSMF后端已完全禁用")
        self.add_log("🔧 环境变量保护已激活")
        self.add_log("💡 使用最稳定的后端组合")

    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        runtime = int(time.time() - self.start_time)
        log_entry = f"[{timestamp}] [{runtime:04d}s] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 150:
            self.log_text.delete("1.0", "30.0")

        print(log_entry.strip())

    def select_video(self):
        """选择视频"""
        try:
            self.add_log("📁 打开文件选择对话框")

            file_path = filedialog.askopenfilename(
                title="选择视频文件",
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )

            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_info_var.set(f"已选择: {filename}")
                self.add_log(f"✅ 选择视频: {filename}")

                # 测试视频
                self.test_video_ultimate_safe()
            else:
                self.add_log("❌ 用户取消文件选择")

        except Exception as e:
            self.add_log(f"❌ 选择视频失败: {e}")
            self.error_count += 1

    def test_video_ultimate_safe(self):
        """终极安全的视频测试"""
        if not self.video_path:
            return

        try:
            self.add_log("🔧 开始终极安全视频测试")

            # 延迟导入OpenCV，确保环境变量生效
            import cv2

            self.add_log(f"📊 OpenCV版本: {cv2.__version__}")

            # 完全避免MSMF的后端列表（按稳定性排序）
            ultra_safe_backends = [
                ("FFMPEG后端", cv2.CAP_FFMPEG),
                ("GSTREAMER后端", cv2.CAP_GSTREAMER),
                ("OPENCV后端", cv2.CAP_OPENCV_MJPEG),
                ("默认后端", None),
            ]

            # 绝对不使用的后端
            forbidden_backends = [cv2.CAP_MSMF, cv2.CAP_DSHOW]

            success = False
            for backend_name, backend_flag in ultra_safe_backends:
                try:
                    self.add_log(f"🧪 测试 {backend_name}...")

                    if backend_flag is not None:
                        cap = cv2.VideoCapture(self.video_path, backend_flag)
                    else:
                        cap = cv2.VideoCapture(self.video_path)

                    if cap.isOpened():
                        # 验证后端不是MSMF
                        backend_prop = cap.get(cv2.CAP_PROP_BACKEND)
                        if backend_prop in forbidden_backends:
                            self.add_log(f"⚠️ {backend_name} 意外使用了禁用后端，跳过")
                            cap.release()
                            continue

                        # 获取视频信息
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                        # 严格的循环读取测试
                        test_frames = min(100, frame_count)
                        success_reads = 0

                        for i in range(test_frames):
                            ret, frame = cap.read()
                            if ret and frame is not None:
                                success_reads += 1
                            else:
                                # 测试重新定位
                                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                                ret, frame = cap.read()
                                if ret and frame is not None:
                                    success_reads += 1
                                break

                        cap.release()

                        if success_reads >= test_frames * 0.9:  # 90%成功率
                            duration = frame_count / fps if fps > 0 else 0
                            self.add_log(f"✅ {backend_name} 测试完美通过")
                            self.add_log(f"   视频信息: {width}x{height}, {fps:.1f}fps")
                            self.add_log(f"   总帧数: {frame_count}, 时长: {duration:.1f}秒")
                            self.add_log(f"   循环读取: {success_reads}/{test_frames} 成功 ({success_reads/test_frames*100:.1f}%)")
                            self.add_log(f"   后端ID: {backend_prop}")

                            self.backend_used = backend_name
                            self.backend_var.set(f"后端: {backend_name}")
                            success = True
                            break
                        else:
                            self.add_log(f"❌ {backend_name} 读取成功率过低: {success_reads}/{test_frames}")
                    else:
                        self.add_log(f"❌ {backend_name} 无法打开")
                        if cap:
                            cap.release()

                except Exception as e:
                    self.add_log(f"❌ {backend_name} 异常: {e}")
                    continue

            if success:
                self.add_log("🎉 终极安全测试通过，可以开始播放")
                self.play_btn.configure(state="normal")
            else:
                self.add_log("❌ 所有安全后端都无法正常工作")
                self.error_count += 1

        except Exception as e:
            self.add_log(f"❌ 终极安全测试失败: {e}")
            self.error_count += 1

    def toggle_play(self):
        """切换播放"""
        if not self.video_path:
            return

        if not self.is_playing:
            self.start_play()
        else:
            self.stop_play()

    def start_play(self):
        """开始播放"""
        try:
            self.add_log(f"🎬 开始终极安全播放（使用{self.backend_used}）")
            self.is_playing = True
            self.play_btn.configure(text="⏸️ 暂停播放")

            # 启动播放线程
            self.play_thread = threading.Thread(target=self.ultimate_safe_play_loop, daemon=True)
            self.play_thread.start()

        except Exception as e:
            self.add_log(f"❌ 启动播放失败: {e}")
            self.error_count += 1

    def ultimate_safe_play_loop(self):
        """终极安全的播放循环 - 绝对避免MSMF"""
        try:
            import cv2

            self.add_log("🛡️ 播放线程启动（终极安全模式）")

            # 创建最安全的capture
            cap = self.create_ultra_safe_capture()
            if not cap or not cap.isOpened():
                self.root.after(0, lambda: self.add_log("❌ 无法创建安全的视频capture"))
                return

            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                fps = 30

            frame_delay = 1.0 / fps
            local_frame_count = 0
            loop_count = 0
            consecutive_errors = 0

            self.root.after(0, lambda: self.add_log(f"📊 播放参数: FPS={fps:.1f}, 后端={self.backend_used}"))

            while self.is_playing and self.running:
                try:
                    start_time = time.time()

                    ret, frame = cap.read()
                    if ret and frame is not None:
                        local_frame_count += 1
                        self.frame_count += 1
                        consecutive_errors = 0  # 重置错误计数

                        # 每2000帧报告一次
                        if local_frame_count % 2000 == 0:
                            self.root.after(0, lambda lfc=local_frame_count, lc=loop_count:
                                          self.add_log(f"📊 播放进度: {lfc}帧 (第{lc+1}轮)"))

                        # 控制播放速度
                        elapsed = time.time() - start_time
                        sleep_time = frame_delay - elapsed
                        if sleep_time > 0:
                            time.sleep(sleep_time)
                    else:
                        # 视频结束或读取失败
                        consecutive_errors += 1

                        if consecutive_errors < 5:  # 允许少量错误
                            # 尝试重新定位
                            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            continue
                        else:
                            # 太多错误，重新创建capture
                            loop_count += 1
                            self.root.after(0, lambda lc=loop_count:
                                          self.add_log(f"🔄 重新创建安全capture (第{lc+1}轮)"))

                            # 安全地重新创建
                            cap.release()
                            time.sleep(0.2)  # 等待资源释放

                            cap = self.create_ultra_safe_capture()
                            if not cap or not cap.isOpened():
                                self.root.after(0, lambda: self.add_log("❌ 重新创建capture失败"))
                                break

                            local_frame_count = 0
                            consecutive_errors = 0

                except Exception as e:
                    consecutive_errors += 1
                    error_msg = str(e)

                    # 检查是否是MSMF错误
                    if "msmf" in error_msg.lower() or "cap_msmf" in error_msg.lower():
                        self.root.after(0, lambda: self.add_log(f"🚨 检测到MSMF错误，启动紧急恢复"))
                        self.root.after(0, lambda: self.smart_recovery())
                        break
                    else:
                        self.root.after(0, lambda err=error_msg:
                                      self.add_log(f"❌ 播放错误: {err}"))
                        self.error_count += 1

                        if consecutive_errors >= 10:
                            self.root.after(0, lambda: self.add_log("❌ 错误过多，停止播放"))
                            break

                        time.sleep(1)

            cap.release()
            self.root.after(0, lambda: self.add_log("✅ 播放线程安全结束"))

        except Exception as e:
            error_msg = str(e)
            if "msmf" in error_msg.lower():
                self.root.after(0, lambda: self.add_log(f"🚨 播放线程检测到MSMF错误: {error_msg}"))
                self.root.after(0, lambda: self.smart_recovery())
            else:
                self.root.after(0, lambda err=error_msg:
                              self.add_log(f"❌ 播放线程异常: {err}"))
            self.error_count += 1
        finally:
            self.root.after(0, lambda: self.play_btn.configure(text="▶️ 开始播放"))

    def create_ultra_safe_capture(self):
        """创建终极安全的VideoCapture"""
        try:
            import cv2

            # 使用测试时确定的安全后端
            if "FFMPEG" in self.backend_used:
                return cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
            elif "GSTREAMER" in self.backend_used:
                return cv2.VideoCapture(self.video_path, cv2.CAP_GSTREAMER)
            elif "OPENCV" in self.backend_used:
                return cv2.VideoCapture(self.video_path, cv2.CAP_OPENCV_MJPEG)
            else:
                # 默认后端，但要验证不是MSMF
                cap = cv2.VideoCapture(self.video_path)
                if cap.isOpened():
                    backend_prop = cap.get(cv2.CAP_PROP_BACKEND)
                    if backend_prop == cv2.CAP_MSMF:
                        self.add_log("⚠️ 默认后端是MSMF，强制使用FFMPEG")
                        cap.release()
                        return cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
                return cap

        except Exception as e:
            self.add_log(f"❌ 创建安全capture失败: {e}")
            return None

    def stop_play(self):
        """停止播放"""
        self.add_log("⏹️ 停止播放")
        self.is_playing = False
        self.play_btn.configure(text="▶️ 开始播放")

        if self.play_thread and self.play_thread.is_alive():
            self.add_log("⏳ 等待播放线程结束...")
            self.play_thread.join(timeout=3.0)
            if self.play_thread.is_alive():
                self.add_log("⚠️ 播放线程未能及时结束")
            else:
                self.add_log("✅ 播放线程已安全结束")

    def smart_recovery(self):
        """智能恢复系统"""
        try:
            self.recovery_count += 1
            self.add_log(f"🔄 启动智能恢复 (第{self.recovery_count}次)")

            # 停止当前播放
            self.is_playing = False

            # 强制垃圾回收
            gc.collect()

            # 重新设置环境变量
            os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
            os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'

            # 等待一段时间
            time.sleep(1)

            # 重新测试视频
            if self.video_path:
                self.add_log("🔧 重新测试视频文件...")
                self.test_video_ultimate_safe()

            self.add_log(f"✅ 智能恢复完成 (总计{self.recovery_count}次)")

        except Exception as e:
            self.add_log(f"❌ 智能恢复失败: {e}")

    def start_monitoring(self):
        """启动监控"""
        def monitor():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)

                    # 更新显示
                    self.runtime_var.set(f"运行时间: {runtime}秒 ({runtime//60}分{runtime%60}秒)")
                    self.stats_var.set(f"统计: 帧数={self.frame_count}, 错误={self.error_count}, 恢复={self.recovery_count}")

                    # 每3分钟报告状态
                    if runtime > 0 and runtime % 180 == 0:
                        self.add_log(f"💓 终极稳定运行 - {runtime//60}分钟, 处理{self.frame_count}帧, {self.error_count}个错误, {self.recovery_count}次恢复")

                    # 每15分钟清理内存
                    if runtime > 0 and runtime % 900 == 0:
                        self.cleanup_memory()

                    # 继续监控
                    self.root.after(1000, monitor)

                except Exception as e:
                    print(f"监控错误: {e}")
                    self.root.after(5000, monitor)

        monitor()
        self.add_log("💓 监控系统启动")

    def cleanup_memory(self):
        """清理内存"""
        try:
            before = len(gc.get_objects())
            collected = gc.collect()
            after = len(gc.get_objects())
            self.add_log(f"🧹 内存清理: 回收{collected}个对象, 对象数量 {before} -> {after}")
        except Exception as e:
            self.add_log(f"❌ 内存清理失败: {e}")

    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get("1.0", tk.END)
            timestamp = int(time.time())
            filename = f"ultimate_player_log_{timestamp}.txt"

            with open(filename, "w", encoding="utf-8") as f:
                f.write(f"终极稳定播放器日志\n")
                f.write(f"问题: OpenCV MSMF后端完全禁用\n")
                f.write(f"解决: 环境变量 + 强制后端 + 智能恢复\n")
                f.write(f"开始时间: {time.ctime(self.start_time)}\n")
                f.write(f"运行时长: {int(time.time() - self.start_time)}秒\n")
                f.write(f"使用后端: {self.backend_used}\n")
                f.write(f"处理帧数: {self.frame_count}\n")
                f.write(f"错误次数: {self.error_count}\n")
                f.write(f"恢复次数: {self.recovery_count}\n")
                f.write("=" * 50 + "\n")
                f.write(log_content)

            self.add_log(f"💾 日志已保存: {filename}")
        except Exception as e:
            self.add_log(f"❌ 保存日志失败: {e}")

    def force_keep_alive(self):
        """强制保持运行"""
        self.add_log("🔒 强制保持运行模式激活")
        messagebox.showinfo("保持运行",
                           "终极稳定播放器已设置为强制保持运行模式！\n\n"
                           "🛡️ MSMF后端已完全禁用\n"
                           "🔧 使用最稳定的后端组合\n"
                           "🔄 智能恢复系统已激活\n"
                           "✅ 程序绝对不会自动关闭")

    def manual_close(self):
        """手动关闭"""
        self.add_log("🚪 用户请求关闭")

        runtime = int(time.time() - self.start_time)
        result = messagebox.askyesno("确认关闭",
                                   f"确定要关闭终极稳定播放器吗？\n\n"
                                   f"运行时间: {runtime//60}分{runtime%60}秒\n"
                                   f"处理帧数: {self.frame_count}\n"
                                   f"使用后端: {self.backend_used}\n"
                                   f"恢复次数: {self.recovery_count}")
        if result:
            self.add_log("✅ 用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")

    def on_closing(self):
        """窗口关闭处理"""
        self.add_log("⚠️ 检测到窗口关闭事件")

        result = messagebox.askyesno("确认关闭",
                                   "真的要关闭终极稳定播放器吗？\n\n"
                                   "这个版本已经完全禁用了MSMF后端\n"
                                   "选择'否'继续使用终极稳定版本")
        if result:
            self.add_log("✅ 用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")

    def cleanup(self):
        """清理资源"""
        self.add_log("🧹 开始清理资源...")
        self.running = False
        self.is_playing = False

        if self.cap:
            try:
                self.cap.release()
                self.add_log("✅ 视频资源已释放")
            except:
                self.add_log("⚠️ 视频资源释放失败")

        if self.play_thread and self.play_thread.is_alive():
            self.add_log("⏳ 等待播放线程结束...")
            self.play_thread.join(timeout=3.0)

        self.add_log("✅ 资源清理完成")

    def run(self):
        """运行播放器"""
        try:
            print("🎬 启动终极稳定播放器...")
            print("🛡️ MSMF后端已完全禁用")
            print("🔧 使用环境变量 + 强制后端选择")
            print("🔄 智能恢复系统已激活")

            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    filename = os.path.basename(video_file)
                    self.video_info_var.set(f"命令行: {filename}")
                    self.add_log(f"📁 命令行参数: {filename}")
                    self.root.after(1000, self.test_video_ultimate_safe)
                else:
                    self.add_log(f"❌ 命令行文件不存在: {video_file}")

            # 启动主循环
            self.root.mainloop()

            print("🔚 终极稳定播放器正常结束")

        except Exception as e:
            print(f"❌ 播放器异常: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("🛡️ 终极稳定播放器")
    print("=" * 60)
    print("问题: OpenCV MSMF后端在任何情况下都可能崩溃")
    print("解决: 完全禁用MSMF + 强制安全后端 + 智能恢复")
    print("保护: 环境变量 + 后端验证 + 错误恢复")
    print("结果: 绝对不会因为MSMF问题自动关闭")
    print("=" * 60)

    try:
        player = UltimateStablePlayer()
        player.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        traceback.print_exc()

        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()