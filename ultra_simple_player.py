#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超简单播放器 - 绝对不会自动关闭
只包含最基本的功能，没有任何复杂逻辑
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import os

class UltraSimplePlayer:
    def __init__(self):
        print("🚀 启动超简单播放器...")
        
        # 创建窗口
        self.root = tk.Tk()
        self.root.title("超简单MP4播放器 - 绝不自动关闭")
        self.root.geometry("600x400")
        
        # 设置变量
        self.running = True
        
        # 创建界面
        self.create_simple_ui()
        
        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.ask_before_close)
        
        print("✅ 超简单播放器初始化完成")
    
    def create_simple_ui(self):
        """创建最简单的界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightblue")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="超简单MP4播放器", 
                              font=("Arial", 20, "bold"), bg="lightblue")
        title_label.pack(pady=20)
        
        # 状态显示
        self.status_text = tk.Text(main_frame, height=10, width=60, 
                                  font=("Consolas", 10))
        self.status_text.pack(pady=10)
        
        # 添加初始信息
        self.add_status("程序启动成功！")
        self.add_status("这个播放器绝对不会自动关闭")
        self.add_status("点击下面的按钮测试功能")
        
        # 按钮区域
        button_frame = tk.Frame(main_frame, bg="lightblue")
        button_frame.pack(pady=20)
        
        # 测试按钮
        tk.Button(button_frame, text="测试按钮1", font=("Arial", 12),
                 command=self.test1, bg="lightgreen").pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="测试按钮2", font=("Arial", 12),
                 command=self.test2, bg="lightcoral").pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="选择文件", font=("Arial", 12),
                 command=self.select_file, bg="lightyellow").pack(side=tk.LEFT, padx=5)
        
        # 保持运行按钮
        keep_frame = tk.Frame(main_frame, bg="lightblue")
        keep_frame.pack(pady=10)
        
        tk.Button(keep_frame, text="🔒 强制保持运行", font=("Arial", 14, "bold"),
                 command=self.force_keep_running, bg="orange").pack(pady=5)
        
        # 手动关闭按钮
        tk.Button(keep_frame, text="❌ 手动关闭程序", font=("Arial", 12),
                 command=self.manual_close, bg="red", fg="white").pack(pady=5)
        
        # 启动定时器
        self.start_timer()
    
    def add_status(self, message):
        """添加状态信息"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_text.see(tk.END)
    
    def test1(self):
        """测试按钮1"""
        self.add_status("测试按钮1被点击 ✅")
        print("测试按钮1被点击")
    
    def test2(self):
        """测试按钮2"""
        self.add_status("测试按钮2被点击 ✅")
        print("测试按钮2被点击")
    
    def select_file(self):
        """选择文件"""
        try:
            self.add_status("打开文件选择对话框...")
            
            file_path = filedialog.askopenfilename(
                title="选择任意文件",
                filetypes=[("所有文件", "*.*"), ("视频文件", "*.mp4 *.avi")]
            )
            
            if file_path:
                filename = os.path.basename(file_path)
                self.add_status(f"选择了文件: {filename} ✅")
                print(f"选择了文件: {file_path}")
            else:
                self.add_status("用户取消了文件选择")
                
        except Exception as e:
            self.add_status(f"文件选择出错: {e} ❌")
            print(f"文件选择出错: {e}")
    
    def force_keep_running(self):
        """强制保持运行"""
        self.add_status("🔒 强制保持运行模式激活！")
        self.add_status("程序将继续运行，不会自动关闭")
        print("🔒 强制保持运行模式激活")
        
        # 显示确认消息
        messagebox.showinfo("保持运行", "程序已设置为强制保持运行模式！\n不会自动关闭。")
    
    def manual_close(self):
        """手动关闭"""
        self.add_status("用户请求手动关闭程序")
        
        result = messagebox.askyesno("手动关闭", 
                                   "确定要手动关闭程序吗？\n\n这是唯一的关闭方式。")
        if result:
            self.add_status("用户确认关闭程序 ✅")
            print("用户确认关闭程序")
            self.running = False
            self.root.destroy()
        else:
            self.add_status("用户取消关闭，继续运行 ✅")
            print("用户取消关闭")
    
    def ask_before_close(self):
        """关闭前询问"""
        self.add_status("用户尝试关闭窗口")
        
        result = messagebox.askyesno("确认关闭", 
                                   "真的要关闭程序吗？\n\n选择'否'继续运行\n选择'是'关闭程序")
        if result:
            self.add_status("用户确认关闭 ✅")
            print("用户确认关闭")
            self.running = False
            self.root.destroy()
        else:
            self.add_status("用户取消关闭，继续运行 ✅")
            print("用户取消关闭，继续运行")
    
    def start_timer(self):
        """启动定时器"""
        import time
        if not hasattr(self, 'start_time'):
            self.start_time = time.time()
        
        def update_timer():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)
                    
                    # 每10秒报告一次
                    if runtime > 0 and runtime % 10 == 0:
                        self.add_status(f"💓 程序运行正常，已运行 {runtime} 秒")
                    
                    # 继续定时器
                    self.root.after(1000, update_timer)
                    
                except Exception as e:
                    print(f"定时器错误: {e}")
        
        update_timer()
    
    def run(self):
        """运行播放器"""
        try:
            print("🎬 启动超简单播放器界面...")
            print("💡 这个版本绝对不会自动关闭")
            print("💡 只有用户手动确认才能关闭")
            
            # 处理命令行参数
            if len(sys.argv) > 1:
                file_arg = sys.argv[1]
                print(f"命令行参数: {file_arg}")
                self.add_status(f"命令行参数: {file_arg}")
            
            # 启动主循环
            self.root.mainloop()
            
            print("🔚 程序正常结束")
            
        except Exception as e:
            print(f"❌ 程序异常: {e}")
            import traceback
            traceback.print_exc()
            
            # 保持控制台打开
            try:
                input("\n程序异常，按回车键退出...")
            except:
                import time
                time.sleep(10)

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 超简单MP4播放器")
    print("=" * 60)
    print("特点:")
    print("- 绝对不会自动关闭")
    print("- 只包含最基本功能")
    print("- 需要用户手动确认才能关闭")
    print("- 没有复杂的逻辑")
    print("=" * 60)
    
    try:
        player = UltraSimplePlayer()
        player.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        try:
            input("按回车键退出...")
        except:
            import time
            time.sleep(10)

if __name__ == "__main__":
    main()
