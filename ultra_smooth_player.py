#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超流畅播放器 - 解决拖动卡顿和pthread错误
使用优化的ffmpeg配置和高性能seek策略
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk

# 🚀 超流畅ffmpeg配置 - 解决拖动卡顿问题
print("🔧 设置超流畅ffmpeg环境...")

# 优化的ffmpeg配置 - 平衡性能和稳定性
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '100'

# 关键优化：使用多线程但控制好
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;4|thread_type;1|thread_count;4|buffer_size;131072|max_delay;100000'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '4'  # 使用4个线程提升性能

# 启用硬件加速
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '0'

# 优化seek性能 - 关键配置
os.environ['OPENCV_FFMPEG_SEEK_BUFFER_SIZE'] = '16384'  # 增大seek缓冲
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '0'  # 启用异步处理
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '0'     # 不强制同步

# 线程安全但不过度限制
os.environ['OPENCV_FFMPEG_THREAD_SAFE'] = '1'
os.environ['FFMPEG_THREAD_SAFE'] = '1'

# 禁用MSMF避免冲突
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'

print("✅ 超流畅ffmpeg环境设置完成")

# 检查依赖
try:
    import pygame
    pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=1024)
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用")
except Exception as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")

class UltraSmoothPlayer:
    def __init__(self, root, video_path=None):
        self.root = root
        self.root.title("超流畅播放器 - 解决拖动卡顿")
        self.root.geometry("1000x700")
        
        # 播放状态
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.duration = 0
        
        # 视频相关
        self.cap = None
        self.video_path = None
        self.video_thread = None
        
        # 音频相关
        self.audio_file = None
        self.audio_start_time = None
        self.audio_offset = 0
        self.audio_playing = False
        
        # 超流畅seek优化
        self.seek_lock = threading.Lock()
        self.last_seek_time = 0
        self.seek_debounce = 0.1  # 100ms防抖，更快响应
        self.seek_in_progress = False
        self.pending_seek = None
        
        # 高性能缓存
        self.frame_cache = {}
        self.cache_size = 100  # 增大缓存
        self.cache_lock = threading.Lock()
        
        # 拖动状态
        self.is_dragging = False
        self.drag_start_time = 0
        
        # 同步控制
        self.play_start_time = None
        self.video_start_frame = 0
        self.sync_lock = threading.Lock()
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示区域
        video_container = ttk.Frame(main_frame)
        video_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = tk.Label(video_container, bg='black', fg='white',
                                   text="超流畅播放器\n\n特点:\n• 优化的ffmpeg配置\n• 解决拖动卡顿问题\n• 消除pthread错误\n• 高性能seek策略\n\n点击'打开视频'开始")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="播放控制")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮行
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="📁 打开视频", command=self.open_video).pack(side=tk.LEFT, padx=(0, 5))
        
        self.play_btn = ttk.Button(btn_frame, text="▶️ 播放", command=self.toggle_play)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(btn_frame, text="⏹️ 停止", command=self.stop).pack(side=tk.LEFT, padx=(0, 5))
        
        # 音频控制
        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(btn_frame, text="🔊 音频", variable=self.audio_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 进度控制
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.on_seek)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))
        
        # 绑定拖动事件
        self.progress_bar.bind("<Button-1>", self.on_drag_start)
        self.progress_bar.bind("<ButtonRelease-1>", self.on_drag_end)
        self.progress_bar.bind("<B1-Motion>", self.on_drag_motion)
        
        self.time_label = ttk.Label(progress_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 信息面板
        info_frame = ttk.LabelFrame(main_frame, text="播放信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_label = ttk.Label(info_frame, text="未加载视频")
        self.info_label.pack(padx=5, pady=5)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪 - 超流畅播放器", relief=tk.SUNKEN)
        self.status_label.pack(fill=tk.X)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def open_video(self):
        """打开视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("常见视频", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv"),
                ("MP4文件", "*.mp4"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频文件 - 超流畅版本"""
        try:
            print(f"📁 加载视频: {video_path}")
            self.status_label.configure(text="正在加载视频...")
            
            # 停止当前播放
            self.stop()
            
            # 清理缓存
            with self.cache_lock:
                self.frame_cache.clear()
            
            # 使用优化的ffmpeg后端
            print("🔧 使用超流畅ffmpeg后端...")
            self.cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)
            
            if not self.cap.isOpened():
                print("⚠️ ffmpeg后端失败，尝试默认后端...")
                self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 验证参数
            if self.total_frames <= 0:
                self.total_frames = 10000
            if self.fps <= 0 or self.fps > 120:
                self.fps = 30
            
            self.duration = self.total_frames / self.fps
            
            print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps, {width}x{height}")
            
            # 设置进度条
            self.progress_bar.configure(to=self.total_frames-1)
            self.current_frame = 0
            
            # 显示第一帧
            self.show_current_frame()
            
            # 预处理音频
            self.preprocess_audio(video_path)
            
            # 更新界面
            self.video_path = video_path
            filename = os.path.basename(video_path)
            self.info_label.configure(text=f"文件: {filename}\n分辨率: {width}x{height}\n帧率: {self.fps}fps\n时长: {self.format_time(self.duration)}")
            self.status_label.configure(text=f"已加载: {filename}")
            
        except Exception as e:
            error_msg = f"加载视频失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            self.status_label.configure(text="加载失败")
    
    def preprocess_audio(self, video_path):
        """预处理音频"""
        if not self.audio_var.get() or not MOVIEPY_AVAILABLE:
            print("🔇 音频已禁用")
            return
        
        def process_in_thread():
            try:
                print("🔊 预处理音频...")
                self.root.after(0, lambda: self.status_label.configure(text="正在处理音频..."))
                
                import tempfile
                
                video_clip = VideoFileClip(video_path)
                
                if video_clip.audio is not None:
                    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                        self.audio_file = temp_file.name
                    
                    video_clip.audio.write_audiofile(
                        self.audio_file,
                        verbose=False,
                        logger=None,
                        codec='pcm_s16le',
                        ffmpeg_params=['-ar', '44100', '-ac', '2']
                    )
                    
                    video_clip.close()
                    
                    print(f"✅ 音频预处理完成: {self.audio_file}")
                    self.root.after(0, lambda: self.status_label.configure(text="音频预处理完成"))
                else:
                    print("⚠️ 视频文件没有音频轨道")
                    self.audio_file = None
                    
            except Exception as e:
                print(f"❌ 音频预处理失败: {e}")
                self.audio_file = None
        
        threading.Thread(target=process_in_thread, daemon=True).start()

    def show_current_frame(self):
        """显示当前帧 - 高性能缓存版本"""
        if not self.cap:
            return

        try:
            # 检查缓存
            with self.cache_lock:
                if self.current_frame in self.frame_cache:
                    frame = self.frame_cache[self.current_frame]
                else:
                    # 读取新帧
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)
                    ret, frame = self.cap.read()
                    if not ret:
                        return

                    # 智能缓存管理
                    if len(self.frame_cache) >= self.cache_size:
                        # 移除距离当前帧最远的缓存
                        farthest_frame = max(self.frame_cache.keys(),
                                           key=lambda x: abs(x - self.current_frame))
                        del self.frame_cache[farthest_frame]

                    self.frame_cache[self.current_frame] = frame.copy()

            # 转换并显示
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 获取显示区域大小
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()

            if label_width > 1 and label_height > 1:
                h, w = frame_rgb.shape[:2]
                scale = min(label_width/w, label_height/h)
                new_w, new_h = int(w*scale), int(h*scale)

                if new_w > 0 and new_h > 0:
                    frame_resized = cv2.resize(frame_rgb, (new_w, new_h))
                    pil_image = Image.fromarray(frame_resized)
                    photo = ImageTk.PhotoImage(pil_image)

                    self.video_label.configure(image=photo, text="")
                    self.video_label.image = photo

            # 更新进度和时间
            if not self.is_dragging:  # 拖动时不更新进度条
                self.progress_var.set(self.current_frame)

            current_time = self.current_frame / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(self.duration)}")

        except Exception as e:
            print(f"❌ 显示帧失败: {e}")

    def format_time(self, seconds):
        """格式化时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"

    def toggle_play(self):
        """播放/暂停切换"""
        if not self.cap:
            messagebox.showwarning("警告", "请先加载视频文件")
            return

        if self.is_playing:
            self.pause()
        else:
            self.play()

    def play(self):
        """开始播放 - 超流畅版本"""
        if not self.cap:
            return

        self.is_playing = True
        self.play_btn.configure(text="⏸️ 暂停")

        # 设置播放基准
        with self.sync_lock:
            self.play_start_time = time.time()
            self.video_start_frame = self.current_frame
            self.audio_offset = self.current_frame / self.fps

        # 播放音频
        self.play_audio()

        # 启动视频播放线程
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.ultra_smooth_playback_loop, daemon=True)
            self.video_thread.start()

        self.status_label.configure(text="正在播放...")
        print("▶️ 开始播放")

    def pause(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 暂停音频
        self.pause_audio()

        self.status_label.configure(text="已暂停")
        print("⏸️ 暂停播放")

    def stop(self):
        """停止播放"""
        self.is_playing = False
        self.play_btn.configure(text="▶️ 播放")

        # 停止音频
        self.stop_audio()

        # 重置位置
        if self.cap:
            self.current_frame = 0
            self.show_current_frame()

        self.status_label.configure(text="已停止")
        print("⏹️ 停止播放")

    def ultra_smooth_playback_loop(self):
        """超流畅播放循环 - 高性能版本"""
        print("🎬 启动超流畅播放循环")

        while self.is_playing and self.cap:
            try:
                current_time = time.time()

                # 计算目标帧
                with self.sync_lock:
                    if self.play_start_time:
                        elapsed_time = current_time - self.play_start_time
                        target_frame = self.video_start_frame + int(elapsed_time * self.fps)

                        # 超流畅帧控制 - 更精确的同步
                        frame_diff = target_frame - self.current_frame

                        if frame_diff > 20:  # 大幅落后
                            print(f"🔄 大幅跳帧: {self.current_frame} -> {target_frame}")
                            self.current_frame = target_frame
                            # 清理远程缓存
                            with self.cache_lock:
                                keys_to_remove = [k for k in self.frame_cache.keys()
                                                if abs(k - target_frame) > 50]
                                for k in keys_to_remove:
                                    del self.frame_cache[k]
                        elif frame_diff > 0:
                            # 渐进式追赶 - 更平滑
                            self.current_frame = min(target_frame, self.current_frame + min(frame_diff, 2))

                        # 检查播放结束
                        if self.current_frame >= self.total_frames:
                            self.root.after(0, self.stop)
                            break

                # 更新显示
                self.root.after(0, self.show_current_frame)

                # 精确的时间控制
                frame_time = 1.0 / self.fps
                time.sleep(frame_time * 0.95)  # 稍微快一点

            except Exception as e:
                print(f"❌ 播放循环错误: {e}")
                break

        print("🎬 播放循环结束")

    def play_audio(self):
        """播放音频"""
        if not self.audio_var.get() or not self.audio_file or not PYGAME_AVAILABLE:
            return

        try:
            print(f"🔊 播放音频，偏移: {self.audio_offset:.2f}秒")

            pygame.mixer.music.load(self.audio_file)
            pygame.mixer.music.set_volume(1.0)
            pygame.mixer.music.play()

            self.audio_start_time = time.time() - self.audio_offset
            self.audio_playing = True

            print("✅ 音频播放开始")

        except Exception as e:
            print(f"❌ 音频播放失败: {e}")
            self.audio_playing = False

    def pause_audio(self):
        """暂停音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.pause()
            self.audio_playing = False
            print("🔊 音频已暂停")
        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.stop()
            self.audio_playing = False
            self.audio_start_time = None
            self.audio_offset = 0
            print("🔊 音频已停止")
        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def on_drag_start(self, event):
        """开始拖动"""
        self.is_dragging = True
        self.drag_start_time = time.time()
        print("🔄 开始拖动进度条")

    def on_drag_motion(self, event):
        """拖动中"""
        if self.is_dragging:
            # 实时更新时间显示
            frame_num = int(self.progress_var.get())
            current_time = frame_num / self.fps
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(self.duration)}")

    def on_drag_end(self, event):
        """结束拖动"""
        self.is_dragging = False
        drag_duration = time.time() - self.drag_start_time
        print(f"🔄 结束拖动进度条，持续时间: {drag_duration:.2f}秒")

        # 执行最终的seek
        frame_num = int(self.progress_var.get())
        self.ultra_smooth_seek(frame_num)

    def on_seek(self, value):
        """进度条变化处理"""
        if self.is_dragging:
            return  # 拖动中不执行seek

        # 非拖动的seek（如点击进度条）
        frame_num = int(float(value))
        self.ultra_smooth_seek(frame_num)

    def ultra_smooth_seek(self, frame_num):
        """超流畅seek - 解决卡顿问题"""
        current_time = time.time()

        # 防抖处理
        if current_time - self.last_seek_time < self.seek_debounce:
            self.pending_seek = frame_num
            return

        self.last_seek_time = current_time

        with self.seek_lock:
            if self.seek_in_progress:
                self.pending_seek = frame_num
                return

            self.seek_in_progress = True

        try:
            # 计算跳转距离
            frame_diff = abs(frame_num - self.current_frame)
            seek_time = frame_num / self.fps

            print(f"🎬 超流畅跳转: 帧{frame_num} ({seek_time:.2f}秒), 距离{frame_diff}帧")

            with self.sync_lock:
                self.current_frame = frame_num

                if self.is_playing:
                    self.play_start_time = time.time()
                    self.video_start_frame = frame_num
                    self.audio_offset = frame_num / self.fps

                    # 智能音频重启策略
                    if frame_diff > 90:  # 超过3秒才重启音频
                        print(f"🔊 大幅跳转({frame_diff}帧)，重启音频")
                        self.stop_audio()
                        self.play_audio()
                    else:
                        print(f"🔊 小幅跳转({frame_diff}帧)，保持音频")

            # 清理远程缓存
            if frame_diff > 50:
                with self.cache_lock:
                    keys_to_remove = [k for k in self.frame_cache.keys()
                                    if abs(k - frame_num) > 100]
                    for k in keys_to_remove:
                        del self.frame_cache[k]

            # 显示当前帧
            if not self.is_playing:
                self.show_current_frame()

        except Exception as e:
            print(f"❌ 超流畅跳转失败: {e}")
        finally:
            self.seek_in_progress = False

            # 处理待处理的seek
            if self.pending_seek is not None:
                pending = self.pending_seek
                self.pending_seek = None
                self.root.after(50, lambda: self.ultra_smooth_seek(pending))

    def on_close(self):
        """程序关闭处理"""
        print("🧹 清理资源...")

        # 停止播放
        self.stop()

        # 释放视频资源
        if self.cap:
            self.cap.release()

        # 清理音频文件
        if self.audio_file and os.path.exists(self.audio_file):
            try:
                os.unlink(self.audio_file)
                print("🧹 临时音频文件已清理")
            except Exception as e:
                print(f"⚠️ 清理音频文件失败: {e}")

        # 退出pygame
        if PYGAME_AVAILABLE:
            pygame.mixer.quit()

        print("✅ 资源清理完成")
        self.root.destroy()


def main():
    """主函数"""
    print("=" * 60)
    print("🎬 超流畅播放器 - 解决拖动卡顿和pthread错误")
    print("=" * 60)
    print("超流畅特点:")
    print("• 优化的ffmpeg配置 - 使用多线程提升性能")
    print("• 解决拖动卡顿问题 - 高性能seek策略")
    print("• 消除pthread错误 - 平衡的线程配置")
    print("• 智能缓存机制 - 100帧高性能缓存")
    print("• 超快响应 - 100ms防抖，即时响应")
    print("=" * 60)

    root = tk.Tk()

    # 检查命令行参数
    video_path = None
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"⚠️ 文件不存在: {video_path}")
            video_path = None

    app = UltraSmoothPlayer(root, video_path)
    root.mainloop()


if __name__ == "__main__":
    main()
