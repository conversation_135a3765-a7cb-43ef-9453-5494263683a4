#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频音频同时播放器 - 不使用ffmpeg
使用pygame播放音频，OpenCV播放视频，实现同步播放
"""

import os
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk

# 设置环境变量 - 禁用ffmpeg
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '100'  # 优先使用MSMF
os.environ['OPENCV_VIDEOIO_PRIORITY_FFMPEG'] = '0'  # 禁用FFMPEG
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '1'  # 启用硬件加速

print("🔧 设置非ffmpeg环境变量...")
print("✅ 环境变量设置完成 - 优先使用MSMF后端")

# 检查依赖
try:
    import pygame
    pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=1024)
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用 - 音频播放引擎")
except ImportError as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用 - 音频提取引擎")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")

class VideoAudioPlayer:
    def __init__(self, root, video_path=None):
        self.root = root
        self.root.title("视频音频同时播放器 - 无ffmpeg版本")
        self.root.geometry("1000x700")
        
        # 播放状态
        self.is_playing = False
        self.is_paused = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.frame_delay = 1/30
        
        # 视频相关
        self.cap = None
        self.video_path = None
        self.video_thread = None
        
        # 音频相关
        self.audio_clip = None
        self.audio_start_time = None
        self.audio_paused_position = 0
        self.audio_enabled = True
        
        # 同步相关
        self.sync_lock = threading.Lock()
        self.start_time = None
        
        self.setup_ui()
        
        if video_path:
            self.load_video(video_path)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(main_frame)
        self.video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.video_label = tk.Label(self.video_frame, bg='black', text="请选择视频文件")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # 控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 播放控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(0, 5))
        
        self.open_btn = ttk.Button(button_frame, text="打开视频", command=self.open_video)
        self.open_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.play_btn = ttk.Button(button_frame, text="播放", command=self.toggle_play)
        self.play_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(button_frame, text="停止", command=self.stop_video)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 音频控制
        self.audio_var = tk.BooleanVar(value=True)
        self.audio_check = ttk.Checkbutton(button_frame, text="启用音频", 
                                          variable=self.audio_var)
        self.audio_check.pack(side=tk.LEFT, padx=(10, 5))
        
        # 进度条
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(progress_frame, from_=0, to=100,
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.seek_video)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(progress_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.pack(fill=tk.X)
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def open_video(self):
        """打开视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.load_video(file_path)
    
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            print(f"🎬 加载视频: {video_path}")
            
            # 停止当前播放
            self.stop_video()
            
            # 尝试使用MSMF后端打开视频
            self.cap = cv2.VideoCapture(video_path, cv2.CAP_MSMF)
            
            if not self.cap.isOpened():
                print("⚠️ MSMF后端失败，尝试默认后端...")
                self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                raise Exception("无法打开视频文件")
            
            # 获取视频信息
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.frame_delay = 1.0 / self.fps if self.fps > 0 else 1/30
            
            print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps")
            
            # 设置进度条范围
            self.progress_bar.configure(to=self.total_frames-1)
            
            # 加载音频
            self.load_audio(video_path)
            
            # 显示第一帧
            self.current_frame = 0
            self.show_frame()
            
            # 更新状态
            self.video_path = video_path
            self.status_label.configure(text=f"已加载: {os.path.basename(video_path)}")
            
        except Exception as e:
            print(f"❌ 加载视频失败: {e}")
            messagebox.showerror("错误", f"加载视频失败: {e}")
    
    def load_audio(self, video_path):
        """加载音频"""
        if not MOVIEPY_AVAILABLE or not PYGAME_AVAILABLE:
            print("⚠️ 音频功能不可用")
            return
        
        try:
            print("🔊 加载音频...")
            
            # 使用moviepy加载音频
            video_clip = VideoFileClip(video_path)
            
            if video_clip.audio is not None:
                self.audio_clip = video_clip.audio
                print(f"✅ 音频加载成功: {self.audio_clip.duration:.2f}秒")
            else:
                print("⚠️ 视频文件没有音频轨道")
                self.audio_clip = None
            
            # 不关闭video_clip，保持audio_clip可用
            
        except Exception as e:
            print(f"❌ 加载音频失败: {e}")
            self.audio_clip = None
    
    def show_frame(self):
        """显示当前帧"""
        if not self.cap:
            return
        
        try:
            ret, frame = self.cap.read()
            if not ret:
                return
            
            # 转换为RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 调整大小适应显示区域
            label_width = self.video_label.winfo_width()
            label_height = self.video_label.winfo_height()
            
            if label_width > 1 and label_height > 1:
                # 计算缩放比例
                h, w = frame_rgb.shape[:2]
                scale = min(label_width/w, label_height/h)
                new_w, new_h = int(w*scale), int(h*scale)
                
                # 调整大小
                frame_resized = cv2.resize(frame_rgb, (new_w, new_h))
                
                # 转换为PIL图像
                pil_image = Image.fromarray(frame_resized)
                photo = ImageTk.PhotoImage(pil_image)
                
                # 显示图像
                self.video_label.configure(image=photo, text="")
                self.video_label.image = photo  # 保持引用
            
            # 更新进度条和时间
            self.progress_var.set(self.current_frame)
            current_time = self.current_frame / self.fps if self.fps > 0 else 0
            total_time = self.total_frames / self.fps if self.fps > 0 else 0
            self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            
        except Exception as e:
            print(f"❌ 显示帧失败: {e}")
    
    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def toggle_play(self):
        """切换播放/暂停"""
        if not self.cap:
            messagebox.showwarning("警告", "请先加载视频文件")
            return
        
        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()
    
    def play_video(self):
        """开始播放"""
        if not self.cap:
            return
        
        self.is_playing = True
        self.is_paused = False
        self.play_btn.configure(text="暂停")
        
        # 记录开始时间
        with self.sync_lock:
            self.start_time = time.time() - (self.current_frame / self.fps)
        
        # 启动视频播放线程
        if not self.video_thread or not self.video_thread.is_alive():
            self.video_thread = threading.Thread(target=self.video_playback_loop, daemon=True)
            self.video_thread.start()
        
        # 启动音频播放
        self.play_audio()
        
        print("▶️ 开始播放")
    
    def pause_video(self):
        """暂停播放"""
        self.is_playing = False
        self.is_paused = True
        self.play_btn.configure(text="播放")
        
        # 暂停音频
        self.pause_audio()
        
        print("⏸️ 暂停播放")
    
    def stop_video(self):
        """停止播放"""
        self.is_playing = False
        self.is_paused = False
        self.play_btn.configure(text="播放")
        
        # 停止音频
        self.stop_audio()
        
        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.show_frame()
        
        print("⏹️ 停止播放")
    
    def video_playback_loop(self):
        """视频播放循环"""
        print("🎬 启动视频播放循环")
        
        while self.is_playing and self.cap:
            try:
                # 计算应该显示的帧
                with self.sync_lock:
                    if self.start_time:
                        elapsed_time = time.time() - self.start_time
                        target_frame = int(elapsed_time * self.fps)
                        
                        # 如果当前帧落后太多，跳转到目标帧
                        if target_frame > self.current_frame + 5:
                            self.current_frame = target_frame
                            self.cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
                
                # 读取并显示帧
                ret, frame = self.cap.read()
                if not ret:
                    # 播放结束
                    self.root.after(0, self.stop_video)
                    break
                
                self.current_frame += 1
                
                # 在主线程中更新UI
                self.root.after(0, self.show_frame)
                
                # 控制播放速度
                time.sleep(self.frame_delay)
                
            except Exception as e:
                print(f"❌ 播放循环错误: {e}")
                break
        
        print("🎬 视频播放循环结束")

    def play_audio(self):
        """播放音频"""
        if not self.audio_var.get() or not self.audio_clip or not PYGAME_AVAILABLE:
            return

        try:
            print("🔊 开始播放音频...")

            # 计算音频开始位置
            audio_start_pos = self.current_frame / self.fps if self.fps > 0 else 0

            if self.audio_paused_position > 0:
                audio_start_pos = self.audio_paused_position
                self.audio_paused_position = 0

            # 创建音频片段
            if audio_start_pos < self.audio_clip.duration:
                audio_segment = self.audio_clip.subclip(audio_start_pos)

                # 导出为临时文件
                import tempfile
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    temp_audio_path = temp_file.name

                # 使用较快的参数导出
                audio_segment.write_audiofile(
                    temp_audio_path,
                    verbose=False,
                    logger=None,
                    codec='pcm_s16le',
                    ffmpeg_params=['-ar', '22050', '-ac', '1']  # 降低采样率和声道数
                )

                # 播放音频
                pygame.mixer.music.load(temp_audio_path)
                pygame.mixer.music.play()

                self.audio_start_time = time.time()

                print(f"✅ 音频播放开始，从{audio_start_pos:.2f}秒")

                # 清理临时文件（延迟删除）
                def cleanup_temp_file():
                    try:
                        time.sleep(1)  # 等待播放开始
                        if os.path.exists(temp_audio_path):
                            os.unlink(temp_audio_path)
                    except:
                        pass

                threading.Thread(target=cleanup_temp_file, daemon=True).start()

        except Exception as e:
            print(f"❌ 音频播放失败: {e}")

    def pause_audio(self):
        """暂停音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.pause()

            # 计算暂停位置
            if self.audio_start_time:
                elapsed = time.time() - self.audio_start_time
                self.audio_paused_position = (self.current_frame / self.fps) + elapsed

            print("🔊 音频已暂停")

        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not PYGAME_AVAILABLE:
            return

        try:
            pygame.mixer.music.stop()
            self.audio_start_time = None
            self.audio_paused_position = 0
            print("🔊 音频已停止")

        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def seek_video(self, value):
        """跳转到指定位置"""
        if not self.cap:
            return

        try:
            frame_number = int(float(value))

            # 跳转视频
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            self.current_frame = frame_number

            # 更新同步时间
            with self.sync_lock:
                if self.is_playing:
                    self.start_time = time.time() - (frame_number / self.fps)

            # 如果正在播放，重新开始音频
            if self.is_playing:
                self.stop_audio()
                self.play_audio()

            # 显示当前帧
            if not self.is_playing:
                self.show_frame()

            print(f"🎬 跳转到帧: {frame_number}")

        except Exception as e:
            print(f"❌ 跳转失败: {e}")

    def on_closing(self):
        """程序关闭时的清理"""
        print("🧹 清理资源...")

        # 停止播放
        self.stop_video()

        # 释放资源
        if self.cap:
            self.cap.release()

        if self.audio_clip:
            self.audio_clip.close()

        # 退出pygame
        if PYGAME_AVAILABLE:
            pygame.mixer.quit()

        self.root.destroy()


def main():
    """主函数"""
    print("=" * 60)
    print("🎬 视频音频同时播放器 - 无ffmpeg版本")
    print("=" * 60)
    print("特点: 使用MSMF后端播放视频，pygame播放音频")
    print("优势: 避免ffmpeg相关问题，更好的兼容性")
    print("=" * 60)

    root = tk.Tk()

    # 检查命令行参数
    video_path = None
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if not os.path.exists(video_path):
            print(f"⚠️ 文件不存在: {video_path}")
            video_path = None

    app = VideoAudioPlayer(root, video_path)
    root.mainloop()


if __name__ == "__main__":
    main()
