#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频播放结束诊断工具 - 专门找出视频未播放完就自动结束的原因
详细监控播放过程，记录所有可能导致提前结束的因素
"""

import os
import sys

# 🛡️ 设置最安全的环境变量
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1'
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
import queue
import gc
import traceback

class VideoEndDiagnostic:
    def __init__(self):
        print("🔍 启动视频播放结束诊断工具...")

        # 创建窗口
        self.root = tk.Tk()
        self.root.title("视频播放结束诊断工具 - 找出提前结束原因")
        self.root.geometry("1000x800")

        # 设置变量
        self.running = True
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.play_thread = None
        self.current_frame = 0

        # 诊断数据
        self.start_time = time.time()
        self.play_start_time = None
        self.frame_count = 0
        self.error_count = 0
        self.read_failures = 0
        self.null_frames = 0
        self.empty_frames = 0
        self.last_successful_frame = 0
        self.total_expected_frames = 0
        self.actual_fps = 0
        self.expected_duration = 0
        self.actual_duration = 0

        # 详细诊断记录
        self.diagnostic_log = []

        # 创建界面
        self.create_ui()

        # 设置关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动监控
        self.start_monitoring()

        print("✅ 视频播放结束诊断工具初始化完成")

    def create_ui(self):
        """创建界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg="lightgoldenrodyellow")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标题
        title_label = tk.Label(main_frame, text="视频播放结束诊断工具",
                              font=("Arial", 18, "bold"), bg="lightgoldenrodyellow")
        title_label.pack(pady=10)

        # 问题说明
        problem_frame = tk.LabelFrame(main_frame, text="诊断目标", font=("Arial", 12))
        problem_frame.pack(fill=tk.X, pady=5)

        problem_text = """🔍 诊断目标: 视频未播放完就自动结束并关闭程序
📊 监控内容: 帧读取状态、错误类型、播放进度、资源状态
🎯 目标: 找出导致提前结束的具体原因
📝 记录: 详细的诊断日志和错误分析"""

        tk.Label(problem_frame, text=problem_text, font=("Arial", 10),
                justify=tk.LEFT, bg="lightyellow").pack(padx=10, pady=10, fill=tk.X)

        # 诊断状态显示
        status_frame = tk.LabelFrame(main_frame, text="诊断状态", font=("Arial", 12))
        status_frame.pack(fill=tk.X, pady=5)

        # 第一行状态
        status_row1 = tk.Frame(status_frame)
        status_row1.pack(fill=tk.X, padx=10, pady=5)

        self.runtime_var = tk.StringVar(value="运行时间: 0秒")
        tk.Label(status_row1, textvariable=self.runtime_var,
                font=("Arial", 12, "bold"), fg="blue").pack(side=tk.LEFT)

        self.play_time_var = tk.StringVar(value="播放时间: 未开始")
        tk.Label(status_row1, textvariable=self.play_time_var,
                font=("Arial", 12), fg="green").pack(side=tk.RIGHT)

        # 第二行状态
        status_row2 = tk.Frame(status_frame)
        status_row2.pack(fill=tk.X, padx=10, pady=5)

        self.frame_progress_var = tk.StringVar(value="帧进度: 0/0 (0%)")
        tk.Label(status_row2, textvariable=self.frame_progress_var,
                font=("Arial", 12)).pack(side=tk.LEFT)

        self.fps_var = tk.StringVar(value="FPS: 未知")
        tk.Label(status_row2, textvariable=self.fps_var,
                font=("Arial", 12)).pack(side=tk.RIGHT)

        # 第三行状态
        status_row3 = tk.Frame(status_frame)
        status_row3.pack(fill=tk.X, padx=10, pady=5)

        self.error_stats_var = tk.StringVar(value="错误统计: 读取失败=0, 空帧=0, 异常=0")
        tk.Label(status_row3, textvariable=self.error_stats_var,
                font=("Arial", 12), fg="red").pack(side=tk.LEFT)

        # 视频控制
        control_frame = tk.Frame(main_frame, bg="lightgoldenrodyellow")
        control_frame.pack(fill=tk.X, pady=5)

        tk.Button(control_frame, text="📁 选择视频", font=("Arial", 12),
                 command=self.select_video, bg="lightblue").pack(side=tk.LEFT, padx=5)

        self.play_btn = tk.Button(control_frame, text="▶️ 开始诊断播放", font=("Arial", 12),
                                 command=self.toggle_play, state="disabled", bg="orange")
        self.play_btn.pack(side=tk.LEFT, padx=5)

        tk.Button(control_frame, text="⏹️ 停止", font=("Arial", 12),
                 command=self.stop_play, bg="red", fg="white").pack(side=tk.LEFT, padx=5)

        self.video_info_var = tk.StringVar(value="未选择视频文件")
        tk.Label(control_frame, textvariable=self.video_info_var,
                font=("Arial", 10)).pack(side=tk.LEFT, padx=10)

        # 进度条
        progress_frame = tk.Frame(main_frame, bg="lightgoldenrodyellow")
        progress_frame.pack(fill=tk.X, pady=5)

        tk.Label(progress_frame, text="播放进度:", font=("Arial", 10)).pack(side=tk.LEFT)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)

        self.progress_text_var = tk.StringVar(value="0%")
        tk.Label(progress_frame, textvariable=self.progress_text_var,
                font=("Arial", 10)).pack(side=tk.RIGHT)

        # 诊断日志显示
        log_frame = tk.LabelFrame(main_frame, text="详细诊断日志", font=("Arial", 12))
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 日志文本框
        log_text_frame = tk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_text_frame, font=("Consolas", 9), wrap=tk.WORD)
        log_scrollbar = tk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 控制按钮
        button_frame = tk.Frame(main_frame, bg="lightgoldenrodyellow")
        button_frame.pack(fill=tk.X, pady=5)

        tk.Button(button_frame, text="📊 生成诊断报告", command=self.generate_report).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="🧹 清理内存", command=self.cleanup_memory).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="💾 保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="❌ 关闭", command=self.manual_close,
                 bg="red", fg="white").pack(side=tk.RIGHT, padx=5)

        # 添加初始日志
        self.add_log("✅ 视频播放结束诊断工具启动成功")
        self.add_log("🔍 专门诊断视频未播放完就自动结束的问题")
        self.add_log("📊 将详细监控播放过程中的所有状态变化")

    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        runtime = int(time.time() - self.start_time)
        log_entry = f"[{timestamp}] [{runtime:04d}s] {message}\n"

        # 添加到诊断记录
        self.diagnostic_log.append({
            'timestamp': timestamp,
            'runtime': runtime,
            'message': message,
            'frame_count': self.frame_count,
            'current_frame': self.current_frame
        })

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 200:
            self.log_text.delete("1.0", "50.0")

        print(log_entry.strip())

    def select_video(self):
        """选择视频"""
        try:
            self.add_log("📁 打开文件选择对话框")

            file_path = filedialog.askopenfilename(
                title="选择视频文件进行诊断",
                filetypes=[("视频文件", "*.mp4 *.avi"), ("所有文件", "*.*")]
            )

            if file_path:
                self.video_path = file_path
                filename = os.path.basename(file_path)
                self.video_info_var.set(f"已选择: {filename}")
                self.add_log(f"✅ 选择视频: {filename}")

                # 详细分析视频
                self.analyze_video_detailed()
            else:
                self.add_log("❌ 用户取消文件选择")

        except Exception as e:
            self.add_log(f"❌ 选择视频失败: {e}")
            self.error_count += 1

    def analyze_video_detailed(self):
        """详细分析视频"""
        if not self.video_path:
            return

        try:
            self.add_log("🔍 开始详细视频分析...")

            # 创建capture进行分析
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)

            if cap and cap.isOpened():
                # 获取详细视频信息
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                duration = frame_count / fps if fps > 0 else 0

                self.total_expected_frames = frame_count
                self.actual_fps = fps
                self.expected_duration = duration

                self.add_log(f"📊 视频详细信息:")
                self.add_log(f"   分辨率: {width}x{height}")
                self.add_log(f"   帧率: {fps:.2f} fps")
                self.add_log(f"   总帧数: {frame_count}")
                self.add_log(f"   预期时长: {duration:.2f}秒 ({duration/60:.1f}分钟)")

                # 测试读取能力
                self.add_log("🧪 测试视频读取能力...")
                test_positions = [0, frame_count//4, frame_count//2, frame_count*3//4, frame_count-10]

                for i, pos in enumerate(test_positions):
                    if pos >= frame_count:
                        continue

                    try:
                        cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                        ret, frame = cap.read()

                        if ret and frame is not None:
                            self.add_log(f"   位置 {pos} ({pos/frame_count*100:.1f}%): ✅ 读取成功")
                        else:
                            self.add_log(f"   位置 {pos} ({pos/frame_count*100:.1f}%): ❌ 读取失败")
                    except Exception as e:
                        self.add_log(f"   位置 {pos}: ❌ 异常 {e}")

                # 重置到开始位置
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)

                cap.release()

                self.add_log("✅ 视频分析完成，可以开始诊断播放")
                self.play_btn.configure(state="normal")

            else:
                self.add_log("❌ 无法打开视频文件进行分析")
                self.error_count += 1

        except Exception as e:
            self.add_log(f"❌ 视频分析失败: {e}")
            self.error_count += 1

    def toggle_play(self):
        """切换播放"""
        if not self.video_path:
            return

        if not self.is_playing:
            self.start_diagnostic_play()
        else:
            self.stop_play()

    def start_diagnostic_play(self):
        """开始诊断播放"""
        try:
            self.add_log("🎬 开始诊断播放 - 详细监控所有状态")
            self.is_playing = True
            self.play_start_time = time.time()
            self.play_btn.configure(text="⏸️ 停止诊断")

            # 重置诊断数据
            self.frame_count = 0
            self.current_frame = 0
            self.read_failures = 0
            self.null_frames = 0
            self.empty_frames = 0
            self.last_successful_frame = 0

            # 启动诊断播放线程
            self.play_thread = threading.Thread(target=self.diagnostic_play_loop, daemon=True)
            self.play_thread.start()

        except Exception as e:
            self.add_log(f"❌ 启动诊断播放失败: {e}")
            self.error_count += 1

    def diagnostic_play_loop(self):
        """诊断播放循环 - 详细监控每一帧"""
        try:
            self.add_log("🔍 诊断播放线程启动 - 开始详细监控")

            # 创建capture
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
            if not cap or not cap.isOpened():
                self.root.after(0, lambda: self.add_log("❌ 无法创建capture进行诊断"))
                return

            # 获取播放参数
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            if fps <= 0:
                fps = 30
                self.root.after(0, lambda: self.add_log("⚠️ 无法获取FPS，使用默认值30"))

            frame_delay = 1.0 / fps
            consecutive_failures = 0
            last_report_frame = 0

            self.root.after(0, lambda: self.add_log(f"📊 诊断参数: FPS={fps:.2f}, 总帧数={total_frames}, 帧延迟={frame_delay:.3f}s"))

            while self.is_playing and self.running:
                try:
                    loop_start_time = time.time()

                    # 尝试读取帧
                    ret, frame = cap.read()

                    if ret:
                        if frame is not None:
                            # 成功读取到有效帧
                            self.current_frame += 1
                            self.frame_count += 1
                            self.last_successful_frame = self.current_frame
                            consecutive_failures = 0

                            # 检查帧内容
                            if frame.size == 0:
                                self.empty_frames += 1
                                self.root.after(0, lambda cf=self.current_frame:
                                              self.add_log(f"⚠️ 帧 {cf}: 帧大小为0"))

                            # 更新进度
                            if total_frames > 0:
                                progress = (self.current_frame / total_frames) * 100
                                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                                self.root.after(0, lambda p=progress: self.progress_text_var.set(f"{p:.1f}%"))

                            # 每1000帧详细报告一次
                            if self.current_frame - last_report_frame >= 1000:
                                play_time = time.time() - self.play_start_time
                                actual_fps = self.current_frame / play_time if play_time > 0 else 0

                                self.root.after(0, lambda cf=self.current_frame, pt=play_time, afps=actual_fps:
                                              self.add_log(f"📊 进度报告: 帧{cf}/{total_frames} ({cf/total_frames*100:.1f}%), "
                                                          f"播放{pt:.1f}s, 实际FPS={afps:.1f}"))
                                last_report_frame = self.current_frame

                        else:
                            # 读取成功但帧为None
                            self.null_frames += 1
                            consecutive_failures += 1
                            self.root.after(0, lambda cf=self.current_frame:
                                          self.add_log(f"⚠️ 帧 {cf}: 读取成功但帧为None"))
                    else:
                        # 读取失败
                        self.read_failures += 1
                        consecutive_failures += 1

                        # 检查是否真的到了视频末尾
                        current_pos = cap.get(cv2.CAP_PROP_POS_FRAMES)

                        self.root.after(0, lambda cf=self.current_frame, cp=current_pos, tf=total_frames:
                                      self.add_log(f"❌ 帧读取失败: 当前位置={cp}, 总帧数={tf}, 进度={cp/tf*100:.1f}%"))

                        # 如果还没到视频末尾就读取失败，这是异常情况
                        if current_pos < total_frames * 0.95:  # 允许5%的误差
                            self.root.after(0, lambda:
                                          self.add_log(f"🚨 异常: 视频未播放完就无法读取帧！"))
                            self.root.after(0, lambda:
                                          self.add_log(f"   当前位置: {current_pos}/{total_frames} ({current_pos/total_frames*100:.1f}%)"))
                            self.root.after(0, lambda:
                                          self.add_log(f"   连续失败: {consecutive_failures}次"))

                            # 尝试恢复
                            if consecutive_failures < 10:
                                self.root.after(0, lambda: self.add_log("🔄 尝试跳过当前帧继续播放..."))
                                try:
                                    cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos + 1)
                                    time.sleep(0.1)
                                    continue
                                except Exception as seek_error:
                                    self.root.after(0, lambda err=str(seek_error):
                                                  self.add_log(f"❌ 跳帧失败: {err}"))
                            else:
                                self.root.after(0, lambda:
                                              self.add_log(f"❌ 连续失败{consecutive_failures}次，停止播放"))
                                break
                        else:
                            # 正常到达视频末尾
                            self.root.after(0, lambda:
                                          self.add_log(f"✅ 视频正常播放完毕: {current_pos}/{total_frames}"))
                            break

                    # 检查连续失败次数
                    if consecutive_failures >= 20:
                        self.root.after(0, lambda:
                                      self.add_log(f"❌ 连续失败{consecutive_failures}次，强制停止"))
                        break

                    # 控制播放速度
                    elapsed = time.time() - loop_start_time
                    sleep_time = frame_delay - elapsed
                    if sleep_time > 0:
                        time.sleep(sleep_time)

                except Exception as e:
                    self.error_count += 1
                    error_msg = str(e)

                    self.root.after(0, lambda err=error_msg, cf=self.current_frame:
                                  self.add_log(f"❌ 播放异常 (帧{cf}): {err}"))

                    # 检查是否是严重错误
                    if any(keyword in error_msg.lower() for keyword in ['pthread', 'async_lock', 'assertion']):
                        self.root.after(0, lambda:
                                      self.add_log(f"🚨 检测到严重错误，立即停止播放"))
                        break

                    consecutive_failures += 1
                    if consecutive_failures >= 10:
                        self.root.after(0, lambda:
                                      self.add_log(f"❌ 异常过多，停止播放"))
                        break

                    time.sleep(0.1)

            # 播放结束统计
            cap.release()

            play_duration = time.time() - self.play_start_time if self.play_start_time else 0
            completion_rate = (self.current_frame / self.total_expected_frames * 100) if self.total_expected_frames > 0 else 0

            self.actual_duration = play_duration

            self.root.after(0, lambda: self.add_log("=" * 50))
            self.root.after(0, lambda: self.add_log("📊 播放结束统计:"))
            self.root.after(0, lambda pd=play_duration, cr=completion_rate:
                          self.add_log(f"   播放时长: {pd:.2f}秒 (预期: {self.expected_duration:.2f}秒)"))
            self.root.after(0, lambda cr=completion_rate:
                          self.add_log(f"   完成率: {cr:.1f}% ({self.current_frame}/{self.total_expected_frames})"))
            self.root.after(0, lambda:
                          self.add_log(f"   读取失败: {self.read_failures}次"))
            self.root.after(0, lambda:
                          self.add_log(f"   空帧: {self.null_frames}次"))
            self.root.after(0, lambda:
                          self.add_log(f"   错误: {self.error_count}次"))

            if completion_rate < 95:
                self.root.after(0, lambda:
                              self.add_log(f"🚨 诊断结果: 视频未正常播放完毕！"))
                self.root.after(0, lambda:
                              self.add_log(f"   可能原因: 视频文件损坏、编码问题、或播放器兼容性问题"))
            else:
                self.root.after(0, lambda:
                              self.add_log(f"✅ 诊断结果: 视频基本正常播放完毕"))

            self.root.after(0, lambda: self.add_log("=" * 50))

        except Exception as e:
            self.root.after(0, lambda err=str(e):
                          self.add_log(f"❌ 诊断播放线程异常: {err}"))
            self.root.after(0, lambda:
                          self.add_log(f"   异常详情: {traceback.format_exc()}"))
            self.error_count += 1
        finally:
            self.root.after(0, lambda: self.play_btn.configure(text="▶️ 开始诊断播放"))
            self.is_playing = False

    def stop_play(self):
        """停止播放"""
        self.add_log("⏹️ 停止诊断播放")
        self.is_playing = False
        self.play_btn.configure(text="▶️ 开始诊断播放")

        if self.play_thread and self.play_thread.is_alive():
            self.add_log("⏳ 等待诊断线程结束...")
            self.play_thread.join(timeout=3.0)

    def generate_report(self):
        """生成诊断报告"""
        try:
            self.add_log("📊 生成详细诊断报告...")

            timestamp = int(time.time())
            filename = f"video_diagnostic_report_{timestamp}.txt"

            with open(filename, "w", encoding="utf-8") as f:
                f.write("视频播放结束诊断报告\n")
                f.write("=" * 60 + "\n\n")

                f.write("基本信息:\n")
                f.write(f"视频文件: {self.video_path}\n")
                f.write(f"诊断时间: {time.ctime()}\n")
                f.write(f"总运行时间: {int(time.time() - self.start_time)}秒\n\n")

                f.write("视频信息:\n")
                f.write(f"预期总帧数: {self.total_expected_frames}\n")
                f.write(f"预期FPS: {self.actual_fps:.2f}\n")
                f.write(f"预期时长: {self.expected_duration:.2f}秒\n\n")

                f.write("播放统计:\n")
                f.write(f"实际播放帧数: {self.current_frame}\n")
                f.write(f"实际播放时长: {self.actual_duration:.2f}秒\n")
                f.write(f"完成率: {(self.current_frame/self.total_expected_frames*100):.1f}%\n")
                f.write(f"最后成功帧: {self.last_successful_frame}\n\n")

                f.write("错误统计:\n")
                f.write(f"读取失败次数: {self.read_failures}\n")
                f.write(f"空帧次数: {self.null_frames}\n")
                f.write(f"异常次数: {self.error_count}\n\n")

                f.write("诊断结论:\n")
                completion_rate = (self.current_frame / self.total_expected_frames * 100) if self.total_expected_frames > 0 else 0
                if completion_rate < 95:
                    f.write("❌ 视频未正常播放完毕\n")
                    f.write("可能原因:\n")
                    f.write("1. 视频文件损坏或不完整\n")
                    f.write("2. 视频编码格式兼容性问题\n")
                    f.write("3. OpenCV后端处理问题\n")
                    f.write("4. 系统资源不足\n")
                    f.write("5. 播放器配置问题\n")
                else:
                    f.write("✅ 视频基本正常播放完毕\n")

                f.write("\n" + "=" * 60 + "\n")
                f.write("详细日志:\n\n")

                for entry in self.diagnostic_log:
                    f.write(f"[{entry['timestamp']}] [{entry['runtime']:04d}s] "
                           f"[帧{entry['frame_count']}] {entry['message']}\n")

            self.add_log(f"✅ 诊断报告已保存: {filename}")
            messagebox.showinfo("报告生成", f"诊断报告已保存到:\n{filename}")

        except Exception as e:
            self.add_log(f"❌ 生成报告失败: {e}")

    def start_monitoring(self):
        """启动监控"""
        def monitor():
            if self.running:
                try:
                    runtime = int(time.time() - self.start_time)

                    # 更新运行时间
                    self.runtime_var.set(f"运行时间: {runtime}秒 ({runtime//60}分{runtime%60}秒)")

                    # 更新播放时间
                    if self.play_start_time:
                        play_time = time.time() - self.play_start_time
                        self.play_time_var.set(f"播放时间: {play_time:.1f}秒")
                    else:
                        self.play_time_var.set("播放时间: 未开始")

                    # 更新帧进度
                    if self.total_expected_frames > 0:
                        progress = (self.current_frame / self.total_expected_frames) * 100
                        self.frame_progress_var.set(f"帧进度: {self.current_frame}/{self.total_expected_frames} ({progress:.1f}%)")
                    else:
                        self.frame_progress_var.set(f"帧进度: {self.current_frame}/未知")

                    # 更新FPS
                    if self.play_start_time and self.current_frame > 0:
                        play_time = time.time() - self.play_start_time
                        actual_fps = self.current_frame / play_time if play_time > 0 else 0
                        self.fps_var.set(f"FPS: 实际{actual_fps:.1f} / 预期{self.actual_fps:.1f}")
                    else:
                        self.fps_var.set(f"FPS: 预期{self.actual_fps:.1f}")

                    # 更新错误统计
                    self.error_stats_var.set(f"错误统计: 读取失败={self.read_failures}, 空帧={self.null_frames}, 异常={self.error_count}")

                    # 继续监控
                    self.root.after(1000, monitor)

                except Exception as e:
                    print(f"监控错误: {e}")
                    self.root.after(5000, monitor)

        monitor()
        self.add_log("💓 监控系统启动")

    def cleanup_memory(self):
        """清理内存"""
        try:
            before = len(gc.get_objects())
            collected = gc.collect()
            after = len(gc.get_objects())
            self.add_log(f"🧹 内存清理: 回收{collected}个对象, 对象数量 {before} -> {after}")
        except Exception as e:
            self.add_log(f"❌ 内存清理失败: {e}")

    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get("1.0", tk.END)
            timestamp = int(time.time())
            filename = f"video_diagnostic_log_{timestamp}.txt"

            with open(filename, "w", encoding="utf-8") as f:
                f.write(f"视频播放结束诊断日志\n")
                f.write(f"生成时间: {time.ctime()}\n")
                f.write(f"视频文件: {self.video_path}\n")
                f.write("=" * 50 + "\n")
                f.write(log_content)

            self.add_log(f"💾 日志已保存: {filename}")
        except Exception as e:
            self.add_log(f"❌ 保存日志失败: {e}")

    def manual_close(self):
        """手动关闭"""
        self.add_log("🚪 用户请求关闭")

        runtime = int(time.time() - self.start_time)
        result = messagebox.askyesno("确认关闭",
                                   f"确定要关闭诊断工具吗？\n\n"
                                   f"运行时间: {runtime//60}分{runtime%60}秒\n"
                                   f"诊断帧数: {self.current_frame}\n"
                                   f"错误次数: {self.error_count}")
        if result:
            self.add_log("✅ 用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")

    def on_closing(self):
        """窗口关闭处理"""
        self.add_log("⚠️ 检测到窗口关闭事件")

        result = messagebox.askyesno("确认关闭",
                                   "真的要关闭诊断工具吗？\n\n"
                                   "建议先生成诊断报告")
        if result:
            self.add_log("✅ 用户确认关闭")
            self.cleanup()
            self.root.destroy()
        else:
            self.add_log("❌ 用户取消关闭，继续运行")

    def cleanup(self):
        """清理资源"""
        self.add_log("🧹 开始清理资源...")
        self.running = False
        self.is_playing = False

        if self.cap:
            try:
                self.cap.release()
                self.add_log("✅ 视频资源已释放")
            except:
                self.add_log("⚠️ 视频资源释放失败")

        if self.play_thread and self.play_thread.is_alive():
            self.add_log("⏳ 等待播放线程结束...")
            self.play_thread.join(timeout=3.0)

        self.add_log("✅ 资源清理完成")

    def run(self):
        """运行诊断工具"""
        try:
            print("🔍 启动视频播放结束诊断工具界面...")
            print("📊 专门诊断视频未播放完就自动结束的问题")

            # 处理命令行参数
            if len(sys.argv) > 1:
                video_file = sys.argv[1]
                if os.path.exists(video_file):
                    self.video_path = video_file
                    filename = os.path.basename(video_file)
                    self.video_info_var.set(f"命令行: {filename}")
                    self.add_log(f"📁 命令行参数: {filename}")
                    self.root.after(1000, self.analyze_video_detailed)
                else:
                    self.add_log(f"❌ 命令行文件不存在: {video_file}")

            # 启动主循环
            self.root.mainloop()

            print("🔚 诊断工具正常结束")

        except Exception as e:
            print(f"❌ 诊断工具异常: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 视频播放结束诊断工具")
    print("=" * 60)
    print("问题: 视频未播放完就自动结束并关闭程序")
    print("功能: 详细监控播放过程，记录所有状态变化")
    print("目标: 找出导致提前结束的具体原因")
    print("输出: 详细的诊断报告和错误分析")
    print("=" * 60)

    try:
        diagnostic = VideoEndDiagnostic()
        diagnostic.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        traceback.print_exc()

        try:
            input("按回车键退出...")
        except:
            time.sleep(10)

if __name__ == "__main__":
    main()